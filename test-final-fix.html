<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Animation Cards</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Version finale corrigée - UNIQUEMENT translateY */
        .card-field {
            position: relative;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center top;
            z-index: 1;
            opacity: 1;
            margin-bottom: 20px;
        }

        /* États finaux - SANS margin-top */
        .card-field.active {
            transform: translateY(0px);
            opacity: 1;
            z-index: 10;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border: 2px solid #007bff;
        }

        .card-field.inactive {
            transform: translateY(100px);  /* Décal<PERSON> vers le bas */
            opacity: 0.7;
            z-index: 5;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .card-field.done {
            transform: translateY(-100px) scale(0.95);  /* Monte et rétrécit */
            opacity: 0;
            z-index: 1;
            pointer-events: none;
            box-shadow: none;
        }

        /* Animations */
        .card-field.inactive-to-active {
            animation: slideUpToActive 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .card-field.active-to-done {
            animation: slideUpAndFade 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes slideUpToActive {
            0% {
                transform: translateY(100px);
                opacity: 0.7;
                z-index: 5;
            }
            100% {
                transform: translateY(0px);
                opacity: 1;
                z-index: 10;
            }
        }

        @keyframes slideUpAndFade {
            0% {
                transform: translateY(0px) scale(1);
                opacity: 1;
                z-index: 10;
            }
            100% {
                transform: translateY(-100px) scale(0.95);
                opacity: 0;
                z-index: 1;
            }
        }

        /* Container */
        .cards-container {
            position: relative;
            min-height: 500px;
            overflow: visible; /* Changé pour voir les animations */
            border: 2px dashed #ddd;
            padding: 20px;
        }

        /* Styles visuels */
        .card-field .card-header {
            transition: all 0.3s ease;
        }

        .card-field.active .card-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .card-field.inactive .card-header {
            background: #f8f9fa;
            color: #6c757d;
        }

        .card-field.done .card-header {
            background: #28a745;
            color: white;
        }

        /* Debug styles */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            min-width: 200px;
        }

        .debug-card {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }

        .debug-card.active { background: #007bff; }
        .debug-card.inactive { background: #6c757d; }
        .debug-card.done { background: #28a745; }
        .debug-card.hidden { background: #dc3545; }

        /* Indicateur visuel de position */
        .position-indicator {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .card-field.active .position-indicator { background: #007bff; }
        .card-field.inactive .position-indicator { background: #6c757d; }
        .card-field.done .position-indicator { background: #28a745; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                
                <h2 class="text-center mb-4">Test Final - Animation Cards</h2>
                <p class="text-center text-muted mb-4">
                    Version corrigée utilisant uniquement <code>translateY()</code> sans <code>margin-top</code>
                </p>
                
                <!-- Debug info -->
                <div class="debug-info">
                    <div><strong>Debug Info</strong></div>
                    <div>Étape: <span id="current-step">0</span>/<span id="total-steps">4</span></div>
                    <div>Animation: <span id="is-animating">Non</span></div>
                    <hr style="margin: 10px 0; border-color: #555;">
                    <div id="cards-status"></div>
                </div>
                
                <!-- Contrôles -->
                <div class="text-center mb-4">
                    <button class="btn btn-outline-secondary btn-prev-field me-2" id="btn-prev">← Précédent</button>
                    <button class="btn btn-primary btn-next-field me-2" id="btn-next">Suivant →</button>
                    <button class="btn btn-outline-info" onclick="resetAnimation()">🔄 Reset</button>
                </div>
                
                <!-- Container des cards -->
                <div class="cards-container">
                    
                    <!-- Card 1 -->
                    <div class="card card-field" data-field-id="1">
                        <div class="position-indicator">1</div>
                        <div class="card-header">
                            <h5 class="mb-0">Étape 1 : Informations personnelles</h5>
                        </div>
                        <div class="card-body">
                            <p>Cette carte devrait être visible en premier (position normale).</p>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Nom" value="Test 1">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Card 2 -->
                    <div class="card card-field" data-field-id="2">
                        <div class="position-indicator">2</div>
                        <div class="card-header">
                            <h5 class="mb-0">Étape 2 : Informations professionnelles</h5>
                        </div>
                        <div class="card-body">
                            <p>Cette carte devrait être 100px plus bas (translateY: 100px).</p>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Entreprise" value="Test 2">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Card 3 -->
                    <div class="card card-field" data-field-id="3">
                        <div class="position-indicator">3</div>
                        <div class="card-header">
                            <h5 class="mb-0">Étape 3 : Adresse</h5>
                        </div>
                        <div class="card-body">
                            <p>Cette carte est cachée au début.</p>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Adresse" value="Test 3">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Card 4 -->
                    <div class="card card-field" data-field-id="4">
                        <div class="position-indicator">4</div>
                        <div class="card-header">
                            <h5 class="mb-0">Étape 4 : Confirmation</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6>Félicitations !</h6>
                                <p class="mb-0">Animation terminée avec succès !</p>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6>Comportement attendu :</h6>
                    <ul class="mb-0 small">
                        <li><strong>Card active</strong> : Position normale (translateY: 0)</li>
                        <li><strong>Card inactive</strong> : 100px plus bas (translateY: 100px)</li>
                        <li><strong>Card done</strong> : Monte et disparaît (translateY: -100px, opacity: 0)</li>
                        <li><strong>Transition</strong> : La card inactive monte pour prendre la place de l'active</li>
                    </ul>
                </div>
                
            </div>
        </div>
    </div>

    <script>
        class FinalCardManager {
            constructor() {
                this.cards = Array.from(document.querySelectorAll('.card-field'));
                this.currentStep = 0;
                this.isAnimating = false;
                this.init();
            }
            
            init() {
                this.setupInitialState();
                this.bindEvents();
                this.updateDebugInfo();
            }
            
            setupInitialState() {
                this.cards.forEach((card, index) => {
                    // Nettoyer toutes les classes
                    card.classList.remove('active', 'inactive', 'done', 'inactive-to-active', 'active-to-done');
                    
                    if (index === 0) {
                        card.classList.add('active');
                        card.style.display = 'block';
                    } else if (index === 1) {
                        card.classList.add('inactive');
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
                
                this.currentStep = 0;
                this.updateDebugInfo();
            }
            
            bindEvents() {
                document.getElementById('btn-next').addEventListener('click', () => this.nextStep());
                document.getElementById('btn-prev').addEventListener('click', () => this.prevStep());
            }
            
            nextStep() {
                if (this.isAnimating || this.currentStep >= this.cards.length - 1) {
                    console.log('Cannot go next:', { isAnimating: this.isAnimating, currentStep: this.currentStep, maxStep: this.cards.length - 1 });
                    return;
                }
                
                console.log('Starting nextStep from', this.currentStep, 'to', this.currentStep + 1);
                
                this.isAnimating = true;
                this.updateDebugInfo();
                
                const currentCard = this.cards[this.currentStep];
                const nextCard = this.cards[this.currentStep + 1];
                const futureCard = this.cards[this.currentStep + 2];
                
                console.log('Cards:', { 
                    current: currentCard?.dataset.fieldId, 
                    next: nextCard?.dataset.fieldId, 
                    future: futureCard?.dataset.fieldId 
                });
                
                // Animation de la card actuelle vers "done"
                currentCard.classList.remove('active');
                currentCard.classList.add('active-to-done');
                
                // Animation de la card suivante vers "active"
                if (nextCard) {
                    nextCard.classList.remove('inactive');
                    nextCard.classList.add('inactive-to-active');
                }
                
                // Préparer la card future comme "inactive"
                if (futureCard) {
                    futureCard.style.display = 'block';
                    futureCard.classList.add('inactive');
                }
                
                // Attendre la fin des animations
                setTimeout(() => {
                    // Finaliser les états
                    currentCard.classList.remove('active-to-done');
                    currentCard.classList.add('done');
                    
                    if (nextCard) {
                        nextCard.classList.remove('inactive-to-active');
                        nextCard.classList.add('active');
                    }
                    
                    this.currentStep++;
                    this.isAnimating = false;
                    this.updateDebugInfo();
                    
                    console.log('NextStep completed. New step:', this.currentStep);
                    
                }, 600);
            }
            
            prevStep() {
                if (this.isAnimating || this.currentStep <= 0) return;
                
                this.isAnimating = true;
                this.updateDebugInfo();
                
                const currentCard = this.cards[this.currentStep];
                const prevCard = this.cards[this.currentStep - 1];
                const futureCard = this.cards[this.currentStep + 1];
                
                currentCard.classList.remove('active');
                currentCard.classList.add('inactive');
                
                if (prevCard) {
                    prevCard.classList.remove('done');
                    prevCard.classList.add('active');
                }
                
                if (futureCard) {
                    futureCard.classList.remove('inactive');
                    futureCard.style.display = 'none';
                }
                
                setTimeout(() => {
                    this.currentStep--;
                    this.isAnimating = false;
                    this.updateDebugInfo();
                }, 300);
            }
            
            updateDebugInfo() {
                document.getElementById('current-step').textContent = this.currentStep;
                document.getElementById('total-steps').textContent = this.cards.length;
                document.getElementById('is-animating').textContent = this.isAnimating ? 'Oui' : 'Non';
                
                // Status des cards
                const statusContainer = document.getElementById('cards-status');
                statusContainer.innerHTML = '';
                
                this.cards.forEach((card, index) => {
                    const div = document.createElement('div');
                    div.className = 'debug-card';
                    
                    let status = 'hidden';
                    if (card.classList.contains('active')) status = 'active';
                    else if (card.classList.contains('inactive')) status = 'inactive';
                    else if (card.classList.contains('done')) status = 'done';
                    
                    div.classList.add(status);
                    div.textContent = `Card ${index + 1}: ${status}`;
                    statusContainer.appendChild(div);
                });
            }
        }
        
        // Initialisation
        let cardManager;
        document.addEventListener('DOMContentLoaded', function() {
            cardManager = new FinalCardManager();
        });
        
        function resetAnimation() {
            if (cardManager) {
                cardManager.setupInitialState();
            }
        }
    </script>
</body>
</html>
