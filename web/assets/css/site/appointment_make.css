.aside-content {
    align-items: flex-start !important;
}
.consulting-reason.active,
.calendar.active {
    border: 1px solid var(--blue);
}
.consulting-reason-description.truncated {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    cursor: pointer;
}
.consulting-reason-description p:last-child {
    margin-bottom: 0;
}
input[name="consulting_reason_id"],
input[name="calendar_id"],
input[name="date_start"] {
    display: none;
}
#kt_calendar_container {
    position: relative;
}
#kt_calendar_no_events {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    text-align: center;
}
.wizard-step-content-border {
    border: 4px solid rgba(42, 37, 54, 0.07);
    box-sizing: border-box;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
