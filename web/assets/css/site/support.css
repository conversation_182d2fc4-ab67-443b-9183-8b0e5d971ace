#ticket{
	margin-top: 50px;
}
#ticket .section_header {
	margin: 0px 50px 30px 0px;
	float: left;
}
#ticket hr.right {
	border-bottom: 2px solid #ffffff;
}

#ticket .box{
    background-color: white;
	border: 1px solid #CCC;
	border-radius: 4px 4px 4px 4px;
	float: none;
	margin: 0px auto;
	width: 100%;
	margin-bottom: 25px;
	box-shadow: 0px 0px 10px 0px #DDD;
}
#ticket .box .head{
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    margin-bottom: 37px;
    padding: 25px 0 25px;
    text-align: center;
    width: 100%;
}
#ticket .box .head h4{
	font-weight: normal;
	color: #373D44;
	font-size: 21px;
	margin: 0px;
}
#ticket .box .division{
	display: inline-block;
    margin: 17px 0 23px;
    position: relative;
    text-align: center;
    width: 100%;
}
#ticket .box .division hr{
	border-color: #E2E2E4;
    border-width: 1px;
    margin: 0;
    position: absolute;
    width: 40%;
}
#ticket .box .division hr.left{
	top: 13px;
}
#ticket .box .division hr.right{
	bottom: 6px;
    right: 0;
}
#ticket .box .division span{
	color: #666666;
	font-size: 18px;
}

#ticket form{
	margin: 0px;
}
#ticket form .form-actions {
	margin-bottom: 0px;
}


/* Affichage d'un ticket */
#ticket .box .head {
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    padding: 25px 0 25px;
    width: 100%;
    margin-bottom: 10px;
}
#ticket .box .head h4 {
	font-weight: normal;
	color: #373D44;
	font-size: 21px;
	margin: 0px;
	text-align: left;
	padding-left: 20px
}
#ticket .box .head h4 i {
	padding-right:10px;
}
#ticket .box .box-content {
	padding:10px
}
#ticket .box .box-content form {
	padding: 20px;
}


/* Formulaire d'ouverture d'un ticket */
#ticket .box .form{
	margin: 0 auto;
	width: 90%;
}
#ticket .box .form .input-prepend {
	background: #fff;
	text-align: center;
	height: 32px;
	padding: 0px;
	margin-bottom: 0px;
	margin-left: 20px;
	box-sizing: border-box;
}
#ticket .box .form .add-on {
	background: #fff;
	padding: 5px;
}
#ticket .box .form .add-on i{
	opacity: .5;
}
#ticket .box .form label{
	width:160px;
}

#ticket .box .form input[type="text"], 
#ticket .box .form input[type="email"], 
#ticket .box .form input[type="password"] {
	border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
    border-left: 1px solid #DBDBDB;
    border-style: solid;
    border-width: 1px;
    font-size: 14px;
    padding:5px;
    padding-left: 10px;
    min-width: 0px;
    box-sizing: border-box;
    max-width: 250px;
    box-sizing: border-box;
}
#ticket .box .form button[type="submit"]{
    margin-top: 20px;
}


#ticket .box .form-accueil {
	text-align: center;
}
#ticket .box .form-accueil .input-prepend {
	width:100%;
	height: 32px;
	margin-left: 0px;
	margin-bottom: 10px;
	padding: 10px 0px;
	box-sizing: border-box;
}
#ticket .box .form-accueil .add-on {
	background: #fff;
}

.ticket-content {
	display: block;
	padding: 10px;
	margin: 0px;
	font-size: 14px;
	line-height: 18px;
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, 0.15);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	color: #333333;
	margin-bottom: 20px;
}

.ticket-content.alert {
	background-color: #fffee1 !important;
	color: black !important;
	border: 1px solid #f5c056 !important;
}

#ticket .box .my-tickets {
    text-align: left;
    width: 90%;
    margin: 0 auto
}
.my-ticket {
	display: block;
	padding: 10px;
	margin: 0px;
	font-size: 14px;
	line-height: 18px;
	border-bottom: 1px solid #ccc;
	color: #333333;
	margin-bottom: 20px;
}

#alert-close {
	width: 83%;
	margin: 0 auto;
	margin-bottom: 10px;
}

#form-reponse {
	padding:0px !important;
}

#form-reponse form {
	width:auto !important;
}

#form-reponse .control-group {
	margin: 0 auto !important;
}
#form-reponse form > fieldset > .controls {
	width: 83% !important;
	margin: 0 auto !important;
}

#form-reponse label {
	width: auto !important;
}

#form-reponse .form-actions {
	padding: 0px 20px 20px;
	margin-top: 20px;
	margin-bottom: 0px;
	background-color: #f5f5f5;
	border-top: 1px solid #e5e5e5;
}


/* Portrait tablet to landscape and desktop */
@media (min-width: 768px) and (max-width: 979px) { 
	#ticket .box .division{
	}
	#ticket .box .division hr{
	}
	#ticket .box .division hr.left{
	}
	#ticket .box .division hr.right{
	}
}
@media (max-width: 979px) {

}

@media (min-width: 980px) {
}

@media (min-width: 480px) and (max-width: 768px) { 
	#ticket .box .form input[type="text"], 
	#ticket .box .form input[type="email"], 
	#ticket .box .form input[type="password"] {
		border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
	    border-left: 1px solid #DBDBDB;
	    border-style: solid;
	    border-width: 1px;
	    font-size: 16px;
	    height: 42px;
	    padding:5px;
	    width: 82%;
	    padding-left: 10px
	}
}

@media (max-width: 480px) {
	#ticket .box .form input[type="text"], 
	#ticket .box .form input[type="email"], 
	#ticket .box .form input[type="password"] {
		border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
	    border-left: 1px solid #DBDBDB;
	    border-style: solid;
	    border-width: 1px;
	    font-size: 16px;
	    height: 42px;
	    padding:5px;
	    width: 82%;
	    padding-left: 10px
	}

}
/* Large desktop */
@media (min-width: 1200px) {
}

.chosen-container-single .chosen-single {
    height: 26px;
}

#ticket .btn-default {
	color: #333 !important;
	background-color: #fff !important;
}