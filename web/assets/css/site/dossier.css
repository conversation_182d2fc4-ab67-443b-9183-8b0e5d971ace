.w-nav {
    z-index: 1;
}
.bg-accent-3 {
    background: #fcfcfc !important;
}
.header-logo-link {
    touch-action: none;
    pointer-events: none;
}
.header-middle, .header-right-side {
    display: none;
}
.form .card-header {
    display: flex!important;
}
.form-group label {
    font-weight: 500;
}
.form-group label.radio,
.form-group label.checkbox {
    font-weight: 400;
}
.card.card-custom > .card-header .card-title .card-label,
.card.card-custom > .card-header h3.card-title {
    font-weight: 600;
    color: var(--blue);
}

.card-document {
    border: 1px solid var(--blue) !important;
}
.card.card-custom.card-document > .card-header {
    background: transparent !important;
    border-bottom: 1px solid #EBEDF3 !important;
}
.card.card-custom.card-document > .card-header .card-title .card-label,
.card.card-custom.card-document > .card-header h3.card-title {
    overflow: visible;
    text-overflow: initial;
    white-space: initial;
}
.card-document .card-bg-icon {
    background: white !important;
}
.card-document .svg-icon.svg-icon-white svg g [fill] {
    fill: var(--blue) !important;
}
.card-document.card-link {
    cursor: pointer;
}

.table-amounts {
    background: white !important;
}
.footer-quote-signature {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: #F3F6F9;
    box-shadow: 0 -10px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}
.footer-middle {
    display: none !important;
}

@media screen and (max-width: 767px) {
    .card-toolbar .btn-primary {
        padding: 0.65rem 1rem;
    }
}

.accordion.accordion-svg-toggle .card .card-header .card-title .card-label .svg-icon svg {
    transform: rotate(0deg) !important;
}

.accordion.accordion-toggle-arrow .card .card-header .card-title {
    color: var(--blue);
}


.modal-form .card .card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2.25rem;
}
.modal-form .card .card-header .card-title {
    margin: 0;
}
.modal-form .card .card-header .card-title h3 {
    font-weight: 500;
    font-size: 1.275rem;
    color: #181C32;
    margin-bottom: 0;
}