.card-blank-state {

}
.blank-state {
    display: flex;
    flex-direction: row;
    gap: 24px;
}
.blank-state-icon {
    background: var(--Or-100, #F1E7CD);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
}
.blank-state-icon i {
    font-size: 24px;
    color: var(--astraeos-400);
}
.blank-state .blank-state-image {
    margin: 0;
}
.blank-state .blank-state-image img {
    max-height: 150px;
    max-width: 100%; /*for mobile*/
}
.blank-state .blank-state-icon {
    margin: 0;
}
.blank-state .blank-state-icon iconify-icon {
    font-size: 20px;
}
.blank-state h4 {
    font-size: 20px;
    color: var(--gray-900, #212529);
    font-weight: 500;
    margin-bottom: 10px;
}
.blank-state p {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: var(--gray-800, #343A40);
}
.blank-state .stretch {
    margin: 0 auto 30px auto;
    max-width: 450px;
}
.blank-state .stretch small {
    font-size: 13px;
}
.blank-state .links {
    text-align: center;
}

.blank-state form .card {
    box-shadow: none;
    background: transparent;
}
.blank-state form .card .card-header {
    border: none;
}
.blank-state form .card .card-header .card-toolbar {
    margin: 0 auto;
}
.blank-state form .card .card-body {
    display: none;
}

.card-body .card-blank-state {
    box-shadow: none;
}

.buttons {
    display: flex;
    flex-direction: row;
    gap: 12px;
}
