.payment.braintree .form-group.cardNumber .controls {
    position: relative;
}
.payment.braintree .hosted-field {
    height: 45px;
    background-color: #F3F6F9;
    padding: 0.825rem 1.42rem;
}
.payment.braintree .brand-icon {
    position: absolute;
    top: 13px;
    right: 13px;
}

.payment.braintree .form-group.has-warning .form-control {
    color: #F64E60;
    border: 1px solid #F64E60;
}
.payment.braintree .form-group.has-warning label {
    color: #F64E60;
}

.payment.braintree #paypal-infos {
    display: none;
    overflow: hidden;
    padding: 15px;
    background-image: url('https://checkout.paypal.com/pwpp/2.15.7/images/paypal-small.svg'), none;
    background-position: 20px 50%;
    background-repeat: no-repeat;
    background-size: 13px 15px;
    border-top: 1px solid rgb(209, 212, 214);
    border-bottom: 1px solid rgb(209, 212, 214);
}
.payment.braintree #paypal-infos .paypal-name {
    color: #212B36;
    font-size: 13px;
    font-weight: 800;
    margin-left: 36px;
}
.payment.braintree .paypal-customer {
    color: rgb(110, 120, 127);
    font-size: 13px;
    margin-left: 5px;
}
.payment.braintree .paypal-cancel {
    color: rgb(61, 149, 206);
    font-size: 11px;
    line-height: 20px;
    margin: 0px 0px 0px 25px;
    padding: 0px;
    background-color: transparent;
    border: 0px;
    cursor: pointer;
    text-decoration: underline;
    float: right;
}