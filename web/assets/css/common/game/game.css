.card-game-avatar {
    margin-top: 150px;
}
.card-game-avatar .card-header {
    min-height: 0 !important;
    height: 0;
}
.user-avatar {
    position: relative;
}
.user-avatar.user-avatar-over-card {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) translateY(50px);
}
.user-avatar-holder {
    background: #FFFFFF;
    width: 475px;
    height: 475px;
    display: flex;
    align-items: center;
    justify-content: center;
    clip-path: polygon(
            47.5% 5.66987%,
            48.2899% 5.30154%,
            49.13176% 5.07596%,
            50% 5%,
            50.86824% 5.07596%,
            51.7101% 5.30154%,
            52.5% 5.66987%,
            87.14102% 25.66987%,
            87.85495% 26.16978%,
            88.47124% 26.78606%,
            88.97114% 27.5%,
            89.33948% 28.2899%,
            89.56505% 29.13176%,
            89.64102% 30%,
            89.64102% 70%,
            89.56505% 70.86824%,
            89.33948% 71.7101%,
            88.97114% 72.5%,
            88.47124% 73.21394%,
            87.85495% 73.83022%,
            87.14102% 74.33013%,
            52.5% 94.33013%,
            51.7101% 94.69846%,
            50.86824% 94.92404%,
            50% 95%,
            49.13176% 94.92404%,
            48.2899% 94.69846%,
            47.5% 94.33013%,
            12.85898% 74.33013%,
            12.14505% 73.83022%,
            11.52876% 73.21394%,
            11.02886% 72.5%,
            10.66052% 71.7101%,
            10.43495% 70.86824%,
            10.35898% 70%,
            10.35898% 30%,
            10.43495% 29.13176%,
            10.66052% 28.2899%,
            11.02886% 27.5%,
            11.52876% 26.78606%,
            12.14505% 26.16978%,
            12.85898% 25.66987%
    );
    position: relative;
    overflow: hidden;
}
.profile-image-outer {
    background: #FFFFFF;
    width: 380px;
    height: 380px;
    clip-path: polygon(
            47.5% 5.66987%,
            48.2899% 5.30154%,
            49.13176% 5.07596%,
            50% 5%,
            50.86824% 5.07596%,
            51.7101% 5.30154%,
            52.5% 5.66987%,
            87.14102% 25.66987%,
            87.85495% 26.16978%,
            88.47124% 26.78606%,
            88.97114% 27.5%,
            89.33948% 28.2899%,
            89.56505% 29.13176%,
            89.64102% 30%,
            89.64102% 70%,
            89.56505% 70.86824%,
            89.33948% 71.7101%,
            88.97114% 72.5%,
            88.47124% 73.21394%,
            87.85495% 73.83022%,
            87.14102% 74.33013%,
            52.5% 94.33013%,
            51.7101% 94.69846%,
            50.86824% 94.92404%,
            50% 95%,
            49.13176% 94.92404%,
            48.2899% 94.69846%,
            47.5% 94.33013%,
            12.85898% 74.33013%,
            12.14505% 73.83022%,
            11.52876% 73.21394%,
            11.02886% 72.5%,
            10.66052% 71.7101%,
            10.43495% 70.86824%,
            10.35898% 70%,
            10.35898% 30%,
            10.43495% 29.13176%,
            10.66052% 28.2899%,
            11.02886% 27.5%,
            11.52876% 26.78606%,
            12.14505% 26.16978%,
            12.85898% 25.66987%
    );
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}
.profile-image {
    background: #FFFFFF;
    width: 360px;
    height: 360px;
    clip-path: polygon(
            47.5% 5.66987%,
            48.2899% 5.30154%,
            49.13176% 5.07596%,
            50% 5%,
            50.86824% 5.07596%,
            51.7101% 5.30154%,
            52.5% 5.66987%,
            87.14102% 25.66987%,
            87.85495% 26.16978%,
            88.47124% 26.78606%,
            88.97114% 27.5%,
            89.33948% 28.2899%,
            89.56505% 29.13176%,
            89.64102% 30%,
            89.64102% 70%,
            89.56505% 70.86824%,
            89.33948% 71.7101%,
            88.97114% 72.5%,
            88.47124% 73.21394%,
            87.85495% 73.83022%,
            87.14102% 74.33013%,
            52.5% 94.33013%,
            51.7101% 94.69846%,
            50.86824% 94.92404%,
            50% 95%,
            49.13176% 94.92404%,
            48.2899% 94.69846%,
            47.5% 94.33013%,
            12.85898% 74.33013%,
            12.14505% 73.83022%,
            11.52876% 73.21394%,
            11.02886% 72.5%,
            10.66052% 71.7101%,
            10.43495% 70.86824%,
            10.35898% 70%,
            10.35898% 30%,
            10.43495% 29.13176%,
            10.66052% 28.2899%,
            11.02886% 27.5%,
            11.52876% 26.78606%,
            12.14505% 26.16978%,
            12.85898% 25.66987%
    );
    position: relative;
    overflow: hidden;
}
.profile-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.3);
    text-align: center;
    color: white;
    justify-content: center;
    align-items: center;
    display: none;
}
.profile-image:hover .profile-image-overlay {
    display: flex;
}
.user-avatar.big .user-avatar-holder {
    width: 185px;
    height: 185px;
}
.user-avatar.big .profile-image-outer {
    width: 130px;
    height: 130px;
}
.user-avatar.big .profile-image {
    width: 124px;
    height: 124px;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.user-avatar-holder .progress-bar {
    position: absolute;
    top: 50%;
    background: transparent;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    margin-top: -4px;
}
.user-avatar-holder .progress {
    width: 100%;
    height: 100%;
    background: transparent;
}
.user-avatar-holder .progress .track,
.user-avatar-holder .progress .fill {
    fill: rgba(0, 0, 0, 0);
    stroke-width: 40;
    transform: translate(75px, 685px) rotate(-90deg);
    stroke-linecap: round;
    stroke-linejoin: round;
}
.user-avatar-holder .progress .track {
    stroke: #e7e8ee;
}
.user-avatar-holder .progress .fill {
    stroke-dasharray: 2160;
    stroke-dashoffset: 2160;
    transition: stroke-dashoffset 1s;
}
.user-avatar.big .progress-bar {
    width: 140px;
    height: 140px;
    margin-top: -1px;
}

.user-avatar.big .user-avatar-holder.number {
    width: 45px;
    height: 45px;
    position: absolute;
    right: 25px;
    bottom: 30px;
    background: white;
}
.user-avatar.big .user-avatar-holder.number .profile-image-outer {
    width: 35px;
    height: 35px;
    background: #45437f;
    color: white;
    line-height: 30px;
    text-align: center;
    font-weight: bold;
}

.card-quest .card-quest-rewards,
.card-item .card-quest-rewards {
    position: absolute;
    top: 15px;
    right: -6px;
}
.reward-sticker {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    height: 32px;
    padding: 0 14px;
    border-radius: 200px;
    background-color: #ffffff;
    box-shadow: 3px 5px 20px 0 rgba(94, 92, 154, .12);
    font-size: 1rem;
    font-weight: 700;
    line-height: 32px;
    margin-bottom: 10px;
}
.reward-sticker i {
    font-size: 0.875rem;
}
.reward-sticker .reward-sticker-image {
    margin-left: 4px;
    width: auto;
    height: 14px;
}
.card-quest .symbol,
.card-item .symbol {
    box-shadow: 3px 5px 20px 0 rgba(94,92,154, .12);
}
.card-quest .card-footer,
.card-item .card-footer {
    background: #fcfcfd !important;
    text-align: center;
}
.card-item .description p:last-child,
.card-quest .description p:last-child {
    margin-bottom: 0;
}
.card-item .conditions {
    text-align: center;
    margin-top: 0.5rem;
}
.card-quest .conditions {
    margin-top: 0.5rem;
}

.fade-scale {
    transform: scale(0);
    opacity: 0;
    transition: all 10s linear;
}
.fade-scale.show {
    opacity: 1;
    transform: scale(1);
}
#ModalReward .modal-dialog {
    min-height: 100%;
    margin: 0 auto;
}
#ModalReward .modal-content {
    background: black;
}
#ModalReward .modal-body {
    text-align: center;
    display: flex;
    flex-direction: row;
}
#ModalReward .lottie {
    height: 350px;
}
#ModalReward .lottie-progress {
    height: 65px;
}
#ModalReward .lottie lottie-player {
    margin: 0 auto;
}
#ModalReward .modal-reward-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: center;
    align-items: center;
}
#ModalReward h4 {
    font-size: 36px;
    text-align: center;
    line-height: 44px;
    color: white;
    margin-bottom: 20px;
    font-weight: normal;
}
#ModalReward .reward-info p {
    font-size: 14px;
    text-align: center;
    color: #caced3;
    margin-bottom: 20px;
}
#ModalReward .rewards {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-bottom: 30px;
}
#ModalReward .rewards .reward-sticker {
    margin: 0 0.5rem;
}
#ModalReward .btn-info {
    background: #1D93E6;
    box-shadow: none;
    border: none;
    font-size: 14px;
    padding: 15px 38px;
}
#ModalReward .btn-info:hover {
    background: #2088d2;
}

.rank-icon {
    position: relative;
}
.rank-icon svg {
    height: 6rem !important;
    width: 6rem !important;
}
.rank-icon .rank-number {
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    font-weight: bold;
    font-size: 1.75rem;
}
