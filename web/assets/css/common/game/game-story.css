#storyModal .modal-dialog {
    padding: 0;
    margin: 0 auto;
    min-height: 100%;
}
#storyModal .modal-content {
    background: transparent;
    box-shadow: none;
}
#storyModal .modal-body {
    margin: 0 auto;
    padding: 0;
}
#storyModal .new-message {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    border-radius: 40px;
    overflow: hidden;
    background-image: url("../../../images/bg/bg-10.jpg");
    padding: 20px;
    position: relative;
}
#storyModal .new-message-overlay {
    background: rgba(0,0,0,.3);
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 40px;
    overflow: hidden;
}
#storyModal .new-message-container {
    background: rgba(205, 210, 214, .90);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 20px;
}
#storyModal .new-message-container .new-message-header {
    background: #E4E6EF;
    padding: 10px 15px;
}
#storyModal .new-message-container .new-message-header h6 {
    margin-bottom: 0;
}
#storyModal .new-message-container .new-message-body {
    display: flex;
    flex-direction: row;
    padding: 10px 15px;
}
#storyModal .new-message-container .new-message-content p {
    margin-bottom: 0;
}
#storyModal .btn-link {
    color: white;
    margin-top: 20px;
}

#storyModal .story-container {
    height: 100%;
    background: white;
    padding: 0;
    border-radius: 40px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}
#storyModal .story-header {
    background: rgba(205, 210, 214, 1);
    padding-top: 33px;
    flex: 0 0 60px;
    height: 60px;
    text-align: center;
}
#storyModal .story-header h6 {
    margin-bottom: 0;
}
#storyModal .story-footer {
    flex: 0 0 60px;
    height: 60px;
    padding: 10px 40px;
    background: #E4E6EF;
}
#storyModal .story-messages {
    height: calc(100% - 120px);
    overflow-y: auto;
    padding: 15px;
}
#storyModal .story-messages .message {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}
#storyModal .story-messages .message.me {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
}
#storyModal .story-messages .message .symbol {
    margin-right: 0.75rem;
}
#storyModal .story-messages .message.me .symbol {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    margin-left: 0.75rem;
    margin-right: 0;
}
#storyModal .story-messages .message.me .name {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
}
#storyModal .story-messages .message-content {
    max-width: 300px;
    font-weight: 500;
    text-align: left;
    margin-top: 0.5rem;
}
#storyModal .story-messages .message-content .text {
    font-size: 1.08rem;
    color: #7E8299;
    background-color: #C9F7F5;
    padding: 1.25rem;
    border-radius: 0.42rem;
}
#storyModal .story-messages .message.me .message-content .text {
    background-color: #E1E9FF;
    text-align: right;
}
#storyModal .story-messages .message .message-content .image,
#storyModal .story-messages .message .message-content .video {
    cursor: pointer;
    border-radius: 0.42rem;
    max-width: 200px;
    overflow: hidden;

    position: relative;
}
#storyModal .story-messages .message .message-content .image img,
#storyModal .story-messages .message .message-content .video img {
    max-width: 200px;
    opacity: .8;
}
#storyModal .story-messages .message .message-content .image img:hover,
#storyModal .story-messages .message .message-content .video img:hover {
    opacity: 1;
}
#storyModal .story-messages .message .message-content .video:before {
    content: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iNDhweCIgaGVpZ2h0PSI0OHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSI+CiAgICA8ZyBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iNDgiIGhlaWdodD0iNDgiLz4KICAgICAgICA8cGF0aCBkPSJNNC4yMjI2Njg4Miw0IEwxOS44MzY3NzI4LDQuMDAwMDEzNTMgQzIxLjM4NzMxODUsNC4wMDAwMTM1MyAyMi42ODIzODk3LDUuMTgxNjAwOSAyMi44MjQxODgxLDYuNzI1NjQ5MjUgQzIyLjk0MTQwMjEsOC4wMDE5OTY1MyAyMy4wMDAwMDkxLDkuNDAxMTM5MDkgMjMuMDAwMDA5MSwxMC45MjMwNzY5IEMyMy4wMDAwMDkxLDEyLjcwNDk1OTkgMjIuOTE5NjcyNCwxNC40ODcwNTQyIDIyLjc1ODk5OSwxNi4yNjkzNiBMMjIuNzU4OTk0MywxNi4yNjkzNTk1IEMyMi42MTk2MDUzLDE3LjgxNTU2MzcgMjEuMzIzNTg5OSwxOSAxOS43NzExMTU1LDE5IEw0LjIyMjY3MDkxLDE5LjAwMDAwMjIgQzIuNjc0MzUyNSwxOS4wMDAwMDIyIDEuMzgwMzcwMzIsMTcuODIxNzEwOSAxLjIzNTc3ODgyLDE2LjI4MDE1ODcgQzEuMDc4NTkyOTQsMTQuNjA0MzMyMyAxLDEzLjAxMDk0NjEgMSwxMS41IEMxLDkuOTg5MDUzNTkgMS4wNzg1OTI5OCw4LjM5NTY2Njk5IDEuMjM1Nzc4OTMsNi43MTk4NDAyIEwxLjIzNTc4MDIyLDYuNzE5ODQwMzIgQzEuMzgwMzcxNTcsNS4xNzgyODk5NCAyLjY3NDM1MjI0LDQgNC4yMjI2Njg4Miw0IFoiIGZpbGw9IiMwMDAwMDAiIG9wYWNpdHk9IjAuMyIvPgogICAgICAgIDxwYXRoIGQ9Ik0xMS4xODIxNTc2LDE0LjgwNTI5MzQgTDE1LjU4NTYwODQsMTEuNzk1Mjg2OCBDMTUuODEzNTgwMiwxMS42Mzk0NTUyIDE1Ljg3MjA2MTQsMTEuMzI4MzIxMSAxNS43MTYyMjk5LDExLjEwMDM0OTQgQzE1LjY4MTQ1ODMsMTEuMDQ5NDgwOCAxNS42Mzc1ODM4LDExLjAwNTQ3NzUgMTUuNTg2ODE3NCwxMC45NzA1NTcgTDExLjE4MzM2NjYsNy45NDE1NjkyOSBDMTAuOTU1ODUyNyw3Ljc4NTA3MDAxIDEwLjY0NDU0ODUsNy44NDI2Mzg3NSAxMC40ODgwNDkyLDguMDcwMTUyNjggQzEwLjQzMDcwMTgsOC4xNTM1MjI1OCAxMC4zOTk5OTk2LDguMjUyMzMwNDUgMTAuMzk5OTk5Niw4LjM1MzUxOTY5IEwxMC4zOTk5OTk2LDE0LjM5MjUxNCBDMTAuMzk5OTk5NiwxNC42Njg2NTY0IDEwLjYyMzg1NzIsMTQuODkyNTE0IDEwLjg5OTk5OTYsMTQuODkyNTE0IEMxMS4wMDA2ODksMTQuODkyNTE0IDExLjA5OTAzMjYsMTQuODYyMTE0MSAxMS4xODIxNTc2LDE0LjgwNTI5MzQgWiIgZmlsbD0iIzAwMDAwMCIvPgogICAgPC9nPgo8L3N2Zz4=);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    z-index: 1;
    height: 48px;
    width: 48px;
}

.dot-flashing {
    position: relative;
    width: 8px;
    height: 8px;
    border-radius: 5px;
    background-color: #9880ff;
    color: #9880ff;
    animation: dotFlashing .7s infinite linear alternate;
    animation-delay: .35s;
    margin: 0 10px;
}
.dot-flashing::before,
.dot-flashing::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 0;
}
.dot-flashing::before {
    left: -12px;
    width: 8px;
    height: 8px;
    border-radius: 5px;
    background-color: #9880ff;
    color: #9880ff;
    animation: dotFlashing .7s infinite alternate;
    animation-delay: 0s;
}
.dot-flashing::after {
    left: 12px;
    width: 8px;
    height: 8px;
    border-radius: 5px;
    background-color: #9880ff;
    color: #9880ff;
    animation: dotFlashing .7s infinite alternate;
    animation-delay: .7s;
}
@keyframes dotFlashing {
    0% {
        background-color: #9880ff;
    }
    50%,
    100% {
        background-color: #c7bafd;
    }
}


.modal-image .modal-content,
.modal-video .modal-content {
    background: transparent;
    box-shadow: none;
}
.modal-image .modal-header,
.modal-video .modal-header {
    border-bottom: none;
    padding-right: 0;
}
.modal-image .modal-header .close .ki,
.modal-video .modal-header .close .ki {
    color: white;
}
.modal-image .modal-body {
    border-radius: 20px;
    overflow: hidden;
    -webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .10);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .10);
}
.modal-image .modal-body img {
    max-width: 100%;
}
