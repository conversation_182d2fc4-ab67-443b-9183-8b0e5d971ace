.login.login-3 .login-content {
    padding-top: 0 !important;
}
.top-signup-container {
    padding-top: 20px;
    background: #F3F5F9;
    z-index: 1;
}
.card-step-header {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--gray-400, #CED4DA);
    position: sticky;
    top: 20px;
    z-index: 1000;
    background: #F3F5F9;
    box-shadow: 0 0 0 4px #F3F5F9;
}
.card-step-header hr {
    display: none;
}
.card-step-header table {
    margin-bottom: 0;
}
.card-step-header .card-step-title {
    flex: 1;
}
.card-step-header .card-step-title h3 {
    margin: 0;
    font-size: 18px;
    color: var(--gray-900, #212529);
    font-weight: 600;
}
.card-step-header .card-step-title p {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: var(--gray-500);
}
.card-step-actions {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-left: auto;
}
.card-step-actions a.btn-icon {
    padding: 0;
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.card-step-actions a.btn-icon.inactive {
    color: var(--astraeos-200, #85899A);
}


.card-field {
    transition: all 0.3s ease;
}
.card-field .card.card-custom {
    border-radius: 6px;
    background: #FFF;
    padding: 24px 20px;
}
.card-field .card.card-custom > .card-header {
    padding: 0;
    flex-wrap: nowrap;
}
.card-field .card.card-custom > .card-body {
    padding: 0;
}
.card-field .card.card-custom .card-body .form-group {
    margin-bottom: 0;
}
.card-field .card.card-custom .card-body .form-group > .control-label {
    display: none;
}
.card-field .card.card-custom .card-footer {
    padding: 24px 0 0 0;
}
.btn-comments {
    border-radius: 4px;
    border: 1px solid var(--gray-400, #CED4DA);
    background: #FFF;
    width: 36px;
    height: 36px;
    padding: 8px;
    font-size: 20px;
    display: flex;
    align-items: center;
    position: relative;
}
.btn-comments .nb-comments {
    position: absolute;
    top: -8px;
    right: -8px;
    padding: 0;
    width: 16px;
    height: 16px;
    line-height: 1;
    border-radius: 50%;
    background: var(--rouge-400, #E95822);
    color: white;
    font-size: 10px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-approve {
    border-radius: 30px;
    background: #9CCF7F;
    color: var(--Astraeos-Astraeos_400, #181C31);
    font-size: 14px;
    font-weight: 600;
}
.btn-unapprove {
    color: #E7A593;
}
.btn-refuse-add-comment {
    border-radius: 30px;
    background: #E7A593;
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.text-approve {
    color: #9CCF7F;
    font-size: 14px;
}
.card-field.inactive .next-field-container .btn-next-field,
div.pb-5 .card-field.active:last-of-type .next-field-container .btn-next-field {
    display: none;
}

.card-field.active .card {
    box-shadow: 0 0 0 4px #E6D7B0;
}
.card-field.inactive {
    border-radius: 6px;
    background: #FFF;
    /* margin-top supprimé - on utilise maintenant translateY */
}
.card-field.inactive .card {
    opacity: .3;
}
/*.card-field.done {
    opacity: 0;
    display: none;
}*/





/* Styles pour les cards avec animations fluides */
.card-field {
    position: relative;
    transition: all 0.6s ease;
    transform-origin: center top;
    z-index: 1;
    opacity: 1;
}

/* Card active - position normale, z-index élevé */
.card-field.active {
    transform: translateY(0px);
    opacity: 1;
    z-index: 10;
}

/* Card inactive - décalée vers le bas */
.card-field.inactive {
    transform: translateY(100px);
    opacity: 0.7;
    z-index: 5;
    margin-top: 0;
}

/* Card done - monte et disparaît */
.card-field.done {
    transform: translateY(-100%);
    opacity: 0;
    z-index: 1;
    pointer-events: none;
}

/* Animation pour le passage de inactive à active */
.card-field.inactive-to-active {
    animation: slideUpToActive 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Animation pour le passage de active à done */
.card-field.active-to-done {
    animation: slideUpAndFade 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Animation pour le passage de inactive à active */
.card-field.active-to-inactive {
    animation: slideDownToInactive 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Animation pour le passage de active à done */
.card-field.done-to-active {
    animation: slideDownToActive 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideUpToActive {
    0% {
        transform: translateY(100px);
        opacity: 0.7;
        z-index: 5;
    }
    100% {
        transform: translateY(0px);
        opacity: 1;
        z-index: 10;
    }
}

@keyframes slideUpAndFade {
    0% {
        transform: translateY(0px);
        opacity: 1;
        z-index: 10;
    }
    100% {
        transform: translateY(-100%);
        opacity: 0;
        z-index: 1;
    }
}

@keyframes slideDownToInactive {
    0% {
        transform: translateY(0px);
        opacity: 1;
        z-index: 10;
    }
    100% {
        transform: translateY(100px);
        opacity: .7;
        z-index: 5;
    }
}

@keyframes slideDownToActive {
    0% {
        transform: translateY(-100%);
        opacity: 0;
        z-index: 1;
    }
    100% {
        transform: translateY(0px);
        opacity: 1;
        z-index: 10;
    }
}

/* Container pour les cards */
.cards-container {
    position: relative;
    min-height: 400px; /* Ajustez selon vos besoins */
    overflow: hidden;
}

/* Amélioration : positionnement absolu pour éviter les conflits */
.card-field {
    position: relative;
    width: 100%;
}

/* États finaux plus clairs */
.card-field.active {
    transform: translateY(0px);
    opacity: 1;
    z-index: 10;
}

.card-field.inactive {
    transform: translateY(100px);
    opacity: 0.7;
    z-index: 5;
}

.card-field.done {
    transform: translateY(-100%);
    opacity: 0;
    z-index: 1;
    pointer-events: none;
}

/* Effet hover pour les cards inactives */
.card-field.inactive:hover {
    transform: translateY(95px);
    opacity: 0.8;
    cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
    .card-field.inactive {
        transform: translateY(60px);
    }
    .card-field.done {
        transform: translateY(-30px) scale(0.98);
    }

    @keyframes slideUpToActive {
        0% {
            transform: translateY(60px);
            opacity: 0.7;
        }
        100% {
            transform: translateY(0px);
            opacity: 1;
        }
    }

    @keyframes slideUpAndFade {
        0% {
            transform: translateY(0px) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(-30px) scale(0.98);
            opacity: 0;
        }
    }
}
