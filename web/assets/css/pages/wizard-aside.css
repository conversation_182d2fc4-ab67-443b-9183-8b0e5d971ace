.login.login-3 .login-aside {
  background: linear-gradient(179deg, #333A56 6.12%, rgba(41, 46, 69, 0.00) 47.51%), #181C31;
  -webkit-box-shadow: 0px 0px 40px rgba(177, 187, 208, 0.15);
  box-shadow: 0px 0px 40px rgba(177, 187, 208, 0.15); }
.login.login-3 .login-aside .wizard-nav {
  padding: 0; }
.login.login-3 .login-aside .wizard-nav .wizard-steps {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step {
  padding: 0.75rem 0;
  -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
  margin-bottom: 1.5rem; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step:last-child {
  margin-bottom: 0; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
  width: 50px;
  height: 50px;
  border-radius: 50px;
  background-color: #F3F6F9;
  margin-right: 1.4rem;
  flex-shrink: 0;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon .wizard-check {
  display: none;
  font-size: 1.4rem; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon .wizard-number {
  font-weight: 600;
  color: #3F4254;
  font-size: 1.35rem; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
  color: #181C32;
  font-weight: 500;
  font-size: 1.4rem; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-desc {
  color: #B5B5C3;
  font-size: 1.08rem;
  font-weight: 500; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon {
  -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
  background-color: #C9F7F5; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon .wizard-check {
  color: #1BC5BD;
  display: inline-block; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon .wizard-number {
  display: none; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-title {
  color: #B5B5C3; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-desc {
  color: #D1D3E0; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
  -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon {
  -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
  background-color: #C9F7F5; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon .wizard-check {
  color: #1BC5BD;
  display: none; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon .wizard-number {
  color: #1BC5BD; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-title {
  color: #181C32; }
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-desc {
  color: #B5B5C3; }
.login.login-3 .login-aside .aside-img-wizard {
  min-height: 320px !important;
  background-size: 400px; }

.login.login-3 .login-content {
  background-color: #F3F5F9; }
.login.login-3 .login-content .form-group .fv-help-block {
  font-size: 1.1rem !important;
  padding-top: 3px; }

@media (min-width: 992px) {
  .login.login-3 .login-aside {
    width: 100%;
    max-width: 600px; }
  .login.login-3 .login-aside .aside-img {
    min-height: 550px !important;
    background-size: 630px; }
  .login.login-3 .login-content .top-signup {
    max-width: 650px;
    width: 100%; }
  .login.login-3 .login-content .top-signin {
    max-width: 450px;
    width: 100%; }
  .login.login-3 .login-content .top-forgot {
    max-width: 450px;
    width: 100%; }
  .login.login-3 .login-content .login-form {
    width: 100%;
    max-width: 450px; }
  .login.login-3 .login-content .login-form.login-form-signup {
    max-width: 650px; } }

@media (min-width: 992px) and (max-width: 1399.98px) {
  .login.login-3 .login-aside {
    width: 100%;
    max-width: 400px; } }

@media (max-width: 991.98px) {
  .login.login-3 .login-aside .aside-img {
    min-height: 500px !important;
    background-size: 500px; }
  .login.login-3 .login-aside .login-logo {
    text-align: center; }
  .login.login-3 .login-aside .wizard-nav {
    padding: 0;
    -ms-flex-line-pack: center;
    align-content: center; }
  .login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step {
    margin-bottom: .5rem; }
  .login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step:last-child {
    margin-bottom: 0; }
  .login.login-3 .login-content .top-signup {
    width: 100%;
    max-width: 550px; }
  .login.login-3 .login-content .top-signin {
    max-width: 550px;
    width: 100%; }
  .login.login-3 .login-content .top-forgot {
    max-width: 550px;
    width: 100%; }
  .login.login-3 .login-content .login-form {
    width: 100%;
    max-width: 550px; } }

@media (max-width: 575.98px) {
  .login.login-3 .login-aside .aside-img {
    min-height: 300px !important;
    background-size: 350px; } }

#kt_form {
  font-size: 1.175rem;
}
.form-control.form-control-solid {
  background-color: white;
  border-color: #E4E6EF;
  color: #3F4254;
  -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
.card .form-control.form-control-solid {
  background-color: #F3F6F9;
  border-color: #E4E6EF;
  color: #3F4254;
}
.card .form-control.form-control-solid + .input-group-append .input-group-text {
  border-top-right-radius: 0.85rem;
  border-bottom-right-radius: 0.85rem;
}
.form-group label {
  font-size: 1.175rem;
  color: #181C32;
  font-weight: 600;
  display: flex;
  align-items: center;
}
.form-group label.checkbox {
  font-weight: normal;
}
.form-group input {
  border: none;
}
.form-group input:focus {
  background-color: #fff;
}
.form-group select {
  height: auto;
  border-radius: 0.85rem;
  padding: 1.75rem 1.5rem;
  font-size: 1.175rem;
  border: none;
}
.select2-container {
  min-width: 100%;
}
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  border: none;
  /*height: 64px;
  border: none;
  border-radius: 0.85rem;
  padding: 1.15rem 0.5rem;
  font-size: 1.175rem;*/
}
/*.select2-container--default .select2-results__option {
  font-size: 1.175rem;
  padding: 1.75rem 1.5rem;
}
.input-group-text {
  font-size: 1.175rem;
  padding: 1rem 1.5rem;
}
.form-group .form-text {
  font-size: 1.05rem;
  font-weight: 400;
}
.input-group-prepend .btn,
.input-group-append .btn {
  padding: 1.75rem 1.5rem;
}*/

#kt_form .col-xxl-7 {
  width: 100%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.input-group.input-group-solid {
  border: 1px solid #E4E6EF;
}
.input-group.input-group-solid .input-group-prepend ~ .form-control {
  padding-left: 1.5rem !important;
  background-color: white;
}

@media (max-width: 991px) {
  .login-aside > div {
    padding-top: 0 !important;
  }
  .wizard-aside-left-content {
    display: none !important;
  }
  .login-content {
    padding: 1rem !important;
  }
  .radio-inline {
    overflow-x: auto;
  }
}

.switch-right .switch-infos label {
  font-size: 1rem;
}

.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step {
    margin-bottom: 1rem;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
    color: var(--Astraeos-Astraeos_200, #85899A);
    font-size: 13px;
    font-weight: 400;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-title {
    color: white;
    opacity: 1;
}

.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon {
    background: transparent;
    border: 2px solid var(--astraeos-200);
    width: 32px;
    height: 32px;
    position: relative;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon:after {
    position: absolute;
    top: 38px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--astraeos-200);
    font-family: Flaticon;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    line-height: 1;
    text-decoration: inherit;
    text-rendering: optimizeLegibility;
    text-transform: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    content: "\f144";
    font-size: 20px;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step:last-of-type .wizard-icon:after {
    display: none;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon .wizard-number {
    color: white;
    font-size: 13px;
    font-weight: 400;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon {

}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon {
    background: white;
    border-color: white;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon .wizard-number {
    color: var(--Astraeos-Astraeos_400, #181C31);
    opacity: 1;
    font-weight: 500;
}

.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon {
    background: transparent;
}
.login.login-3 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon .wizard-check {
    color: white;
    font-size: 12px;
    margin-top: 2px;
}

input.form-control.input-sm {
  width: auto;
}
.input-group.input-group-small {
  width: 200px;
}
.input-group.input-group-small-integer {
  width: 150px;
}
.card input.form-control-solid + .input-group-append .input-group-text {
  border: none;
}

.card select,
.card .select2 {
  border: 1px solid #EBEDF3;
  border-radius: 0.85rem;
}
