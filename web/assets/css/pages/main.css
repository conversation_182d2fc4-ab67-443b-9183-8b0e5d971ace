.header-container {
    display: flex;
    flex: 1 0 auto;
    position: relative;
    flex-direction: column;
}
.header-container-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.header {
    height: 80px;
}
.header img {
    max-height: 40px;
}
.header.medium {
    height: 100px;
}
.header.medium img {
    max-height: 60px;
}
.header.large {
    height: 120px;
}
.header.large img {
    max-height: 80px;
}

.video-responsive {
    overflow:hidden;
    padding-bottom:56.25%;
    position:relative;
    height:0;
}
.video-responsive iframe {
    left:0;
    top:0;
    height:100%;
    width:100%;
    position:absolute;
}
figure.table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.line {
    padding: 0;
    display: block;
    position: relative;
}
.bloc-line-header-bg-image {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-attachment: fixed;
    background-position: center top;
    background-repeat: no-repeat;
    background-color: transparent;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
.line_overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: 0 0;
}

figure.image {
    clear: both;
    display: table;
    margin: 0.9em auto;
    min-width: 50px;
    text-align: center;
}
figure.image-style-side {
    float: right;
    margin-left: 1.5rem;
    max-width: 50%;
}
figure.image-inline {
    align-items: flex-start;
    display: inline-flex;
    max-width: 100%;
}
figcaption {
    caption-side: bottom;
    color: #333;
    display: table-caption;
    font-size: .75em;
    outline-offset: -1px;
    padding: 0.6em;
    word-break: break-word;
}