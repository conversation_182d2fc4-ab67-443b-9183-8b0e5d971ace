.wizard.wizard-4 {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column; }
  .wizard.wizard-4 .wizard-nav .wizard-steps {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap; }
    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(25% - 0.25rem);
      flex: 0 0 calc(25% - 0.25rem);
      width: calc(25% - 0.25rem);
      background-color: #F3F6F9;
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem; }
      .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        color: #3F4254;
        padding: 2rem 2.5rem; }
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-number {
          font-size: 1.3rem;
          font-weight: 600;
          -webkit-box-flex: 0;
          -ms-flex: 0 0 2.75rem;
          flex: 0 0 2.75rem;
          height: 2.75rem;
          width: 2.75rem;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack: center;
          -ms-flex-pack: center;
          justify-content: center;
          background-color: rgba(105, 147, 255, 0.08);
          color: #6993FF;
          margin-right: 1rem;
          border-radius: 0.5rem; }
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label {
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-orient: vertical;
          -webkit-box-direction: normal;
          -ms-flex-direction: column;
          flex-direction: column; }
          .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-title {
            font-size: 1.1rem;
            font-weight: 600; }
      .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
        background-color: #ffffff; }
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-number {
          color: #ffffff;
          background-color: #6993FF; }
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-label .wizard-title {
          color: #6993FF; }
    .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="2"] .wizard-step {
      -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 0.25rem);
      flex: 0 0 calc(50% - 0.25rem);
      width: calc(50% - 0.25rem); }
    .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="3"] .wizard-step {
      -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(33.33333% - 0.25rem);
      flex: 0 0 calc(33.33333% - 0.25rem);
      width: calc(33.33333% - 0.25rem); }
    .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="4"] .wizard-step {
      -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(25% - 0.25rem);
      flex: 0 0 calc(25% - 0.25rem);
      width: calc(25% - 0.25rem); }
    .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="5"] .wizard-step {
      -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(20% - 0.25rem);
      flex: 0 0 calc(20% - 0.25rem);
      width: calc(20% - 0.25rem); }

@media (max-width: 1399.98px) {
  .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(50% - 0.25rem);
    flex: 0 0 calc(50% - 0.25rem);
    width: calc(50% - 0.25rem);
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    margin-bottom: 0.5rem; }
  .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="2"] .wizard-step, .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="4"] .wizard-step {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(50% - 0.25rem);
    flex: 0 0 calc(50% - 0.25rem);
    width: calc(50% - 0.25rem); }
  .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="3"] .wizard-step,
  .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="4"] .wizard-step,
  .wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="5"] .wizard-step {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    width: 100%; } }

@media (max-width: 767.98px) {
  .wizard.wizard-4 .wizard-nav .wizard-steps {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start; }
    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
      -webkit-box-flex: 0 !important;
      -ms-flex: 0 0 100% !important;
      flex: 0 0 100% !important;
      position: relative;
      width: 100% !important; }
      .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        padding: 0.5rem 2rem; } }

.offcanvas {
  width: 700px;
}
.offcanvas.offcanvas-right {
  right: -700px;
}
.offcanvas .wizard-nav {
  display: none;
}
.offcanvas .card.card-custom.card-transparent {
  box-shadow: none;
}

.offcanvas .form-group.row .col-lg-4,
.offcanvas .form-group.row .col-lg-8 {
  width: 100%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.form-panel-container #kt_form > .row > .col-xl-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}


.offcanvas-panel-form {

}
.offcanvas-panel .offcanvas-header {
  height: 50px;
  padding: 0 20px;
  margin-bottom: 10px;
}
.offcanvas-panel .offcanvas-close {
  position: relative !important;
}
.offcanvas-panel .offcanvas-close i {
  color: #323232;
}
.offcanvas-panel .offcanvas-close iconify-icon {
  color: #323232;
  font-size: 24px;
}
.offcanvas-panel .offcanvas-header-actions {
  margin-left: auto;
}
.offcanvas-panel .offcanvas-content {
  height: calc(100vh - 50px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding: 20px;
}
.offcanvas-panel .offcanvas-content hr {
  width: 100%;
}
.offcanvas-panel .offcanvas-content form.form-in-panel {
  height: 100%;
}
.offcanvas-panel .offcanvas-content .card:not(.card-stat) {
  border: 1px solid var(--gray-300);
  border-radius: 6px;
}
.offcanvas-content-container {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}
.offcanvas-content-container.with-footer {
  height: calc(100% - 80px);
}
.offcanvas-content .offcanvas-content-container form {
  height: auto;
}
.offcanvas-footer {
  margin-top: auto;
  padding: 20px;
  background-color: #ffffff;
  border-top: 1px solid #EBEDF3;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  height: 80px;
  margin-left: -20px;
  margin-right: -20px;
}
.offcanvas-header h4 {
  margin: 0;
  color: var(--gray-900, #212529);
  font-size: 20px;
  font-weight: 500;
}
.form-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.form-panel-content {
  overflow-y: auto;
  padding: 5px 0 20px 0; /* add 5px for floating label */
  height: 100%;
}
.form-panel-content > .card:first-child > .card-header {
  display: none !important;
}
.form-panel-footer {
  height: 100px;
  justify-content: flex-end;
  align-items: center;
  display: flex;
  z-index: 1;
  position: relative;
  flex-shrink: 0;
  border-top: 1px solid #EBEDF3;
  margin-top: auto;
  padding: 0 20px;
  margin-left: -20px;
  margin-right: -20px;
}
/*.form-panel-footer button {
  display: block;
  width: 60%;
}*/
