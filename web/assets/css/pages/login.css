@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

:root {
  --blue: #333a56;
  --orange: #d09705;
}
.login.login-1 .login-aside {
  background: var(--blue);
}
.login.login-1 .login-aside .aside-img {
  min-height: 450px; }

.login.login-1 .login-signin,
.login.login-1 .login-signup,
.login.login-1 .login-forgot {
  display: none; }

.login.login-1.login-signin-on .login-signup {
  display: none; }

.login.login-1.login-signin-on .login-signin {
  display: block; }

.login.login-1.login-signin-on .login-forgot {
  display: none; }

.login.login-1.login-signup-on .login-signup {
  display: block; }

.login.login-1.login-signup-on .login-signin {
  display: none; }

.login.login-1.login-signup-on .login-forgot {
  display: none; }

.login.login-1.login-forgot-on .login-signup {
  display: none; }

.login.login-1.login-forgot-on .login-signin {
  display: none; }

.login.login-1.login-forgot-on .login-forgot {
  display: block; }

@media (min-width: 992px) {
  .login.login-1 .login-aside {
    width: 100%;
    max-width: 600px; }
  .login.login-1 .login-content {
    width: 100%;
    max-width: 500px; }
    .login.login-1 .login-content .login-form {
      width: 100%;
      max-width: 450px; } }

@media (min-width: 992px) and (max-width: 1399.98px) {
  .login.login-1 .login-aside {
    width: 100%;
    max-width: 450px; } }

@media (max-width: 991.98px) {
  .login.login-1 .login-content .login-form {
    width: 100%;
    max-width: 400px; } }

@media (max-width: 575.98px) {
  .login.login-1 .aside-img {
    min-height: 300px !important;
    background-size: 400px; }
  .login.login-1 .login-content .login-form {
    width: 100%;
    max-width: 100%; } }

.form-group .toggle-password {
  position: absolute;
  right: 35px;
  top: 0;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
}

.login-aside {
  order: 2;
  border-radius: 10px;
  margin: 16px;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 32px);
  position: sticky;
  top: 16px;
}
.login.login-1 .login-aside {
  background: linear-gradient(0deg, #333A56 0%, #181C31 100%);
}

.login-aside > div {
  padding: 0 !important;
}
.login-aside > div a {
  margin: 0 !important;
}
.login-aside h3 {
  display: none;
}

.login-content h3 {
  color: #121212;
  text-align: center;
  font-family: Inter, Helvetica, "sans-serif";
  font-size: 30px;
  font-weight: 500;
  line-height: 120%; /* 36px */
  margin-bottom: 24px;
}

.forgot-password {
  color: #181C31;
  font-size: 16px;
  font-weight: 600;
}
