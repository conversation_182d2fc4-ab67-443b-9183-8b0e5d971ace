body {
    background: #333a56 !important;
}
#content {
    padding: 0;
}
#main-container {
    background: transparent;
}
#main-container {
    overflow: visible;
}
#content iframe {
    border: none;
}
#content iframe:first-of-type {
    border-right: 3px solid #181C32;
}
#content iframe:last-of-type {
    border-left: 3px solid #181C32;
}
.card-similar-pages {
    border-radius: 0;
    height: 73px;
    z-index: 10;
    white-space: nowrap;
    border: none;
}
.card-similar-pages .card-header {
    border-bottom: none;
    padding: 1rem 1.25rem;
    background: transparent;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav {
    border-bottom: 1px solid #7E8299;
    overflow: hidden;
    overflow-x: auto;
    flex: none;
    flex-wrap: nowrap;
}
.card-similar-pages > .card-header.card-header-tabs-line .nav .nav-link {
    padding-top: 1rem;
    padding-bottom: 1rem;
    cursor: pointer;
}
#content.full-width .card-similar-pages {
    left: 0;
    width: 100%;
}
.card-similar-pages + div {
    margin-top: 73px;
}
