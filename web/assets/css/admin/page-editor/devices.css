/*! Devices.css v0.1.16 | MIT License | github.com/picturepan2/devices.css */
.device,
.device::before,
.device::after,
.device *,
.device *::before,
.device *::after {
  box-sizing: border-box;
  display: block;
}

.device {
  position: relative;
  transform: scale(1);
  z-index: 1;
}

.device .device-frame {
  z-index: 1;
}

.device .device-content {
  background-color: #fff;
  background-position: center center;
  background-size: cover;
  object-fit: cover;
  position: relative;
}

.device-iphone-x {
  height: 868px;
  width: 428px;
}

.device-iphone-x .device-frame {
  background: #222;
  border-radius: 68px;
  box-shadow: inset 0 0 2px 2px #c8cacb, inset 0 0 0 7px #e2e3e4;
  height: 868px;
  padding: 28px;
  width: 428px;
}

.device-iphone-x .device-content {
  border-radius: 40px;
  height: 812px;
  width: 375px;
}

.device-iphone-x .device-stripe::after,
.device-iphone-x .device-stripe::before {
  border: solid rgba(51, 51, 51, .25);
  border-width: 0 7px;
  content: "";
  height: 7px;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 9;
}

.device-iphone-x .device-stripe::after {
  top: 85px;
}

.device-iphone-x .device-stripe::before {
  bottom: 85px;
}

.device-iphone-x .device-header {
  background: #222;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  height: 25px;
  left: 50%;
  margin-left: -102px;
  position: absolute;
  top: 28px;
  width: 204px;
}

.device-iphone-x .device-header::after,
.device-iphone-x .device-header::before {
  content: "";
  height: 10px;
  position: absolute;
  top: 0;
  width: 10px;
}

.device-iphone-x .device-header::after {
  background: radial-gradient(circle at bottom left, transparent 0, transparent 75%, #222 75%, #222 100%);
  left: -10px;
}

.device-iphone-x .device-header::before {
  background: radial-gradient(circle at bottom right, transparent 0, transparent 75%, #222 75%, #222 100%);
  right: -10px;
}

.device-iphone-x .device-sensors::after,
.device-iphone-x .device-sensors::before {
  content: "";
  position: absolute;
}

.device-iphone-x .device-sensors::after {
  background: #444;
  border-radius: 3px;
  height: 6px;
  left: 50%;
  margin-left: -25px;
  top: 32px;
  width: 50px;
}

.device-iphone-x .device-sensors::before {
  background: #444;
  border-radius: 50%;
  height: 14px;
  left: 50%;
  margin-left: 40px;
  top: 28px;
  width: 14px;
}

.device-iphone-x .device-btns {
  background: #c8cacb;
  height: 32px;
  left: -3px;
  position: absolute;
  top: 115px;
  width: 3px;
}

.device-iphone-x .device-btns::after,
.device-iphone-x .device-btns::before {
  background: #c8cacb;
  content: "";
  height: 62px;
  left: 0;
  position: absolute;
  width: 3px;
}

.device-iphone-x .device-btns::after {
  top: 60px;
}

.device-iphone-x .device-btns::before {
  top: 140px;
}

.device-iphone-x .device-power {
  background: #c8cacb;
  height: 100px;
  position: absolute;
  right: -3px;
  top: 200px;
  width: 3px;
}

.device-iphone-8 {
  height: 871px;
  width: 419px;
}

.device-iphone-8 .device-frame {
  background: #fff;
  border-radius: 68px;
  box-shadow: inset 0 0 0 2px #c8cacb, inset 0 0 0 7px #e2e3e4;
  height: 871px;
  padding: 102px 22px;
  width: 419px;
}

.device-iphone-8 .device-content {
  border: 2px solid #222;
  border-radius: 4px;
  height: 667px;
  width: 375px;
}

.device-iphone-8 .device-stripe::after,
.device-iphone-8 .device-stripe::before {
  border: solid rgba(51, 51, 51, .15);
  border-width: 0 7px;
  content: "";
  height: 6px;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 9;
}

.device-iphone-8 .device-stripe::after {
  top: 68px;
}

.device-iphone-8 .device-stripe::before {
  bottom: 68px;
}

.device-iphone-8 .device-header {
  border: 2px solid #c8cacb;
  border-radius: 50%;
  bottom: 25px;
  height: 58px;
  left: 50%;
  margin-left: -29px;
  position: absolute;
  width: 58px;
}

.device-iphone-8 .device-sensors {
  background: #666;
  border-radius: 3px;
  height: 6px;
  left: 50%;
  margin-left: -38px;
  position: absolute;
  top: 52px;
  width: 76px;
}

.device-iphone-8 .device-sensors::after,
.device-iphone-8 .device-sensors::before {
  background: #666;
  border-radius: 50%;
  content: "";
  position: absolute;
}

.device-iphone-8 .device-sensors::after {
  height: 10px;
  left: 50%;
  margin-left: -5px;
  top: -25px;
  width: 10px;
}

.device-iphone-8 .device-sensors::before {
  height: 12px;
  left: -42px;
  margin-top: -6px;
  top: 50%;
  width: 12px;
}

.device-iphone-8 .device-btns {
  background: #c8cacb;
  height: 30px;
  left: -3px;
  position: absolute;
  top: 102px;
  width: 3px;
}

.device-iphone-8 .device-btns::after,
.device-iphone-8 .device-btns::before {
  background: #c8cacb;
  content: "";
  height: 56px;
  left: 0;
  position: absolute;
  width: 3px;
}

.device-iphone-8 .device-btns::after {
  top: 62px;
}

.device-iphone-8 .device-btns::before {
  top: 132px;
}

.device-iphone-8 .device-power {
  background: #c8cacb;
  height: 80px;
  position: absolute;
  right: -2px;
  top: 160px;
  width: 3px;
}

.device-iphone-8.device-gold .device-frame {
  box-shadow: inset 0 0 0 2px #e4b08a, inset 0 0 0 7px #f7e8dd;
}

.device-iphone-8.device-gold .device-header {
  border-color: #e4b08a;
}

.device-iphone-8.device-gold .device-btns,
.device-iphone-8.device-gold .device-btns::after,
.device-iphone-8.device-gold .device-btns::before {
  background: #e4b08a;
}

.device-iphone-8.device-gold .device-power {
  background: #e4b08a;
}

.device-iphone-8.device-spacegray .device-frame {
  background: #222;
  box-shadow: inset 0 0 0 2px #74747a, inset 0 0 0 7px #9b9ba0;
}

.device-iphone-8.device-spacegray .device-stripe::after,
.device-iphone-8.device-spacegray .device-stripe::before {
  border-color: rgba(204, 204, 204, .35);
}

.device-iphone-8.device-spacegray .device-btns,
.device-iphone-8.device-spacegray .device-btns::after,
.device-iphone-8.device-spacegray .device-btns::before {
  background: #74747a;
}

.device-google-pixel-2-xl {
  height: 832px;
  width: 404px;
}

.device-google-pixel-2-xl .device-frame {
  background: #121212;
  border-radius: 36px;
  box-shadow: inset 0 0 0 2px #cfcfcf, inset 0 0 0 7px #9c9c9c;
  height: 832px;
  padding: 56px 22px;
  width: 404px;
}

.device-google-pixel-2-xl .device-content {
  border-radius: 27px;
  height: 720px;
  width: 360px;
}

.device-google-pixel-2-xl .device-header {
  height: 832px;
  left: 50%;
  margin-left: -150px;
  position: absolute;
  top: 0;
  width: 300px;
}

.device-google-pixel-2-xl .device-header::after,
.device-google-pixel-2-xl .device-header::before {
  background: #333;
  border-radius: 3px;
  content: "";
  height: 6px;
  left: 50%;
  margin-left: -73px;
  margin-top: -3px;
  position: absolute;
  width: 146px;
}

.device-google-pixel-2-xl .device-header::after {
  top: 24px;
}

.device-google-pixel-2-xl .device-header::before {
  bottom: 28px;
}

.device-google-pixel-2-xl .device-sensors {
  background: #333;
  border-radius: 7px;
  height: 14px;
  left: 54px;
  margin-top: -7px;
  position: absolute;
  top: 36px;
  width: 14px;
}

.device-google-pixel-2-xl .device-btns {
  background: #cfcfcf;
  height: 102px;
  position: absolute;
  right: -3px;
  top: 306px;
  width: 3px;
}

.device-google-pixel-2-xl .device-power {
  background: #cfcfcf;
  height: 58px;
  position: absolute;
  right: -3px;
  top: 194px;
  width: 3px;
}

.device-google-pixel {
  height: 744px;
  width: 360px;
}

.device-google-pixel .device-frame {
  background: #f7f7f8;
  border-radius: 54px;
  box-shadow: inset 0 0 0 2px #c8cacb, inset 0 0 0 6px #e2e3e4, inset 0 0 0 10px white;
  height: 744px;
  padding: 82px 18px 86px 18px;
  width: 360px;
}

.device-google-pixel .device-content {
  border: 2px solid #222;
  border-radius: 2px;
  height: 576px;
  width: 324px;
}

.device-google-pixel .device-stripe {
  border-top: 6px solid rgba(51, 51, 51, .15);
  bottom: 0;
  left: 254px;
  position: absolute;
  top: 0;
  width: 8px;
}

.device-google-pixel .device-stripe::after,
.device-google-pixel .device-stripe::before {
  border: solid rgba(51, 51, 51, .15);
  border-width: 0 6px;
  content: "";
  height: 10px;
  left: -254px;
  position: absolute;
  width: 360px;
  z-index: 9;
}

.device-google-pixel .device-stripe::after {
  top: 60px;
}

.device-google-pixel .device-stripe::before {
  bottom: 46px;
}

.device-google-pixel .device-sensors {
  background: #ddd;
  border-radius: 2.5px;
  height: 5px;
  left: 50%;
  margin-left: -39px;
  margin-top: -2.5px;
  position: absolute;
  top: 41px;
  width: 78px;
}

.device-google-pixel .device-sensors::after,
.device-google-pixel .device-sensors::before {
  background: #333;
  border-radius: 6px;
  content: "";
  position: absolute;
}

.device-google-pixel .device-sensors::after {
  height: 12px;
  left: 50%;
  margin-left: -14px;
  top: 21.5px;
  width: 28px;
}

.device-google-pixel .device-sensors::before {
  height: 10px;
  left: -81px;
  margin-top: -5px;
  top: 50%;
  width: 10px;
}

.device-google-pixel .device-btns {
  background: #c8cacb;
  height: 102px;
  position: absolute;
  right: -2px;
  top: 298px;
  width: 3px;
}

.device-google-pixel .device-power {
  background: #c8cacb;
  height: 50px;
  position: absolute;
  right: -2px;
  top: 184px;
  width: 3px;
}

.device-google-pixel.device-black .device-frame {
  background: #211d1c;
  box-shadow: inset 0 0 0 2px #363635, inset 0 0 0 6px #6a6967, inset 0 0 0 10px #3d3533;
}

.device-google-pixel.device-black .device-stripe,
.device-google-pixel.device-black .device-stripe::after,
.device-google-pixel.device-black .device-stripe::before {
  border-color: rgba(13, 13, 13, .35);
}

.device-google-pixel.device-black .device-sensors {
  background: #444;
}

.device-google-pixel.device-black .device-sensors::after {
  background: #0d0d0d;
}

.device-google-pixel.device-black .device-btns,
.device-google-pixel.device-black .device-btns::after,
.device-google-pixel.device-black .device-btns::before {
  background: #363635;
}

.device-google-pixel.device-black .device-power {
  background: #363635;
}

.device-google-pixel.device-blue .device-frame {
  box-shadow: inset 0 0 0 2px #2a5aff, inset 0 0 0 6px #7695ff, inset 0 0 0 10px white;
}

.device-google-pixel.device-blue .device-btns,
.device-google-pixel.device-blue .device-btns::after,
.device-google-pixel.device-blue .device-btns::before {
  background: #2a5aff;
}

.device-google-pixel.device-blue .device-power {
  background: #2a5aff;
}

.device-galaxy-s8 {
  height: 828px;
  width: 380px;
}

.device-galaxy-s8 .device-frame {
  background: #222;
  border: solid #cfcfcf;
  border-radius: 55px;
  border-width: 5px 0;
  box-shadow: inset 0 0 0 2px #9c9c9c;
  height: 828px;
  padding: 48px 10px 40px 10px;
  width: 380px;
}

.device-galaxy-s8 .device-content {
  border: 2px solid #222;
  border-radius: 34px;
  height: 740px;
  width: 360px;
}

.device-galaxy-s8 .device-stripe::after,
.device-galaxy-s8 .device-stripe::before {
  border: solid rgba(51, 51, 51, .15);
  border-width: 5px 0;
  content: "";
  height: 828px;
  position: absolute;
  top: 0;
  width: 6px;
  z-index: 9;
}

.device-galaxy-s8 .device-stripe::after {
  left: 48px;
}

.device-galaxy-s8 .device-stripe::before {
  right: 48px;
}

.device-galaxy-s8 .device-sensors {
  background: #666;
  border-radius: 3px;
  height: 6px;
  left: 50%;
  margin-left: -24px;
  margin-top: -3px;
  position: absolute;
  top: 32px;
  width: 48px;
}

.device-galaxy-s8 .device-sensors::after,
.device-galaxy-s8 .device-sensors::before {
  background: #666;
  border-radius: 50%;
  content: "";
  position: absolute;
  top: 50%;
}

.device-galaxy-s8 .device-sensors::after {
  box-shadow: -192px 0 #333, -174px 0 #333, -240px 0 #333;
  height: 8px;
  margin-top: -4px;
  right: -90px;
  width: 8px;
}

.device-galaxy-s8 .device-sensors::before {
  box-shadow: 186px 0 #666;
  height: 12px;
  left: -90px;
  margin-top: -6px;
  width: 12px;
}

.device-galaxy-s8 .device-btns {
  background: #9c9c9c;
  border-radius: 3px 0 0 3px;
  height: 116px;
  left: -3px;
  position: absolute;
  top: 144px;
  width: 3px;
}

.device-galaxy-s8 .device-btns::after {
  background: #9c9c9c;
  border-radius: 3px 0 0 3px;
  content: "";
  height: 54px;
  left: 0;
  position: absolute;
  top: 164px;
  width: 3px;
}

.device-galaxy-s8 .device-power {
  background: #9c9c9c;
  border-radius: 0 3px 3px 0;
  height: 54px;
  position: absolute;
  right: -3px;
  top: 260px;
  width: 3px;
}

.device-galaxy-s8.device-blue .device-frame {
  border-color: #a3c5e8;
  box-shadow: inset 0 0 0 2px #5192d4;
}

.device-galaxy-s8.device-blue .device-stripe::after,
.device-galaxy-s8.device-blue .device-stripe::before {
  border-color: rgba(255, 255, 255, .35);
}

.device-galaxy-s8.device-blue .device-btns,
.device-galaxy-s8.device-blue .device-btns::after {
  background: #5192d4;
}

.device-galaxy-s8.device-blue .device-power {
  background: #5192d4;
}

.device-ipad-pro {
  height: 804px;
  width: 560px;
}

.device-ipad-pro .device-frame {
  background: #fff;
  border-radius: 38px;
  box-shadow: inset 0 0 0 2px #c8cacb, inset 0 0 0 6px #e2e3e4;
  height: 804px;
  padding: 62px 25px;
  width: 560px;
}

.device-ipad-pro .device-content {
  border: 2px solid #222;
  border-radius: 2px;
  height: 680px;
  width: 510px;
}

.device-ipad-pro .device-header {
  border: 2px solid #c8cacb;
  border-radius: 50%;
  bottom: 17px;
  height: 34px;
  left: 50%;
  margin-left: -17px;
  position: absolute;
  width: 34px;
}

.device-ipad-pro .device-sensors {
  background: #666;
  border-radius: 50%;
  height: 10px;
  left: 50%;
  margin-left: -5px;
  margin-top: -5px;
  position: absolute;
  top: 34px;
  width: 10px;
}

.device-ipad-pro.device-gold .device-frame {
  box-shadow: inset 0 0 0 2px #e4b08a, inset 0 0 0 6px #f7e8dd;
}

.device-ipad-pro.device-gold .device-header {
  border-color: #e4b08a;
}

.device-ipad-pro.device-rosegold .device-frame {
  box-shadow: inset 0 0 0 2px #f6a69a, inset 0 0 0 6px #facfc9;
}

.device-ipad-pro.device-rosegold .device-header {
  border-color: #f6a69a;
}

.device-ipad-pro.device-spacegray .device-frame {
  background: #222;
  box-shadow: inset 0 0 0 2px #818187, inset 0 0 0 6px #9b9ba0;
}

.device-ipad-pro.device-spacegray .device-header {
  border-color: #818187;
}

.device-surface-pro {
  height: 394px;
  width: 561px;
}

.device-surface-pro .device-frame {
  background: #0d0d0d;
  border-radius: 10px;
  box-shadow: inset 0 0 0 2px #c8c8c8;
  height: 394px;
  margin: 0 auto;
  padding: 26px 24px;
  width: 561px;
}

.device-surface-pro .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 342px;
  width: 513px;
}

.device-surface-pro .device-btns::after,
.device-surface-pro .device-btns::before {
  background: #c8c8c8;
  content: "";
  height: 2px;
  position: absolute;
  top: -2px;
}

.device-surface-pro .device-btns::after {
  left: 48px;
  width: 26px;
}

.device-surface-pro .device-btns::before {
  left: 94px;
  width: 48px;
}

.device-surface-pro .device-sensors {
  background: #333;
  border-radius: 50%;
  height: 6px;
  left: 50%;
  margin-left: -3px;
  margin-top: -3px;
  position: absolute;
  top: 14px;
  width: 6px;
}

.device-surface-book {
  height: 424px;
  width: 728px;
}

.device-surface-book .device-frame {
  background: #0d0d0d;
  border-radius: 12px;
  box-shadow: inset 0 0 0 2px #c8c8c8;
  height: 408px;
  margin: 0 auto;
  padding: 24px 22px;
  position: relative;
  width: 584px;
}

.device-surface-book .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 360px;
  width: 540px;
}

.device-surface-book .device-btns::after,
.device-surface-book .device-btns::before {
  background: #c8c8c8;
  content: "";
  height: 2px;
  position: absolute;
  top: -2px;
}

.device-surface-book .device-btns::after {
  left: 122px;
  width: 20px;
}

.device-surface-book .device-btns::before {
  left: 168px;
  width: 44px;
}

.device-surface-book .device-power {
  background: linear-gradient(to bottom, #eee, #c8c8c8);
  border: solid #c8c8c8;
  border-radius: 2px;
  border-width: 0 2px;
  height: 12px;
  margin-top: 4px;
  position: relative;
  width: 728px;
}

.device-surface-book .device-power::after,
.device-surface-book .device-power::before {
  content: "";
  position: absolute;
}

.device-surface-book .device-power::after {
  background: radial-gradient(circle at center, #eee 0, #eee 95%, #a2a1a1 100%);
  border-radius: 0 0 6px 6px;
  height: 8px;
  left: 50%;
  margin-left: -125px;
  top: 0;
  width: 250px;
  z-index: 1;
}

.device-surface-book .device-power::before {
  background: linear-gradient(to bottom, #eee, #c8c8c8);
  border-radius: 2px 2px 0 0;
  bottom: 12px;
  height: 8px;
  left: 50%;
  margin-left: -292px;
  width: 584px;
}

.device-macbook-pro {
  height: 444px;
  width: 740px;
}

.device-macbook-pro .device-frame {
  background: #0d0d0d;
  border-radius: 20px;
  box-shadow: inset 0 0 0 2px #c8cacb;
  height: 428px;
  margin: 0 auto;
  padding: 29px 19px 39px 19px;
  position: relative;
  width: 614px;
}

.device-macbook-pro .device-frame::after {
  background: #272626;
  border-radius: 0 0 20px 20px;
  bottom: 2px;
  content: "";
  height: 26px;
  left: 2px;
  position: absolute;
  width: 610px;
}

.device-macbook-pro .device-frame::before {
  bottom: 10px;
  color: #c8cacb;
  content: "MacBook Pro";
  font-size: 12px;
  height: 16px;
  left: 50%;
  line-height: 16px;
  margin-left: -100px;
  position: absolute;
  text-align: center;
  width: 200px;
  z-index: 1;
}

.device-macbook-pro .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 360px;
  width: 576px;
}

.device-macbook-pro .device-power {
  background: #e2e3e4;
  border: solid #d5d6d8;
  border-radius: 2px 2px 0 0;
  border-width: 2px 4px 0 4px;
  height: 14px;
  margin-top: -10px;
  position: relative;
  width: 740px;
  z-index: 9;
}

.device-macbook-pro .device-power::after,
.device-macbook-pro .device-power::before {
  content: "";
  position: absolute;
}

.device-macbook-pro .device-power::after {
  background: #d5d6d8;
  border-radius: 0 0 10px 10px;
  box-shadow: inset 0 0 4px 2px #babdbf;
  height: 10px;
  left: 50%;
  margin-left: -60px;
  top: -2px;
  width: 120px;
}

.device-macbook-pro .device-power::before {
  background: #a0a3a7;
  border-radius: 0 0 180px 180px/ 0 0 12px 12px;
  box-shadow: inset 0 -2px 6px 0 #474a4d;
  height: 12px;
  left: -4px;
  margin: 0 auto;
  top: 10px;
  width: 740px;
}

.device-macbook-pro.device-spacegray .device-frame {
  box-shadow: inset 0 0 0 2px #767a7d;
}

.device-macbook-pro.device-spacegray .device-power {
  background: #909496;
  border-color: #767a7d;
}

.device-macbook-pro.device-spacegray .device-power::after {
  background: #83878a;
  box-shadow: inset 0 0 4px 2px #6a6d70;
}

.device-macbook-pro.device-spacegray .device-power::before {
  background: #515456;
  box-shadow: inset 0 -2px 6px 0 black;
}

.device-macbook {
  height: 432px;
  width: 740px;
}

.device-macbook .device-frame {
  background: #0d0d0d;
  border-radius: 20px;
  box-shadow: inset 0 0 0 2px #c8cacb;
  height: 428px;
  margin: 0 auto;
  padding: 29px 19px 39px 19px;
  position: relative;
  width: 614px;
}

.device-macbook .device-frame::after {
  background: #272626;
  border-radius: 0 0 20px 20px;
  bottom: 2px;
  content: "";
  height: 26px;
  left: 2px;
  position: absolute;
  width: 610px;
}

.device-macbook .device-frame::before {
  bottom: 10px;
  color: #c8cacb;
  content: "MacBook";
  font-size: 12px;
  height: 16px;
  left: 50%;
  line-height: 16px;
  margin-left: -100px;
  position: absolute;
  text-align: center;
  width: 200px;
  z-index: 1;
}

.device-macbook .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 360px;
  width: 576px;
}

.device-macbook .device-power {
  background: #e2e3e4;
  border: solid #d5d6d8;
  border-radius: 2px 2px 0 0;
  border-width: 0 4px;
  height: 4px;
  margin-top: -10px;
  position: relative;
  width: 740px;
  z-index: 9;
}

.device-macbook .device-power::after,
.device-macbook .device-power::before {
  content: "";
  position: absolute;
}

.device-macbook .device-power::after {
  background: radial-gradient(circle at center, #e2e3e4 0, #e2e3e4 85%, #a0a3a7 100%);
  border: solid #adb0b3;
  border-width: 0 2px;
  height: 4px;
  left: 50%;
  margin-left: -60px;
  width: 120px;
}

.device-macbook .device-power::before {
  background: #a0a3a7;
  border-radius: 0 0 180px 180px/ 0 0 10px 10px;
  box-shadow: inset 0 -2px 6px 0 #474a4d;
  height: 10px;
  left: -4px;
  margin: 0 auto;
  top: 4px;
  width: 740px;
}

.device-macbook.device-gold .device-frame {
  box-shadow: inset 0 0 0 2px #edccb4;
}

.device-macbook.device-gold .device-power {
  background: #f7e8dd;
  border-color: #edccb4;
}

.device-macbook.device-gold .device-power::after {
  background: radial-gradient(circle at center, #f7e8dd 0, #f7e8dd 85%, #dfa276 100%);
  border-color: #e4b08a;
}

.device-macbook.device-gold .device-power::before {
  background: #edccb4;
  box-shadow: inset 0 -2px 6px 0 #83491f;
}

.device-macbook.device-rosegold .device-frame {
  box-shadow: inset 0 0 0 2px #f6a69a;
}

.device-macbook.device-rosegold .device-power {
  background: #facfc9;
  border-color: #f6a69a;
}

.device-macbook.device-rosegold .device-power::after {
  background: radial-gradient(circle at center, #facfc9 0, #facfc9 85%, #ef6754 100%);
  border-color: #f6a69a;
}

.device-macbook.device-rosegold .device-power::before {
  background: #f6a69a;
  box-shadow: inset 0 -2px 6px 0 #851b0c;
}

.device-macbook.device-spacegray .device-frame {
  box-shadow: inset 0 0 0 2px #767a7d;
}

.device-macbook.device-spacegray .device-power {
  background: #909496;
  border-color: #767a7d;
}

.device-macbook.device-spacegray .device-power::after {
  background: radial-gradient(circle at center, #909496 0, #909496 85%, #515456 100%);
  border-color: #5d6163;
}

.device-macbook.device-spacegray .device-power::before {
  background: #515456;
  box-shadow: inset 0 -2px 6px 0 black;
}

.device-surface-studio {
  height: 506px;
  width: 640px;
}

.device-surface-studio .device-frame {
  background: #0d0d0d;
  border-radius: 10px;
  box-shadow: inset 0 0 0 2px black;
  height: 440px;
  padding: 20px;
  width: 640px;
}

.device-surface-studio .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 400px;
  width: 600px;
}

.device-surface-studio .device-stripe {
  background: #444;
  border-radius: 0 0 2px 2px;
  bottom: 0;
  height: 4px;
  left: 50%;
  margin-left: -117px;
  position: absolute;
  width: 234px;
}

.device-surface-studio .device-stripe::after,
.device-surface-studio .device-stripe::before {
  content: "";
  left: 50%;
  position: absolute;
  top: -75px;
}

.device-surface-studio .device-stripe::after {
  border: 6px solid #d5d6d8;
  border-radius: 0 0 18px 18px;
  border-top: 0;
  box-shadow: inset 0 0 0 4px #c8cacb;
  height: 60px;
  margin-left: -140px;
  width: 280px;
  z-index: -1;
}

.device-surface-studio .device-stripe::before {
  border: 15px solid #e2e3e4;
  border-radius: 0 0 4px 4px;
  border-top: 0;
  height: 70px;
  margin-left: -150px;
  width: 300px;
  z-index: -2;
}

.device-surface-studio .device-power {
  background: #eff0f0;
  border: solid #e2e3e4;
  border-radius: 0 0 2px 2px;
  border-width: 0 4px 2px 4px;
  height: 32px;
  margin: 30px auto 0 auto;
  position: relative;
  width: 250px;
}

.device-surface-studio .device-power::after {
  background: #adb0b3;
  content: "";
  height: 2px;
  left: -4px;
  position: absolute;
  top: 4px;
  width: 250px;
}

.device-imac-pro {
  height: 484px;
  width: 624px;
}

.device-imac-pro .device-frame {
  background: #0d0d0d;
  border-radius: 18px;
  box-shadow: inset 0 0 0 2px #080808;
  height: 428px;
  padding: 24px 24px 80px 24px;
  position: relative;
  width: 624px;
}

.device-imac-pro .device-frame::after {
  background: #2f2e33;
  border-radius: 0 0 18px 18px;
  bottom: 2px;
  content: "";
  height: 54px;
  left: 2px;
  position: absolute;
  width: 620px;
}

.device-imac-pro .device-frame::before {
  bottom: 15px;
  color: #0d0d0d;
  content: "";
  font-size: 24px;
  height: 24px;
  left: 50%;
  line-height: 24px;
  margin-left: -100px;
  position: absolute;
  text-align: center;
  width: 200px;
  z-index: 9;
}

.device-imac-pro .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 324px;
  width: 576px;
}

.device-imac-pro .device-power::after,
.device-imac-pro .device-power::before {
  content: "";
}

.device-imac-pro .device-power::after {
  background: #222225;
  border-radius: 2px;
  height: 6px;
  margin: 0 auto;
  position: relative;
  width: 180px;
}

.device-imac-pro .device-power::before {
  border: solid transparent;
  border-bottom-color: #333;
  border-width: 0 8px 50px 8px;
  height: 50px;
  margin: 0 auto;
  position: relative;
  width: 130px;
}

.device-apple-watch {
  height: 234px;
  width: 200px;
}

.device-apple-watch .device-frame {
  background: #0d0d0d;
  border-radius: 40px;
  box-shadow: inset 0 0 2px 2px #adb0b3, inset 0 0 0 6px #e2e3e4, inset 0 0 0 8px #e2e3e4;
  height: 234px;
  padding: 32px;
  position: relative;
  width: 200px;
}

.device-apple-watch .device-frame::after {
  border-radius: 30px;
  box-shadow: inset 0 0 25px 0 rgba(255, 255, 255, .75);
  content: "";
  height: 216px;
  left: 9px;
  position: absolute;
  top: 9px;
  width: 182px;
}

.device-apple-watch .device-content {
  border: 2px solid #121212;
  border-radius: 2px;
  height: 170px;
  width: 136px;
}

.device-apple-watch .device-btns {
  background: #e2e3e4;
  border-left: 2px solid #adb0b3;
  border-radius: 8px 4px 4px 8px / 20px 4px 4px 20px;
  box-shadow: inset 0 0 2px 2px #adb0b3;
  height: 44px;
  position: absolute;
  right: -10px;
  top: 52px;
  width: 16px;
  z-index: 9;
}

.device-apple-watch .device-btns::after {
  background: #e2e3e4;
  border-radius: 4px 2px 2px 4px / 10px 2px 2px 10px;
  box-shadow: inset 0 0 1px 2px #adb0b3;
  content: "";
  height: 66px;
  position: absolute;
  right: 6px;
  top: 68px;
  width: 8px;
}

.device-apple-watch .device-btns::before {
  background: #adb0b3;
  box-shadow: 0 -16px #adb0b3, 0 -12px #adb0b3, 0 -8px #adb0b3, 0 -4px #adb0b3, 0 4px #adb0b3, 0 8px #adb0b3, 0 12px #adb0b3, 0 16px #adb0b3;
  content: "";
  height: 2px;
  margin-top: -1px;
  position: absolute;
  right: 0;
  top: 50%;
  width: 6px;
}


#devices {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  background: #f8f9fa;
  z-index: 100;
  padding: 40px;
}
#devices .devices-row {
  height: calc(100vh - 80px);
}
#devices .devices-row > div {
  max-height: 804px;
}
#devices .device {
  position: relative;
  transform: scale(1);
  transform-origin: 100% 100%;
  z-index: 1;
}
#devices .device-iphone-x {
  transform: scale(0.8);
}
#devices .device-frame iframe {
  width: 100%;
  height: 100%;
  border: none;
}
#devices .device-iphone-x .device-frame iframe {
  border-radius: 40px;
}

#devices .close {
  position: absolute;
  top:20px;
  left:20px;
}
#devices .close i {
  font-size: 2rem;
}

#devices .qr-code {
  position: absolute;
  top:20px;
  right:20px;
}
#devices .qr-code img {
  width: 100px;
}
