
h3.gray {
    font-size: 18px;
    color: #aeafb2;
}
.btn-link,
h3 .btn-link {
    padding: 0px;
    color: #6666ff;
}

.media {
    margin-bottom: 10px;
}
.media-left img {
    max-width: 60px;
    margin-right: 10px;
}
.media-body h3 {
    margin-top: 10px;
    margin-bottom: 0;
}

.panel {
    border-radius: 8px;
    overflow: hidden;
}
.panel-body {
    padding: 30px;
}

.panel-blue {
    box-shadow: none;
    border: 2px solid #babafc;
}
.panel-blue .panel-body {
    padding: 0;
    background: #f2f2fa;
}
.panel-blue .row {
    border-bottom: 1px solid #b1b1fc;
    padding: 20px;
    padding-bottom: 0px;
}
.panel-blue h3 {
    margin-top: 0;
}
.panel-blue .item {
    display: inline-block;
    text-align: center;
    padding: 0 20px;
}
.panel-blue .item span {
    display: block;
}
.panel-blue .item .nb {
    font-size: 18px;
    font-style: italic;
    font-weight: bold;
    color: #6666ff;
}
.panel-blue .item .title {
    font-size: 13px;
    font-weight: bold;
    color: #8291a6;
    text-transform: uppercase;
}
.panel-blue .row.footer {
    border-bottom: none;
    padding: 0;
    padding-left: 20px;
}
.panel-blue .row.footer p {
    margin: 15px 0;
    font-size: 16px;
}
.panel-blue .row.footer a.btn-primary {
    display: block;
    margin-top: 15px;
    margin-right: 15px;
}
.panel-blue .row.footer a.btn-primary i {
    margin-left: 10px;
}


.panel-green {
    box-shadow: none;
    border: 1px solid #dbdbdb;
}
.panel-green .panel-body {
    padding: 0;
    background: #F9FAFB;
}
.panel-green .row {
    padding: 20px;
}
.panel-green .row.footer {
    border-bottom: none;
    padding: 0;
    padding-left: 20px;
}
.panel-green .row.footer p {
    margin: 15px 0;
    font-size: 16px;
}
.panel-green .row.footer a.btn-blue {
    background: #46cfb0;
    height: 100%;
    display: block;
    padding: 15px 20px;
    border-radius: 0;
    color: white;
}
.panel-green .row.footer a.btn-blue i {
    margin-left: 10px;
}



.btn-download-invoice i {
    font-size: 18px;
}

.panel-gray {
    background: #fafafa;
    box-shadow: none;
    border: 1px solid #dbdbdb;
}
.panel-gray h3 {
    color: #3c3e45;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: normal;
    font-size: 18px;
}
.panel-gray p {
    color: #88898d;
    margin-bottom: 0;
}
.add-all-univers {
    width: 100%;
    position: relative;
}

.icone {
    width: 64px;
    height: 64px;
    text-align: center;
    padding: 19px;
    background: #6666ff;
    border-radius: 8px;
}
.icone i {
    font-size: 24px;
    color: white;
}

.circle-container {
    display: inline-block;
    margin-right: 20px;
}
.circle {
    position: relative;
    -moz-box-shadow: inset 0 0px 0 2px rgba(0,0,0,.2), 0 0px 0 4px rgba(0,0,0,.1);
    -moz-border-radius: 50em;
    border-radius: 50%;
    width: 130px;
    height: 130px;
    vertical-align: top;
    padding: 0;
}
.circle input {
    color: #3c3e45 !important;
    font-family: 'Lato' !important;
    font-size: 22px !important;
    padding: 0 !important;
}
.circle span.max {
    font-size: 13px;
    position: absolute;
    top: 70%;
    text-align: center;
    width: 100%;
    color: #9e9fa2;
}
.circle-container span.title {
    text-transform: uppercase;
    text-align: center;
    color: #8594a7;
    font-weight: bold;
    display: block;
    width: 100%;
    margin-top: 10px;
}



.subscription_confirmation {
    text-align: center;
    margin-top: 50px;
    background: white;
    box-shadow: 10px 10px 8px 0 rgba(0,0,0,0.02);
    border-radius: 6px;
    padding: 40px;
}
.subscription_confirmation .confirmation_icone {
    background: linear-gradient(135.46deg, #2493C1 0%, #2BBFA5 100%);
    box-shadow: 0 4px 16px 0 rgba(91,107,128,0.2);
    margin: 0 auto;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    padding-top: 40px;
}
.subscription_confirmation .confirmation_icone.text-danger {
    background: #f2dede;
}
.subscription_confirmation .confirmation_icone i {
    font-size: 72px;
    color: white;
}
.subscription_confirmation .confirmation_icone.text-danger i {
    color: #a94442;
}
.subscription_confirmation h3 {
    color: #231F20;
    font-size: 36px;
    font-weight: 300;
    line-height: 42px;
    text-align: center;
}
.subscription_confirmation p {
    color: #677991;
    font-size: 15px;
    line-height: 22px;
    text-align: center;
}
.subscription_confirmation .btn-confirmation,
.subscription_confirmation .btn-error {
    margin-top: 20px;
}

.subscription_confirmation .btn-default {
    text-transform: uppercase;
    padding: 14px 50px;
    border-radius: 12px;
    color: white;
}
.subscription_confirmation .btn-confirmation .btn-default {
    background-color: #46CFB0;
}

.subscription_confirmation .form.form-transparent {
    margin-bottom: 0;
}
.subscription_confirmation .form.form-transparent .form-actions {
    background: transparent;
    box-shadow: none;
    margin-left: 0;
    position: relative;
}
.subscription_confirmation .form.form-transparent .form-actions .btn {
    margin-right: 0;
}
