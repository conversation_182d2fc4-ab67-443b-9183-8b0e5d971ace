@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

:root {
    --blue: #333a56;
    --orange: #BB8805;
    --gray-0: #FFFFFF;
    --gray-100: #F8F9FA;
    --gray-200: #E9ECEF;
    --gray-300: #DEE2E6;
    --gray-400: #CED4DA;
    --gray-500: #ADB5BD;
    --gray-600: #6C757D;
    --gray-700: #495057;
    --gray-800: #343A40;
    --gray-900: #212529;
    --dark-grey: #303030;
    --astraeos-400: #181C31;
    --Astraeos-Astraeos_400: #181C31;
    --astraeos-300: #333A56;
    --Astraeos-Astraeos_300: #333A56;
    --astraeos-200: #85899A;
    --Astraeos-Astraeos_200: #85899A;
    --astraeos-100: #D6D8DD;
    --Astraeos-Astraeos_100: #D6D8DD;
    --astraeos-50: #E8EAF0;
    --Astraeos-Astraeos_50: #E8EAF0;
    --var-400: #BB8805;
    --Or-400: #BB8805;
    --var-100: #F1E7CD;
    --Or-100: #F1E7CD;
    --var-50: #F8F3E6;
    --Or-50: #F8F3E6;
    --Gray-50: #F7FAFC;
    --Gray-100: #EDF2F7;
    --Gray-200: #E2E8F0;
    --Gray-300: #CBD5E0;
    --Gray-400: #A0AEC0;
    --Gray-500: #718096;
    --Gray-600: #4A5568;
    --Gray-700: #2D3748;
    --Gray-800: #1A202C;
    --rouge-500: #A33E18;
    --rouge-400: #E95822;
    --rouge-100: #FBDED3;
    --jaune-500: #B18E32;
    --jaune-400: #FDCB47;
    --jaune-100: #FFF5DA;
    --vert-500: #036A3A;
    --vert-400: #049753;
    --vert-100: #CDEADD;
    --bleu-500: #015A7C;
    --bleu-400: #0281B1;
    --bleu-100: #CCE6EF;
    --spread-focus: 0px 0px 0px 4px #E6D7B0;
    --spread-focus-erreur: 0px 0px 0px 4px #FBDED3;
    --shadow-card: 2px 2px 8px 0px rgba(0,0,0,0);
    --var-72: 72px;
    --var-60: 60px;
    --var-48: 48px;
    --var-36: 36px;
    --var-30: 30px;
    --var-24: 24px;
    --var-20: 20px;
    --var-18: 18px;
    --var-16: 16px;
    --var-14: 14px;
    --var-12: 12px;
    --var-10: 10px;
}
.gray-0 { color: var(--gray-0); }
.gray-100 { color: var(--gray-100); }
.gray-200 { color: var(--gray-200); }
.gray-300 { color: var(--gray-300); }
.gray-400 { color: var(--gray-400); }
.gray-500 { color: var(--gray-500); }
.gray-600 { color: var(--gray-600); }
.gray-700 { color: var(--gray-700); }
.gray-800 { color: var(--gray-800); }
.gray-900 { color: var(--gray-900); }
.astraeos-400 { color: var(--astraeos-400); }
.astraeos-300 { color: var(--astraeos-300); }
.astraeos-200 { color: var(--astraeos-200); }
.astraeos-100 { color: var(--astraeos-100); }
.astraeos-50 { color: var(--astraeos-50); }
.var-400 { color: var(--var-400); }
.var-100 { color: var(--var-100); }
.var-50 { color: var(--var-50); }
.rouge-500 { color: var(--rouge-500); }
.rouge-400 { color: var(--rouge-400); }
.rouge-100 { color: var(--rouge-100); }
.jaune-500 { color: var(--jaune-500); }
.jaune-400 { color: var(--jaune-400); }
.jaune-100 { color: var(--jaune-100); }
.vert-500 { color: var(--vert-500); }
.vert-400 { color: var(--vert-400); }
.vert-100 { color: var(--vert-100); }
.bleu-500 { color: var(--bleu-500); }
.bleu-400 { color: var(--bleu-400); }
.bleu-100 { color: var(--bleu-100); }
.spread-focus { box-shadow: var(--spread-focus); }
.spread-focus-erreur { box-shadow: var(--spread-focus-erreur); }
.shadow-card { box-shadow: var(--shadow-card); }
.var-72 { font-size: var(--var-72); line-height: 0.016667; font-weight: 600; }
.var-60 { font-size: var(--var-60); line-height: 0.02; font-weight: 600; }
.var-48 { font-size: var(--var-48); line-height: 0.025; font-weight: 600; }
.var-36 { font-size: var(--var-36); line-height: 0.033333; font-weight: 600; }
.var-30 { font-size: var(--var-30); line-height: 0.04; font-weight: 500; }
.var-24 { font-size: var(--var-24); line-height: 0.0625; font-weight: 500; }
.var-20 { font-size: var(--var-20); line-height: 0.065; font-weight: 500; }
.var-18 { font-size: var(--var-18); line-height: 0.077778; font-weight: 500; }
.var-16 { font-size: var(--var-16); line-height: 0.075; font-weight: 500; }
.var-14 { font-size: var(--var-14); line-height: 0.085714; font-weight: 500; }
.var-12 { font-size: var(--var-12); line-height: 0.1; font-weight: 500; }
.var-10 { font-size: var(--var-10); line-height: 0.12; font-weight: 400; }
.radius-6 { border-radius: 6px; }

body {
    color: var(--gray-800);
    font-family: "Inter", sans-serif;
}
h1 {
    font-size: 40px;
    color: var(--blue);
}
h2 {
    font-size: 32px;
    color: var(--blue);
}
h3 {
    font-size: 24px;
    color: var(--blue);
}
h4 {
    font-size: 18px;
    color: var(--blue);
}
h4.page-title {
    font-size: 20px;
    color: var(--gray-900, #212529);
    font-weight: 500;
}
p {
    font-size: 14px;
}

#kt_header,
#kt_header_mobile {
    background: var(--blue);
}
a {
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.bg-primary {
    background-color: var(--blue) !important;
}

.container-small {
    max-width: 920px;
    margin: 0 auto;
}

.btn {
    border-radius: 30px;
    height: 40px;
    gap: 8px;
    padding: 0 16px;
    font-weight: 600;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.btn i {
    font-size: 16px;
}
.btn.btn-icon {
    border-radius: 8px;
}
.btn.btn-icon i {
    padding: 0;
    margin: 0;
}
.btn.btn-icon.btn-sm {
    border-radius: 8px;
    height: 32px;
    width: 32px;
}
.btn.btn-icon.btn-sm i {
    font-size: 14px;
}
.btn.btn-lg, .btn.btn-icon.btn-lg {
    height: 48px;
    padding: 0 24px;
    font-size: 18px;
}
.btn.btn-sm {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
}
.btn.btn-sm i {
    font-size: 14px;
}
.btn.btn-xs {
    height: 24px;
    padding: 0 8px;
    gap: 6px;
    font-size: 12px;
}

.btn.btn-primary {
    color: #FFFFFF;
    background: var(--Astraeos-Astraeos_400, #181C31);
    border-color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-primary:focus:not(.btn-text),
.btn.btn-primary.focus:not(.btn-text) {
    background-color: var(--Astraeos-Astraeos_300, #333A56);
    border-color: var(--Astraeos-Astraeos_300, #333A56);
}

.btn.btn-light-primary {
    color: var(--Astraeos-Astraeos_400, #181C31);
    background-color: transparent;
    border-color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-light-primary i {
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-light-primary:focus:not(.btn-text),
.btn.btn-light-primary.focus:not(.btn-text) {
    color: var(--Astraeos-Astraeos_300, #333A56);
    border: 1px solid var(--Astraeos-Astraeos_300, #333A56);
    background-color: transparent;
}
.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-light-primary:focus:not(.btn-text) i,
.btn.btn-light-primary.focus:not(.btn-text) i {
    color: var(--Astraeos-Astraeos_300, #333A56);
}
.btn.btn-light-primary.dropdown-toggle:after {
    color: var(--gray-800);
}
.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled).dropdown-toggle:after,
.btn.btn-light-primary:focus:not(.btn-text).dropdown-toggle:after,
.btn.btn-light-primary.focus:not(.btn-text).dropdown-toggle:after {
    color: var(--Astraeos-Astraeos_300, #333A56);
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean:focus:not(.btn-text),
.btn.btn-clean.focus:not(.btn-text) {
    color: var(--blue);
}
.btn.btn-clean.btn-light-primary:not(.btn-text):not(:disabled):not(.disabled) i {
    color: var(--blue);
}
.btn.btn-clean.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-light-primary:focus:not(.btn-text),
.btn.btn-clean.btn-light-primary.focus:not(.btn-text),
.btn.btn-clean.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-light-primary:focus:not(.btn-text) i,
.btn.btn-clean.btn-light-primary.focus:not(.btn-text) i {
    color: white;
}
.btn.btn-clean:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean:focus:not(.btn-text) i,
.btn.btn-clean.focus:not(.btn-text) i {
    color: var(--blue);
}
.btn.btn-clean.btn-hover-light-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-light-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-light-danger.focus:not(.btn-text) {
    color: #F64E60;
}
.btn.btn-clean.btn-hover-light-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-light-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-light-danger.focus:not(.btn-text) i {
    color: #F64E60;
}

.btn.btn-secondary {
    background: var(--Or-100, #F1E7CD);
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-secondary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-secondary:focus:not(.btn-text),
.btn.btn-secondary.focus:not(.btn-text) {
    background-color: var(--Or-50, #F8F3E6);
    border-color: var(--Or-50, #F8F3E6);
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-secondary.dropdown-toggle:after {
    color: var(--Astraeos-Astraeos_400, #181C31);
    margin-left: 0;
}

.btn.btn-white {
    background: #FFF;
    border-color: #FFF;
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn.btn-white:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-white:focus:not(.btn-text),
.btn.btn-white.focus:not(.btn-text) {
    background: #FFF;
    border-color: #FFF;
    color: var(--Astraeos-Astraeos_300, #333A56);
}
.btn.btn.btn-with-label .label {
    align-self: center;
}

.btn-status {
    font-size: 14px;
    font-weight: 400;
    color: var(--gray-800, #343A40);
}
.btn.btn-status:before {
    content: "";
    position: relative;
    background: var(--vert-400, #049753);
    border-radius: 50%;
    width: 8px;
    height: 8px;
    margin-right: 0;
    display: inline-block;
    vertical-align: middle;
}

.label.label-inline {
    min-height: 24px;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
}
.label.label-inline.label-lg {
    min-height: 28px;
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
}
.label.label-primary {
    background-color: var(--Or-100, #F1E7CD);
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.label.label-light-primary {
    color: var(--astraeos-300);
    background-color: var(--astraeos-50);
}
.label.label-outline-primary {
    background-color: transparent;
    color: var(--blue);
    border: 1px solid var(--blue);
}
.label.label-success {
    background: var(--vert-100, #CDEADD);
    color: var(--vert-500, #036A3A);
}
.label.label-warning {
    background-color: var(--Or-100, #F1E7CD);
    color: var(--jaune-500, #B18E32);
}
.label.label-danger {
    background: var(--rouge-100, #FBDED3);
    color: var(--rouge-500, #A33E18);
}
.label.label-info {
    background: var(--bleu-100, #CCE6EF);
    color: var(--bleu-500, #015A7C);
}


.navi .navi-item .navi-link.active .navi-icon svg g [fill] {
    fill: var(--blue);
}
.svg-icon.svg-icon-primary svg g [fill] {
    fill: var(--blue) !important;
}
.navi .navi-item .navi-text {
    padding-right: 12px;
}
.navi .navi-item .navi-link.active .navi-text {
    color: var(--blue);
}
.navi .navi-item .navi-link:hover,
.navi .navi-item .navi-link:hover .navi-text,
.navi .navi-item .navi-link:hover .navi-icon i {
    color: var(--blue);
}
.text-primary {
    color: var(--blue) !important;
}
a.text-hover-primary:hover, .text-hover-primary:hover {
    color: var(--blue) !important;
}
.text-info {
    color: var(--orange) !important;
}
.text-muted {
    color: var(--gray-600, #6C757D);
}

.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text),
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) {
    color: #F64E60 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-danger:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-danger:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-danger.focus:not(.btn-text) i {
    color: #F64E60 !important;
}

.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-success:focus:not(.btn-text),
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) {
    color: #1BC5BD !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-success:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-success:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-success.focus:not(.btn-text) i {
    color: #1BC5BD !important;
}

.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text),
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) {
    color: #FFA800 !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-warning:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-warning:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-warning.focus:not(.btn-text) i {
    color: #FFA800 !important;
}

.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-clean.btn-hover-info:focus:not(.btn-text),
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) {
    color: #8950FC !important;
    background-color: #F3F6F9 !important;
    border-color: transparent !important;
}
.btn.btn-clean.btn-hover-info:hover:not(.btn-text):not(:disabled):not(.disabled) i,
.btn.btn-clean.btn-hover-info:focus:not(.btn-text) i,
.btn.btn-clean.btn-hover-info.focus:not(.btn-text) i {
    color: #8950FC !important;
}

.btn-block {
    background: var(--gray-0, #FFF);
    height: 60px;
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.btn-block:hover {
    color: var(--Astraeos-Astraeos_300, #333A56);
}

.btn.disabled, .btn:disabled {
    cursor: not-allowed;
    background: var(--gray-500, #ADB5BD);
    color: var(--gray-700, #495057);
}
.image-input [data-action="ckfinder"] {
    position: absolute;
    right: -10px;
    top: -10px;
}


iconify-icon {
    vertical-align: middle;
}

#kt_aside {
    background: linear-gradient(179deg, #333A56 6.12%, rgba(41, 46, 69, 0.00) 47.51%), #181C31;
    width: 56px;
}
#kt_aside .aside-primary {
    width: 56px;
    transition: all .3s ease;
}
.aside-brand {
    padding: 24px 0;
}
.aside-nav {
    margin-top: 58px;
    padding: 0 8px;
    overflow-y: auto;
}
.aside-nav .menu-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}
.aside-nav .menu-nav li {
    position: relative;
    margin-bottom: 8px;
}
.aside-nav .menu-nav li a {
    height: 40px;
    color: #FFF;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 6px;
    margin: 0;
    gap: 8px;
}
.aside-nav .menu-nav li a .menu-icon {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
}
.aside-nav .menu-nav li a:hover,
.aside-nav .menu-nav li a.active {
    background: rgba(231, 192, 116, 0.10);
}
.aside-nav .menu-nav li a:hover .menu-icon,
.aside-nav .menu-nav li a.active .menu-icon {
    color: #E7C074;
}
.aside-nav .menu-nav .menu-text {
    display: none;
    font-weight: 400;
    font-size: 14px;
}
.aside-nav .menu-nav .menu-submenu {
    display: none;
    background: #181C31;
    position: fixed;
    top: 0;
    left: 150px;
    border-radius: 10px;
    z-index: 10;
    width: 200px;
    box-shadow: 0 0 10px rgba(0, 0, 0, .3);
    padding: 8px;
    border: 1px solid #333A56;
}
.aside-nav .menu-nav .menu-submenu ul {
    padding-left: 0;
    list-style: none;
}
.aside-nav .menu-nav .menu-submenu ul li a {
    padding: 0.5rem 1rem;
}
.aside-nav .menu-nav li:hover .menu-submenu {
    display: block;
}

.aside-footer {
    padding: 0 8px;
}
.aside-footer .bottom-bar-item a {
    height: 40px;
    color: #FFF;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 6px;
    margin: 0;
    padding: 0 12px;
}
.aside-footer .bottom-bar-item .svg-icon {
    margin-right: 0;
    display: flex;
    align-items: center;
}
.aside-footer .bottom-bar-item i {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    padding-right: 0;
}
.aside-footer .bottom-bar-item a:hover {
    background: rgba(231, 192, 116, 0.10);
}
.aside-footer .bottom-bar-item a.btn-dropdown:hover {
    background: transparent;
}
.aside-footer .bottom-bar-item a:hover i {
    color: #E7C074;
}
.aside-footer .bottom-bar-item a.btn-dropdown {
    padding: 0 4px;
}
.aside-footer .bottom-bar-item a.btn-dropdown iconify-icon {
    font-size: 32px;
}
.aside-footer .bottom-bar-item .menu-text {
    display: none;
    font-weight: 400;
    font-size: 14px;
}
.aside-footer .bottom-bar .dropdown {
    margin-top: 20px;
}
.aside-footer .bottom-bar .dropdown-menu {
    background: transparent;
    border-radius: 0;
    z-index: 10;
    width: 200px;
    box-shadow: none;
    padding: 0;
    border: none;
    overflow: hidden;
    transform: none !important;
    top: unset !important;
    left: 54px !important;
    will-change: transform !important;
    bottom: 6px !important;
}
.aside-footer .bottom-bar .dropdown-menu .navi-item {
    border-radius: 6px;
    border: 1px solid #464D6D;
    background: #232739;
    padding: 0;
    color: #ADB5BD;
    font-size: 12px;
    margin-bottom: 2px;
    display: inline-block;
}
.aside-footer .bottom-bar .dropdown-menu .navi-item:hover {
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.10) 100%), #232739;
}
.aside-footer .bottom-bar .dropdown-menu .navi-item a {
    padding: 3px 8px;
}
.aside-footer .bottom-bar .dropdown-menu .navi-text {
    color: #ADB5BD;
    font-size: 12px;
}
.aside-footer .bottom-bar .dropdown-menu .navi-link:hover {
    background: rgba(231, 192, 116, 0.10);
}
.aside-footer .bottom-bar .dropdown-menu .navi-link:hover .navi-icon i,
.aside-footer .bottom-bar .dropdown-menu .navi-link:hover .navi-text {
    color: white;
}


#kt_aside.open {
    width: 158px;
}
#kt_aside.open .aside-nav {
    width: 158px;
}
#kt_aside.open .aside-primary {
    width: 158px;
}
#kt_aside.open .aside-nav .menu-text,
#kt_aside.open .aside-footer .bottom-bar-item .menu-text {
    display: block;
}
@media (min-width: 992px) {
    .aside-fixed.aside-minimize .wrapper {
        padding-left: 56px;
    }
    #kt_aside.open + #kt_wrapper {
        padding-left: 158px;
    }
}


.subheader {
    height: 64px;
    padding: 0 12px;
}

.card.card-custom {
    box-shadow: none;
    border-radius: 6px;
}
.card.card-custom > .card-header .card-title .card-label {
    color: var(--gray-900, #212529);
    font-size: 20px;
    font-weight: 500;
}
.card-no-bg {
    background: transparent !important;
}
.card-no-bg .card-header {
    padding: 24px 0 !important;
}
.card-no-bg .card-body {
    padding: 0 !important;
}


.card-filters {
    background: transparent !important;
    box-shadow: none !important;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-300) !important;
    border-radius: 0!important;
}
.card-filters .card-header {
    flex-wrap: nowrap !important;
    border: none !important;
    min-height: 0;
}
.card-filters .filters {
    width: 100%;
}
.card-filters .card-header #filters .row-filters {
    margin: 0;
    flex-wrap: wrap;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
}
.card-filters .card-header #filters .row-filters .form-group,
.card-filters .card-header #filters .row-buttons .form-group {
    margin-bottom: 0;
}
.card-filters .card-header #filters .row-filters label:not(.checkbox) {
    display: none !important;
}
.card-filters .card-header #filters .row-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 8px !important;
}
.card-filters .card-header .card-actions {
    flex-shrink: 0;
}
.card-filters .filters .checkbox-inline .checkbox {
    white-space: nowrap;
}
.card-filters .symbol-list {
    white-space: nowrap;
}
.form-group.form-group-display + .form-group.form-group-sort {
    border-left: 1px solid var(--gray-400);
    padding-left: 12px;
    margin-left: 12px;
}
.form-group.form-group-display .btn.active {
    background: var(--Astraeos-Astraeos_100, #D6D8DD) !important;
}
.form-group.form-group-sort select {
    height: 24px;
    max-width: 150px;
    padding: 2px 6px;
    font-size: 12px;
    background: transparent;
}


.breadcrumb .breadcrumb-item a {
    color: var(--gray-800, #343A40);
    gap :12px;
    font-weight: 500;
}

.dataTables_wrapper .symbol-group .symbol.symbol-30 > img {
    height: 26px;
    width: 26px;
}
.dataTables_wrapper .dataTable th.sorting_desc,
.dataTables_wrapper .dataTable td.sorting_desc {
    color: var(--blue) !important;
}
div.dataTables_wrapper div.dataTables_info {
    font-size: 12px;
    color: var(--gray-600, #6C757D);
}
div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}
div.dataTables_wrapper div.dataTables_filter label {
    margin-bottom: 0;
}
div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0;
    width: 290px;
    padding: 7px 12px;
    height: 36px;
    border: none;
    border-radius: 4px;
    background: white;
}
div.dataTables_wrapper div.dataTables_length {
    padding-top: 6px;
}
div.dataTables_wrapper div.dataTables_length label {
    margin-bottom: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
div.dataTables_wrapper div.dataTables_length select {
    height: 24px;
    background: transparent;
    border-radius: 6px;
    border: 1px solid var(--gray-400, #CED4DA);
    padding: 2px 12px;
}
.dataTables_wrapper .dataTables_paginate .pagination .page-item.previous > .page-link, .dataTables_wrapper .dataTables_paginate .pagination .page-item.next > .page-link, .dataTables_wrapper .dataTables_paginate .pagination .page-item.last > .page-link, .dataTables_wrapper .dataTables_paginate .pagination .page-item.first > .page-link {
    color: var(--Gray-600);
}
table tbody tr td {
    background: white;
}
.table {
    border-spacing: 0 8px !important;
    border-collapse: initial !important;
}
.table td {
    height: 50px;
    vertical-align: middle;
    border-top: none;
    padding: 10px 14px;
}
.table.dataTable td {
    height: 30px;
}
.table-no-thead table thead {
    display: none;
}
.table thead th {
    border-bottom: none;
    color: #495057;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    padding-top: 12px;
    padding-bottom: 12px;
}
.table th:first-child,
.table td:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}
.table th:last-child,
.table td:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
.table td:last-child a {
    display: none;
}
.table tr:hover td:last-child a {
    display: inline-flex;
}
.table td .btn.btn-icon.btn-sm {
    height: 24px;
    width: 24px;
    padding: 0;
}
.table tr td:last-child a.btn.btn-icon i,
.table tr td:last-child a.btn.btn-icon iconify-icon {
    color: var(--astraeos-400);
    font-size: 16px;
}
.table tr td:last-child a.btn.btn-icon.btn-hover-light-danger i,
.table tr td:last-child a.btn.btn-icon.btn-hover-light-danger iconify-icon {
    color: var(--rouge-400);
}
.table tr td:last-child a.btn.btn-icon:hover {
    border-radius: 4px;
    background: var(--Astraeos-Astraeos_50, #E8EAF0);
}

.card.card-custom.card-view-header {
    box-shadow: none;
    background: transparent;
    padding: 0;
}
.card.card-custom.card-view-header .card-body {
    padding: 0;
}
.card.card-custom.card-view-header .card-body .card-view-header-container {
    height: 42px;
}
.card-view-header .card-view-header-actions button,
.card-view-header .card-view-header-actions .btn-icon,
.card-view-header .card-view-header-actions .btn-icon:focus,
.card-view-header .card-view-header-actions .btn-icon:active,
.card-view-header .card-view-header-actions .btn-icon.active,
.card-view-header .card-view-header-actions .dropdown-toggle {
    border-radius: 4px;
    border: none;
    background: white;
    height: 36px;
}
.card-view-header .card-view-header-actions .btn.btn-light-primary:not(:disabled):not(.disabled):active:not(.btn-text),
.card-view-header .card-view-header-actions .btn.btn-light-primary:not(:disabled):not(.disabled).active,
.card-view-header .card-view-header-actions .show > .btn.btn-light-primary.dropdown-toggle,
.card-view-header .card-view-header-actions .show .btn.btn-light-primary.btn-dropdown {
    border: none;
    background: white;
}

.card-view-header .card-view-header-actions button:hover,
.card-view-header .card-view-header-actions .btn-icon:hover,
.card-view-header .card-view-header-actions button:active,
.card-view-header .card-view-header-actions .btn-icon:active {
    color: var(--Astraeos-Astraeos_300, #333A56) !important;
    border: none !important;
    background: white !important;
}
.card-view-header .card-view-header-actions button:hover i,
.card-view-header .card-view-header-actions .btn-icon:hover i {
    color: var(--Astraeos-Astraeos_300, #333A56) !important;
}
.card-view-header .card-view-header-actions .btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled).dropdown-toggle:after,
.card-view-header .card-view-header-actions .btn.btn-light-primary:focus:not(.btn-text).dropdown-toggle:after,
.card-view-header .card-view-header-actions .btn.btn-light-primary.focus:not(.btn-text).dropdown-toggle:after {
    color: var(--Astraeos-Astraeos_300, #333A56) !important;
}
.card-view-header-title {
    width: 190px;
    border-right: 1px solid var(--gray-400, #CED4DA);
    height: 100%;
}
.card-view-header-title h5 {
    font-size: 20px;
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.card-view-header-subtitle {
    padding: 0 20px;
    border-right: 1px solid var(--gray-400, #CED4DA);
    height: 100%;
    color: var(--gray-800, #343A40);
}
.card-view-header-stats {
    padding: 0 20px;
    height: 100%;
}
.header-stat .header-stat-content > span.header-stat-content-title {
    text-transform: uppercase;
    color: var(--gray-800, #343A40);
    font-size: 12px;
    font-weight: 500;
}


.switch-container {
    display: flex;
    flex-direction: row;
    gap: 12px;
}
.switch-left {
    display: inline-block;
    width: auto;
    vertical-align: top;
    margin-bottom: 0;
}
.switch-right {
    display: inline-block;
    margin-bottom: 0;
}
.switch-right .switch-infos {
    margin-top: 7px;
}
.switch-right .switch-infos label {
    margin-bottom: 0;
    font-weight: 500;
}


.input-group.date-time-picker,
.input-group.date-picker {
    max-width: 250px;
}
.input-group-append.bg-circle {
    width: 44px;
    justify-content: center;
    align-items: center;
}
.input-group-append.bg-circle .input-group-text {
    background: var(--astraeos-400);
    border-radius: 50%;
    height: 20px;
    width: 20px;
    padding: 0;
    line-height: 1;
    color: white;
    align-items: center;
    justify-content: center;
}

.navi-item a.navi-link {
    cursor: pointer;
}
.navi-link.text-danger:hover,
.navi-link.text-danger:hover .navi-icon i,
.navi-link.text-danger:hover .navi-text {
    color: #F64E60 !important;
}
.navi-link.text-success:hover,
.navi-link.text-success:hover .navi-icon i,
.navi-link.text-success:hover .navi-text {
    color: #1BC5BD !important;
}
.navi-link.text-warning:hover,
.navi-link.text-warning:hover .navi-icon i,
.navi-link.text-warning:hover .navi-text {
    color: #FFA800 !important;
}
.navi-link.text-info:hover,
.navi-link.text-info:hover .navi-icon i,
.navi-link.text-info:hover .navi-text {
    color: #8950FC !important;
}

.hidden {
    display: none !important;
}
/*.header-fixed[data-header-scroll="on"] .content {
    padding-top: 0 !important;
}*/
body.card-sticky-on .card-sticky {
    padding-top: 70px;
}

ul.navi.no-wrap li .navi-text {
    white-space: nowrap;
    padding-right: 1rem;
}

td .dropdown .dropdown-menu {
    min-width: 175px;
}

.swal2-icon.swal2-warning {
    color: #F64E60;
    border-color: #F64E60;
}
.swal2-cancel.swal2-styled {
    color: #343A40;
    background-color: #E4E6EF;
    border-color: #E4E6EF;
    font-weight: normal;
}
.swal2-cancel.swal2-styled:hover {
    color: #343A40;
    background-color: #d7dae7;
    border-color: #d7dae7;
}

#topbar_notifications_notifications .notification a {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

#toast-container .toast {
    opacity: 1 !important;
}

.select2-container {
    min-width: 200px;
    max-width: 100%;
}
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    height: 48px;
    border-color: var(--gray-400);
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--gray-800, #343A40);
    font-size: 14px;
}
.select2-container--default .select2-selection--single:hover,
.select2-container--default .select2-selection--multiple:hover {
    border-color: var(--Astraeos-Astraeos_300, #333A56);
}
.select2-container--default .select2-selection--single:focus,
.select2-container--default .select2-selection--multiple:focus,
.select2-container--default.select2-container--open .select2-selection--single {
    border-color: var(--Or-400, #BB8805);
    background-color: var(--Or-50, #F8F3E6);
}
.select2-container--default .select2-selection--single:disabled,
.select2-container--default .select2-selection--multiple:disabled {
    background-color: #F8F9FA;
    border-color: #ADB5BD;
    opacity: 1;
}
.select2-container--default .select2-dropdown {
    border: 1px solid var(--gray-300, #DEE2E6);
    background: #FFF;
    box-shadow: 0px 1px 9px 0px rgba(0, 0, 0, 0.05);
}
.select2-container--default .select2-search--dropdown,
.select2-container--default .select2-results__option {
    padding: 8px;
}

.filters .select2-container {
    min-width: 200px;
    max-width: 200px;
}
.filters .select2-container--default .select2-selection--single {
    background-color: transparent;
}
.filters .select2-container--default.select2-container--open .select2-selection--single {
    background-color: var(--Or-50, #F8F3E6);
}
.select2-container .select2-selection--multiple {
    min-height: 40px;
}
.select2-container .select2-selection--multiple .select2-search__field {
    min-width: 100%;
}
.select2-container .select2-search--inline .select2-search__field {
    margin-top: 0;
}
.modal .select2-container {
    min-width: 100%;
}
.image-input-wrapper {
    display: flex;
}
.image-input img {
    max-width: 100%;
    max-height: 120px;
}
input.form-control.input-sm {
    width: auto;
}
.input-group.input-group-md {
    width: 300px;
}
.input-group.input-group-small {
    width: 200px;
}
.input-group.input-group-small-integer {
    width: 150px;
}

#ckf-modal-header {
    background: #fafafa !important;
}

.border-hover-primary:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #6993FF !important;
}
.border-hover-info:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #8950FC !important;
}
.border-hover-success:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #1BC5BD !important;
}
.border-hover-warning:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #FFA800 !important;
}
.border-hover-danger:hover {
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
    border-color: #F64E60 !important;
}

.topbar {
    gap: 16px;
}
.topbar .topbar-item .btn.btn-icon,
.topbar .topbar-item .btn.btn-icon.btn-lg {
    height: 40px;
    width: 40px;
}
.topbar .topbar-item .btn.btn-icon iconify-icon {
    font-size: 20px;
}
.topbar-icon {
    display: flex;
    align-items: center;
}
.topbar-item.topbar-event .btn.btn-hover-transparent-light:hover {
    color: #F3F6F9 !important;
    background-color: rgba(243, 246, 249, 0.1) !important;
    border-color: transparent !important;
}

.btn-create-modal-content:hover > * {
    opacity: .7;
    cursor: pointer;
}
.btn-create-modal-content:after {
    content: "\f002";
    position: absolute;
    left: 50%;
    top: 50%;
    font-size: 1.25rem;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    opacity: .8;
    background: #B5B5C3;
    border-radius: 50%;
    padding: 10px;
    color: white;
    transform: translateX(-50%) translateY(-50%);
    cursor: pointer;
}
.btn-create-modal-content:hover:after {
    opacity: 1;
}
.swal2-lg {
    width: 50em;
}
.swal2-xl {
    width: 75em;
}

#kt_quick_search_dropdown .card-blank-state {
    box-shadow: none !important;
}
#kt_quick_search_dropdown .blank-state {
    text-align: center;
}
#kt_quick_search_dropdown .blank-state .blank-state-image {
    margin: 30px 0;
}
#kt_quick_search_dropdown .blank-state .blank-state-image img {
    max-height: 100px;
}
#kt_quick_search_dropdown .blank-state p {
    margin: 0 auto 30px auto;
    opacity: 0.8;
    font-size: 16px;
    line-height: 24px;
}


.notification-inbox {
    border-radius: 0 !important;
    border-bottom: 1px solid #ebedf3;
    min-height: 75px;
}
.notification-container {
    width: 100%;
}
.notification-inbox.new .notification-subject {
    font-weight: 600 !important;
}
.notification {
    padding-top: 10px;
}

.apexcharts-legend-marker {
    border-radius: 50% !important;
}

#ModalVideo .modal-content {
    background: transparent;
    box-shadow: none;
}
#ModalVideo .modal-header {
    border: none;
    padding-right: 0;
}
#ModalVideo .modal-header i {
    color: white;
    font-size: 16px;
}
#ModalVideo iframe {
    border-color: black !important;
}
.modal-backdrop.show {
    opacity: .6;
}

/* ckeditor5 media embed */
figure.media {
    display: block;
}

.card-bg {
    background-position: top center;
    height: 200px;
    background-size: cover;
}
.card-bg-icon {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.card-empty {
    background: white;
    box-shadow: none !important;
    color: #272C44;
    font-size: 16px;
    font-weight: 500;
}
.card-empty.border-dashed {
    border: 1px dashed #ebedf3;
}
.card-empty .card-spacer {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.card-document {
    box-shadow: none !important;
}
.card-document .card-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
    background: transparent !important;
}
.card-document .card-header .card-title {
    display: none !important;
}
.card-document .card-header .card-toolbar {
    margin-left: auto !important;
}
.card-document .card-header .card-toolbar iconify-icon {
    color: var(--Astraeos-Astraeos_400);
}
.card-document .card-bg {
    height: 140px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
.card-document .card-spacer {
    padding: 16px !important;
}
.card-document .card-spacer h6 {
    color: var(--gray-900, #212529);
}
.document-view-container {

}
.container-without-left-menu .navi-left-menu-container {
    display: none !important;
}
.container-without-left-menu .flex-row-fluid.ml-lg-8 {
    margin-left: 0 !important;
}
.document-view-container .documents .col-xl-3 {
    flex: 1 1 auto !important;
    max-width: unset !important;
}
.document-view-container .documents .card-empty {
    display: none !important;
}
.document-view-container .documents .card.card-custom {
    border: 1px solid transparent;
}
.document-view-container .documents .card.card-custom.current {
    border: 1px solid var(--Or-400, #BB8805);
    box-shadow: 0px 0px 0px 4px #E6D7B0 !important;
}
.document-view-container .documents .card.card-custom:hover {
    border: 1px solid var(--Or-400, #BB8805);
}
.document-view-container .documents .card.card-custom.card-sm > .card-header {
    display: none !important;
}
.document-view-container .documents .card.card-custom > .card-header + .card-body {
    display: flex;
    flex-direction: row;
}
.document-view-container .documents .card-document .card-bg {
    height: 80px;
    border-radius: 6px 0 0 6px;
    width: 80px;
    min-width: 80px;
}
.document-view-container .documents .card-document .card-spacer {
    padding: 8px 15px 8px 15px !important;
    height: 80px;
}
.document-view-container .documents .card-document .card-spacer h6 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.document-view-container .documents .card-document .card-footer {
    display: none !important;
}

.navi-item .accordion.accordion-toggle-arrow .card .card-header .card-title:after {
    right: 20px;
}
.navi-label.navi-label-rounded .label {
    border-radius: 50%;
    padding: 0.5rem;
    width: 24px;
    height: 24px;
}

.mobile-nav {
    background: #FFFFFF;
    position: fixed;
    bottom: 0;
    height: 78px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 10px;
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
    z-index: 1200;
}
.mobile-nav .mobile-nav-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    color: #1BC5BD;
    flex: 1;
}
.mobile-nav .mobile-nav-item.mobile-nav-item-active {
    color: #6993FF;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon {
    font-size: 2rem;
}
.mobile-nav .mobile-nav-item .mobile-nav-icon svg {
    height: 24px !important;
    width: 24px !important;
}
.mobile-nav .mobile-nav-item .mobile-nav-text {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    text-align: center;
}
@media screen and (min-width: 768px) {
    .mobile-nav {
        display: none;
    }
}
@media screen and (max-width: 767px) {
    #help-icon {
        display: none;
    }
    .flex-root {
        padding-bottom: 80px;
    }
    .offcanvas-mobile {
        height: calc(100% - 78px);
    }
}

.form-group {
    position: relative;
    margin-bottom: 16px;
}
.form-group label {
    color: var(--Astraeos-Astraeos_400, #181C31);
}
.form-group .controls {
    position: relative;
}
.form-group .toggle-password {
    position: absolute;
    right: 35px;
    top: 0;
    cursor: pointer;
    height: 100%;
    display: flex;
    align-items: center;
}
.form-group .controls label {
    position: absolute;
    cursor: text;
    z-index: 3;
    top: 0;
    left: 0;
    transform: translate(10px, 14px);
    font-size: 14px;
    font-weight: 400;
    background: #fff;
    padding: 0 10px;
    color: var(--gray-700, #495057);
    transition: all .3s ease;
    border-radius: 4px;
    white-space: nowrap;
}
.form-group .controls label.relative {
    position: relative;
    transform: none;
    padding: 0;
    margin-bottom: 10px;
    background: transparent;
}
.form-group label .label-required {
    color: var(--rouge-400, #E95822);
    margin-left: 4px;
    background: transparent;
    width: auto;
    height: auto;
    font-size: 14px;
}
.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group input:not(:placeholder-shown) + div + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label,
.form-group textarea:not(:placeholder-shown) + div + label,
.form-group select + label,
.form-group .controls select + label,
.form-group .select2-container + label,
.form-group .ck + label {
    font-size: 12px;
    transform: translate(6px,-8px);
}
.form-group input:focus + label {
    color: var(--Or-400, #BB8805);
}
.form-control {
    border-color: var(--gray-400);
    height: 48px;
    padding: 16px;
    color: var(--gray-800, #343A40);
    font-size: 14px;
}
select.form-control {
    padding: 10px 16px;
}
.form-control:hover {
    border-color: var(--Astraeos-Astraeos_300, #333A56);
}
.form-control:focus {
    border-color: var(--Or-400, #BB8805);
    background-color: var(--Or-50, #F8F3E6);
}
.form-control:disabled {
    background-color: #F8F9FA;
    border-color: #ADB5BD;
    opacity: 1;
}

.input-group {
    border: 1px solid var(--gray-400);
    border-radius: 4px;
}
.input-group:hover {
    border-color: var(--Astraeos-Astraeos_300, #333A56);
}
.input-group input {
    border: none;
    height: 46px;
}
.input-group-text {
    background: transparent;
}
.input-group-prepend .input-group-text,
.input-group-append .input-group-text {
    border: none;
}

.navi .navi-item .navi-text {
    padding-right: 10px;
}

div.controls {
    position: relative;
}
.address_results {
    border-radius: 4px;
    background: white;
    padding: 10px 0;
    width: 100%;
    max-height: 250px;
    position:absolute;
    top: calc(100% + 10px);
    display: none;
    overflow-y: auto;
    box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, .1);
    z-index: 1;
}
.address_results li {
    padding: 10px 20px;
    cursor: pointer;
    list-style: none;
}
.address_results li:hover {
    background-color: var(--blue);
    color: white;
}

.filters .checkbox-list .checkbox > span {
    background-color: white;
    border: 1px solid #D1D3E0;
}
.filters .checkbox-list .checkbox > input:checked ~ span {
    background-color: var(--blue);
    border: none;
}

.show-more p {
    margin-bottom: 0;
}
.show-more.stretched .show-more-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 25;
    overflow: hidden;
    position: relative;
}


.dropzone.dz-started .select-document {
    display: none;
}


.fc .fc-event-danger .fc-list-event-dot {
    border-color: #F64E60 !important;
}
.fc .fc-event-facts-done .fc-list-event-dot {
    border-color: #1BC5BD !important;
}
.fc .fc-event-convocations-done .fc-list-event-dot {
    border-color: #0E2072 !important;
}

.card.card-custom.card-document > .card-header {
    min-height: 0;
    padding: 10px;
}
.card.card-custom.card-document > .card-header h3 {
    font-size: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin: 0;
}
.card.card-custom.card-document .card-body {
    padding: 0;
}

.card-icon-bg {
    background: var(--var-50);
    padding: 4px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    width: 32px;
    height: 32px;
}
.card-icon-bg i, .card-icon-bg iconify-icon {
    color: var(--Or-400, #BB8805);
}
.card-icon-bg iconify-icon {
    font-size: 20px;
}
.card.card-custom .card-header .card-toolbar [data-card-tool="toggle"] i {
    color: var(--astraeos-400);
}
.card.card-custom.card-collapsed .card-header .card-toolbar [data-card-tool="toggle"] i,
.card.card-custom.card-collapse .card-header .card-toolbar [data-card-tool="toggle"] i {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
}

.card-row-container {
    display: flex;
    flex-direction: row;
    gap: 24px;
    width: 100%;
}
.card-row-title {
    width: 240px;
    min-width: 240px;
    border-right: 1px solid var(--gray-300);
    display: flex;
    flex-direction: column;
    gap: 8px;
}
@media (max-width: 1199px) {
    .card-row-title {
        width: 200px;
        min-width: 200px;
    }
}
.card-row-title.card-row-title-inline {
    width: auto;
    min-width: auto;
    border-right: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.card-row table tr td {
    vertical-align: top;
}
.card-row table tr td {
    color: var(--gray-600, #6C757D);
    font-size: 14px;
    font-weight: 400;
}
.card-row table tr td:first-child {
    padding-right: 20px;
    font-weight: 500;
    color: var(--gray-900, #212529);
}
.card-row table tr:hover,
.card-row table tr td:hover {
    box-shadow: none;
}
.card-row .btn-icon {
    border: none;
}
.card-row-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: auto;
}

.home-card-person {
    height: 100%;
}
.home-card-person .card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: calc(100% - 24px);
}
.home-card-person .card-row-container {
    flex-direction: column;
}
.home-card-person .card-row-title {
    border-right: none;
    flex-direction: row;
    align-items: center;
}
.home-card-person .card-row-actions {
    position: absolute;
    top: 24px;
    right: 24px;
}
.home-card-person .card-row-actions .btn-light-danger {
    display: none;
}

.minicolors-theme-bootstrap .minicolors-input-swatch {
    width: 48px !important;
    height: 48px !important;
}

@media (max-width: 767px) {
    .header-actions-mobile {
        position: absolute;
        top: 2rem;
        right: 2rem;
        z-index: 1;
    }
}

.dropzone .dz-preview .dz-details .dz-rotate {
    opacity: 0;
}
.dropzone .dz-preview.dz-image-preview .dz-details .dz-rotate {
    opacity: 1;
}
.dropzone .dz-preview .dz-details .dz-rotate {
    margin-top: 20px;
    display: inline-block;
}
.dropzone .dz-preview .dz-details .dz-rotate a {
    margin: 0 5px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 5px 10px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.9);
}
.dropzone .dz-preview .dz-details .dz-rotate a i {
    cursor: pointer;
}

@media (max-width: 767px) {
    .modal {
        max-height: calc(100vh - 100px);
    }
}


#modalNotes .dropdown {
    display: none;
}
.scrolltop {
    bottom: 100px;
}


.card.card-custom.card-sm > .card-header {
    min-height: 50px;
    padding: 0 15px !important;
}
.card.card-custom.card-sm > .card-header .card-title {
    margin: 0;
    max-width: 225px;
}
.card.card-custom.card-sm > .card-header .card-title .card-label {
    font-size: 1rem;
}
.card.card-custom.card-sm .card-spacer .btn {
    padding: 0.55rem 0.75rem;
    font-size: 0.925rem;
    line-height: 1.35;
    border-radius: 0.42rem;
}

.label.label-outline-light {
    background-color: transparent;
    color: #a7a7a7;
    border: 1px solid #a7a7a7;
}

.radio-bordered {
    border: 1px solid #d9dade;
    padding: 8px 12px;
    border-radius: .42rem;
}
.radio-inline .radio-bordered {
    margin-bottom: 1rem;
}
.navi .navi-item .navi-link .navi-text {
    display: flex;
    align-items: center;
    flex-grow: 0;
}
.accordion .card .card-header .card-title .card-label {
    display: flex;
    align-items: center;
}
.accordion .card .card-header .card-title {
    font-weight: 500;
    color: var(--gray-900, #212529);
}


.modal-form .card .card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2.25rem;
}
.modal-form .card .card-header .card-title {
    margin: 0;
}
.modal-form .card .card-header .card-title h3 {
    font-weight: 500;
    font-size: 1.275rem;
    color: #181C32;
    margin-bottom: 0;
}


#table_dossiers .label.label-primary {
    min-height: 20px;
    height: auto;
}


.object-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid var(--gray-400, #CED4DA);
    width: 100%;
}
.object-submenu .nav-link {
    display: block;
    border-bottom: 2px solid transparent;
    margin-bottom: -1px;
    padding: 10px 14px;
    color: var(--gray-600, #6C757D);
    font-size: 12px;
    font-weight: 400;
    white-space: nowrap;
}
.object-submenu .nav-link.active {
    color: var(--gray-800, #343A40) !important;
    font-weight: 500;
    border-bottom-color: var(--Or-400, #BB8805);
}
.object-submenu .nav-link:hover {
    color: var(--gray-800, #343A40) !important;
}
.object-submenu .nav-item .dropdown-menu {
    font-size: 12px;
}
.object-submenu .nav-item .dropdown-menu .dropdown-item {
    padding: 6px 12px;
}

.navi-left-menu-container {
    width: 288px;
    min-width: 288px;
}
.navi.navi-left-menu .navi-item {
    margin-bottom: 20px;
}
.navi.navi-left-menu .navi-item .navi-link {
    padding: 0;
    color: var(--gray-600, #6C757D);
    font-size: 12px;
    font-weight: 400;
}
.navi .navi-item .navi-link .navi-icon {
    flex: 0 0 16px;
    margin-right: 12px;
}
.navi .navi-item .navi-link .navi-icon i {
    font-size: 16px;
    color: var(--Astraeos-Astraeos_50, #E8EAF0);
}
.navi .navi-item .navi-link.active .navi-icon i {
    color: var(--bleu-100, #CCE6EF);
}
.navi.navi-left-menu .navi-item .navi-text {
    color: var(--gray-600, #6C757D);
    font-size: 12px !important;
    font-weight: 400 !important;
}
.navi .navi-item .navi-link.active .navi-text {
    font-weight: 600 !important;
}
.navi.navi-left-menu.navi-active .navi-item .navi-link.active,
.navi.navi-left-menu.navi-hover .navi-item .navi-link:hover {
    background: transparent;
}


.card.card-custom > .card-header {
    min-height: 0;
    border-bottom: none;
    padding: 24px;
}
.card.card-custom > .card-header .card-toolbar {
    margin: 0;
}
.card.card-custom > .card-body {
    padding: 24px;
}
.card.card-custom > .card-header + .card-body {
    padding-top: 0;
}
.card.card-custom > .card-body .form-group:last-child {
    margin-bottom: 0;
}
.card.card-custom > .card-footer {
    padding: 24px;
}




.card.card-custom.card-rooms > .card-body {
    padding: 8px;
}
.card.card-custom.card-rooms .active {
    border-radius: 6px;
    background: var(--gray-100, #F8F9FA);
    padding: 10px;
}
.user-name {
    color: var(--dark-grey, #303030);
    font-size: 16px;
    font-weight: 400;
    line-height: 22px; /* 137.5% */
}
.card.card-custom.card-rooms .active .user-name {
    font-weight: 600;
}
.user-title {
    color: var(--Astraeos-Astraeos_300, #333A56);
    font-size: 12px;
    font-weight: 400;
    line-height: 18px; /* 150% */
}
.card.card-custom.card-rooms .active .user-title {
    font-weight: 500;
}
.last-message-date {
    color: var(--dark-grey, #303030);
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
}
.card.card-custom.card-rooms .active .last-message-date {
    font-weight: 600;
}

#kt_chat_content {

}
#kt_chat_content .card-footer {
    border-top: none;
}
#kt_chat_content .card-room-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    padding: 12px 6px;
    border-radius: 8px;
    border: 1px solid var(--Astraeos-Astraeos_100, #D6D8DD);
}
#kt_chat_content .btn.dropzone-select {
    color: #919191;
}
#kt_chat_content .btn.chat-send {
    width: 40px;
    min-width: 40px;
    height: 40px;
    padding: 0;
    margin-top: -20px;
    margin-right: -4px;
}
#kt_chat_content textarea:focus {
    background: transparent;
}

#kt_chat_content .message .symbol > img {
    width: 100%;
    max-width: 32px;
    height: 32px;
}
#kt_chat_content .message .message-content {
    border-radius: 12px 12px 0px 12px;
    background: var(--Or-100, #F1E7CD);
    padding: 12px;
    color: var(--Astraeos-Astraeos_400, #181C31);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 128.571% */
    margin-bottom: 8px;
}
#kt_chat_content .message .message-infos {
    margin-bottom: 5px;
}
#kt_chat_content .message .message-time {
    color: var(--gray-700, #495057);
    font-size: 10px;
}
#kt_chat_content .message .message-user-name {
    color: var(--Astraeos-Astraeos_400, #181C31);
    font-size: 12px;
}
#kt_chat_content .message .message-content.bg-light-primary {
    background: var(--Or-100, #F1E7CD) !important;
}
#kt_chat_content .message .message-content.bg-light-success {
    border-radius: 12px 12px 12px 0px;
    background: var(--Astraeos-Astraeos_50, #E8EAF0) !important;
}


.switch input:empty ~ span {
    height: 32px;
    margin: 0;
    width: 52px;
}
.switch input:empty ~ span:before {
    background: var(--Astraeos-Astraeos_300, #333A56);
}
.switch input:checked ~ span:before {
    background: var(--vert-400, #049753);
}
.switch:hover input ~ span:before {
    box-shadow: 0px 0px 0px 4px #E6D7B0;
}
.switch input:empty ~ span:after {
    top: 4px;
    margin-left: 4px;
}
.switch input:checked ~ span:after {
    margin-left: 26px;
}
.switch input:empty ~ span:after,
.switch input:checked ~ span:after {
    background-color: #FFF;
    opacity: 1;
}

.switch-sm input:empty ~ span {
    margin: 0;
    height: 24px;
    width: 42px;
    border-radius: 12px;
}
.switch-sm input:empty ~ span:before {
    width: 42px;
    border-radius: 12px;
}
.switch-sm input:checked ~ span:after {
    margin-left: 20px;
}
.switch-sm input:empty ~ span:after {
    height: 20px;
    width: 20px;
    top: 2px;
    margin-left: 2px;
}
.switch-sm input:checked ~ span:after {
    margin-left: 20px;
}
.switch-sm .switch-right .switch-infos {
    margin-top: 2px;
}

.switch-xs input:empty ~ span {
    margin: 0;
    height: 20px;
    width: 34px;
    border-radius: 12px;
}
.switch-xs input:empty ~ span:before {
    width: 34px;
    border-radius: 12px;
}
.switch-xs input:checked ~ span:after {
    margin-left: 20px;
}
.switch-xs input:empty ~ span:after {
    height: 16px;
    width: 16px;
    top: 2px;
    margin-left: 2px;
}
.switch-xs input:checked ~ span:after {
    margin-left: 16px;
}
.switch-xs .switch-right .switch-infos {
    margin-top: 0;
}

.checkbox > span {
    background-color: #FFF;
    border: 1px solid var(--Or-400, #BB8805);
}
.checkbox:hover > span {
    background-color: var(--Astraeos-Astraeos_50, #E8EAF0);
}
.checkbox > input:checked ~ span {
    background: var(--Or-400, #BB8805);
}
.checkbox > input:disabled ~ span {
    opacity: 0.3;
    background: var(--gray-0, #FFF);
}
.checkbox > input:focus ~ span {
    box-shadow: 0px 0px 0px 4px #E6D7B0;
}


.radio > span {
    width: 20px;
    height: 20px;
    border: 2px solid var(--Or-400, #BB8805);
    background-color: #FFF;
    margin-right: 12px;
}
.radio:hover > span {
    background: var(--Astraeos-Astraeos_50, #E8EAF0);
}
.radio input:focus ~ span {
    box-shadow: 0px 0px 0px 4px #E6D7B0;
}
.radio input:disabled ~ span {
    border: 2px solid var(--Or-400, #BB8805);
    opacity: 0.3;
    background: var(--gray-0, #FFF);
}
.radio > input:checked ~ span {
    background-color: var(--Or-400, #BB8805);
}
.radio > span:after {
    width: 8px;
    height: 8px;
}


.radio-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.radio-group label.radio {
    flex: 1;
    border: 1px solid var(--gray-500, #ADB5BD);
    border-right: none;
    background: #FFF;
    padding: 12px 24px;
    border-radius: 0;
    align-self: stretch;
}
.radio-group label.radio:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}
.radio-group label.radio:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border-right: 1px solid var(--gray-500, #ADB5BD);
}


.dropdown-menu > .dropdown-item .menu-icon {
    width: 30px;
}
.quick-search .quick-search-form .input-group {
    border: none;
}


.form-group-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 16px;
    align-items: flex-start;
}
.form-group-grid .form-group {
    margin-bottom: 0;
}
.form-group-grid.form-group-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}
.form-group-grid.form-group-grid-4 {
    grid-template-columns: repeat(4, 1fr);
}
.form-group-grid.grid-auto {
    grid-template-columns: auto 1fr;
}
.form-group-grid.form-group-grid-3.grid-auto {
    grid-template-columns: auto auto auto;
}
.form-group-grid.form-group-grid-4.grid-auto {
    grid-template-columns: auto auto auto auto;
}
/*.form-panel .form-group-grid {
    grid-template-columns: 1fr !important;
}*/

.form-separator {
    height: 1px;
    width: 100%;
    background: var(--gray-300);
    margin-bottom: 16px;
}

.form .card-body div:last-child {
    margin-bottom: 0;
}


.cards-stats {
    display: flex;
    flex-wrap: wrap;
    column-gap: 48px;
    row-gap: 24px;
}
.card-stat {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    background: transparent;
    border: none;
}
.card-stat:hover {
    background: transparent;
}
.card-stat-body {
    display: flex;
    align-items: center;
    gap: 16px;
}
.card-stat-body .card-stat-icon {
    border-radius: 6px;
    background: rgba(231, 192, 116, 0.30);
    padding: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.card-stat-body .card-stat-icon i,
.card-stat-body .card-stat-icon iconify-icon {
    color: #BB8805;
}
.card-stat-value {
    color: #333A56;
    font-size: 36px;
    line-height: 1;
}
.card-stat-value.text {
    font-size: 22px;
    font-weight: 500;
}
.card-stat-footer,
.card-stat-footer span,
.card-stat-footer a {
    color: #495057;
    font-size: 12px;
    font-weight: 400;
}


.cards-stats.cards-stats-inline {
    flex-direction: row;
    flex-wrap: nowrap;
    column-gap: 20px;
}
.card-stat-inline {
    border-radius: 6px;
    background: #FFF;
    flex-direction: row;
    padding: 20px 24px;
    align-items: center;
    gap: 20px;
    justify-content: space-between;
}
.card-stat-flex {
    flex: 1;
}
.card-stat-inline .card-stat-body {
    order: 2;
}
.card-stat-inline .card-stat-value {
    font-size: 20px;
    font-weight: 500;
}

.card-stats-big {
    display: flex;
    flex-direction: column;
    gap: 24px;
}
.card-stat-big {
    border-radius: 6px;
    background: white;
    padding: 20px 24px;
    border: none;
}
.card-stat-big .card-stat-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}
.card-stat-big .card-stat-value {
    font-size: 24px;
    font-weight: 500;
}

.cards-stats-composed {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.cards-stats-composed .cards-stats {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    column-gap: 0;
    row-gap: 12px;
}
.card-stat-composed {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 24px;
    border-radius: 6px;
    background: #FFF;
}
.card-stat-composed:hover {
    background: white;
}
.card-stat-composed .card-stat-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
    width: 100%;
}
.card-stat-composed .card-stat-header .card-stat-icon {
    border-radius: 6px;
    background: rgba(231, 192, 116, 0.30);
    padding: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.card-stat-composed .card-stat-header .card-stat-icon i,
.card-stat-composed .card-stat-header .card-stat-icon iconify-icon {
    color: #BB8805;
    font-size: 20px;
}
.card-stat-composed .card-stat-header .card-stat-title {
    color: var(--gray-900, #212529);
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}
.card-stat-composed .card-stat-header .card-stat-action {
    margin-left: auto;
}
.card-stat-composed .card-stat-header + .card-stat-body {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    flex-wrap: wrap;
}
.card-stat-composed .card-stat-body .card-stat {
    padding-left: 12px;
    border-radius: 0;
    border-left: 1px solid #CED4DA;
    flex: 0 1 calc(25% - 12px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 90px;
}
.card-stat-composed .card-stat-body .card-stat:first-child {
    border-left: none;
    padding-left: 0;
}
.card-stat-composed .card-stat-body .card-stat:last-child {

}
.card-stat-composed .card-stat-body .card-stat .card-stat-body {
    order: 2;
    max-width: 100%;
}
.card-stat-composed .card-stat-body .card-stat .card-stat-body .card-stat-value {
    color: var(--gray-800, #343A40);
    font-size: 16px;
    font-weight: 500;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.card-stat-composed .card-stat-body .card-stat .card-stat-footer {
    color: var(--gray-700, #495057);
    font-size: 12px;
    font-weight: 500;
}



.filters .form-control {
    min-width: 200px;
}
.filters .checkbox-inline .checkbox {
    margin-bottom: 0;
}


.progress {
    background: var(--gray-300);
}
.progress.progress-or .progress-bar {
    background: var(--Or-400);
}


.label-status:before {
    content: " ";
    margin-right: 8px;
    background: var(--astraeos-300);
    vertical-align: middle;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    font-weight: 400;
}
.label-status.label-outline-success:before,
.label-status.label-light-success:before {
    background: var(--vert-400, #049753);
}
.label-status.label-outline-warning:before,
.label-status.label-light-warning:before {
    background: var(--jaune-400, #FDCB47);
}
.label-status.label-outline-danger:before,
.label-status.label-light-danger:before {
    background: var(--rouge-400, #E95822);
}
.label-status.label-outline-info:before,
.label-status.label-light-info:before {
    background: var(--bleu-400, #0281B1);
}
.label-status.label-no-border {
    border: none;
}


.request-section {
    padding-bottom: 20px;
    margin-bottom: 20px;
}
.request-section-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2px;
    margin-bottom: 20px;
}
.request-section-header h4 {
    margin: 0;
    color: var(--gray-900, #212529);
    font-size: 20px;
    font-weight: 500;
}
.request-section .card-user-page .card-toolbar {
    margin-left: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
}
.request-section .card-user-page .card-header .card-title {
    padding: 4px 24px;
}
.request-section .navi .navi-item .navi-link {
    padding: 0.25rem 1.5rem;
}
.request-section .navi .navi-item .navi-link .navi-right {
    margin-left: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
}
.request-section .navi .navi-item .navi-link .navi-right span {
    align-self: center;
}


body.conference-iframe #kt_header_mobile,
body.conference-iframe #kt_aside,
body.conference-iframe .subheader,
body.conference-iframe .card.card-custom.card-view-header {
    display: none !important;
}
body.conference-iframe.header-mobile-fixed .wrapper {
    padding-top: 0 !important;
}
body.conference-iframe.aside-fixed.aside-minimize .wrapper {
    padding-left: 0 !important;
}
body.conference-iframe .container {
    width: 100% !important;
    max-width: 100% !important;
}
