#custom_calendar_container {
    background: #ffffff;
    border-radius: 10px;
    margin-bottom: 20px;
}
#custom_calendar_header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 24px;
}
#custom_calendar_header h4 {
    font-weight: 600;
    color: var(--blue);
    flex-grow: 1;
    font-size: 18px;
    margin: 0;
}
#prev_month {
    margin-left: auto;
    margin-right: 20px;
}
#custom_calendar_header button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: #ffffff;
    color: var(--blue);
}
#custom_calendar_header button:hover {
    color: var(--Astraeos-Astraeos_400, #181C31);
}

#custom_calendar_weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 12px;
    padding: 0 24px;
}
.weekday {
    text-align: center;
    font-weight: 500;
    font-size: 12px;
    color: var(--gray-800, #343A40);
    padding: 8px 0;
    text-transform: uppercase;
}

#custom_calendar_days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    padding: 0 24px 24px 24px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 400;
    font-size: 16px;
    color: var(--gray-900, #212529);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    background: transparent;
}
.calendar-day:not(.empty):hover {
    background: var(--Or-50, #F8F3E6);
    cursor: pointer;
}

.calendar-day.empty {
    cursor: default;
}

.calendar-day.today {
    background: var(--Or-50, #F8F3E6);
}

.calendar-day.selected {
    background: var(--astraeos-400, #181C31) !important;
    color: #D1B06F !important;
    font-weight: 600 !important;
    border: none !important;
}

/* Styles pour les jours avec des événements */
.calendar-day.has-events {
    border: 1px solid #D1B06F;
    background: transparent;
}

.calendar-day.has-events:hover {
    background: var(--Or-50, #F8F3E6);
    border-color: #D1B06F;
}

#custom_calendar_footer {
    padding: 20px 24px;
    border-top: 1px solid var(--gray-300, #DEE2E6);
    text-align: center;
}
#custom_calendar_footer a {
    color: #21253C;
    font-size: 16px;
}


#kt_calendar {
    border-radius: 10px;
    background: var(--gray-0, #FFF);
    padding: 24px;
}
#kt_calendar .fc-toolbar-title {
    text-transform: none;
    color: var(--Astraeos-Astraeos_300, #333A56);
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
}
#kt_calendar .fc-header-toolbar .fc-button-group,
#kt_calendar .fc-header-toolbar button {
    display: none;
}
#kt_calendar.fc-theme-standard .fc-scrollgrid {
    border: none;
}
#kt_calendar.fc-theme-standard .fc-scrollgrid thead {
    display: none;
}
#kt_calendar.fc-theme-standard .fc-scrollgrid tbody .fc-scrollgrid-sync-table {
    display: none;
}
#kt_calendar.fc-theme-standard .fc-cell-shaded {
    display: none;
}
#kt_calendar.fc-theme-standard .fc-scrollgrid-section.fc-scrollgrid-section-body:not(.fc-scrollgrid-section-liquid) {
    display: none;
}
#kt_calendar.fc-theme-standard .fc td {
    border-style: dashed;
    border-width: 1px;
    padding: 0;
    border-right: none;
    border-left: none;
}


/* Responsive */
@media (max-width: 768px) {
    #custom_calendar_container {
        padding: 15px;
    }
    .calendar-day {
        font-size: 12px;
    }
    #custom_calendar_header h4 {
        font-size: 16px;
    }
    .weekday {
        font-size: 10px;
        padding: 6px 0;
    }
}

/* Ajustements pour le calendrier FullCalendar en mode jour */
#kt_calendar .fc-toolbar-title {
    font-size: 1.1rem;
}
#kt_calendar .fc-timegrid-col {
    min-width: 100px;
}
#kt_calendar .fc-timegrid-axis {
    width: 60px;
}
