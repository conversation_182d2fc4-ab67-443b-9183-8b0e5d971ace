.accordion.accordion-svg-toggle .card .card-header .card-title .card-label .svg-icon svg {
    -webkit-transform: rotate(0deg) !important;
    transform: rotate(0deg) !important;
    margin-right: 1rem;
    font-size: 1.4rem;
    color: #6993FF;
}

.accordion .card .card-header .card-title .card-label {
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.accordion .card .card-header .card-title .card-label > i {
    margin-right: 1rem;
    font-size: 1.4rem;
    color: #6993FF;
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
}
.accordion .card .card-header .card-title.collapsed .card-label > i {
    color: #B5B5C3;
    -webkit-transition: all 0.15s ease;
    transition: all 0.15s ease;
}

.accordion .card .card-header .card-title .svg-icon svg g [fill] {
    -webkit-transition: fill 0.3s ease;
    transition: fill 0.3s ease;
    fill: #6993FF !important;
}
.accordion .card .card-header .card-title.collapsed .svg-icon svg g [fill] {
    fill: #B5B5C3 !important;
}

.progression {
    flex: 1;
}

.wizard-step:hover .wizard-title {
    color: #6993FF !important;
}
.wizard.wizard-5 .wizard-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon svg g [fill] {
    fill: #6993FF !important;
}
.wizard.wizard-5 .wizard-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-title {
    color: #6993FF !important;
}


.accordion .card .card-body {
    padding: 2rem 2.25rem;
}
.accordion.accordion-panel .card .card-body {
    padding-top: 2rem;
}
.accordion .card .card-body .card {
    background: transparent;
    box-shadow: none;
}
.accordion .card .card-body .card .card-header {
    padding: 0;
}
.accordion .card .card-body .card .card-body {
    padding: 0;
}

.accordion .card .card-body form .card .card-header {
    order: 2;
    min-height: 0;
    padding: 0;
}
.accordion .card .card-body .card-header .card-title {
    opacity: 0;
}
.accordion .card .card-body form .card-header .card-title {
    display: none;
}
.accordion .card .card-body form .card-header .card-toolbar a.btn-light-primary {
    display: none;
}
.accordion .card .card-body form .card-toolbar {
    margin-top: 0;
}
.accordion .card .card-body form .card .card-body {
    padding: 0;
}
.accordion .card .card-body form .card .form-group:last-of-type {
    margin-bottom: 1rem;
}


.row-integrations .card {
    margin-bottom: 25px !important;
}
.row-integrations .card-body {
    border: 1px solid #F3F6F9 !important;
    padding: 2rem 2.25rem !important;
}
