/* Bank Accounts Styles */

.bank-logo {
    max-height: 24px;
    width: auto;
    border-radius: 4px;
}

.account-card {
    transition: all 0.2s ease;
    cursor: pointer;
    border: 2px solid #dee2e6;
}

.account-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
    border-color: #007bff;
}

.account-card.border-success {
    background-color: #f8fff9;
    border-color: #28a745 !important;
}

.account-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.balance-positive {
    color: #28a745 !important;
}

.balance-negative {
    color: #dc3545 !important;
}

.sync-status {
    font-size: 0.875rem;
}

.sync-status.never-synced {
    color: #ffc107;
}

.sync-status.recently-synced {
    color: #28a745;
}

.sync-status.needs-sync {
    color: #fd7e14;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.empty-state {
    padding: 3rem 1rem;
}

.empty-state i {
    opacity: 0.5;
}

.modal-lg {
    max-width: 900px;
}

.transaction-item {
    border-left: 4px solid #dee2e6;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0.375rem 0.375rem 0;
}

.transaction-item.positive {
    border-left-color: #28a745;
    background-color: #f8fff9;
}

.transaction-item.negative {
    border-left-color: #dc3545;
    background-color: #fff8f8;
}

.transaction-amount {
    font-weight: 600;
    font-size: 1.1rem;
}

.transaction-date {
    color: #6c757d;
    font-size: 0.875rem;
}

.transaction-description {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.transaction-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-powens {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #0d47a1;
}

.alert-powens .alert-link {
    color: #0d47a1;
    text-decoration: underline;
}

.powens-branding {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.powens-branding a {
    color: #2196f3;
    text-decoration: none;
}

.powens-branding a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .account-card {
        margin-bottom: 1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.375rem 0.75rem;
        margin-bottom: 0.25rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .bank-logo {
        max-height: 20px;
    }
}

/* Print styles */
@media print {
    .btn, .btn-group {
        display: none !important;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
    
    .table {
        font-size: 0.875rem;
    }
}
