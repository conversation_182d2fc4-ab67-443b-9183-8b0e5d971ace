.note-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}
.note-labels .label {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid var(--gray-200, #E9ECEF);
    background: var(--Or-400, #BB8805);
}
.note-labels .label.active {
    box-shadow: 0px 0px 0px 4px #E6D7B0;
}
.note-labels .label.label-bleu {
    background: var(--bleu-400, #0281B1);
}
.note-labels .label.label-vert {
    background: var(--vert-400, #049753);
}
.note-labels .label.label-rouge {
    background: var(--rouge-400, #E95822);
}
.note-labels .label.label-jaune {
    background: var(--jaune-400, #FDCB47);
}
.note-labels .label.label-orange {
    background: #FEA800;
}
.note-labels .label.label-violet {
    background: #8950FC;
}
.note-labels .label.label-or {
    background: var(--Or-400, #BB8805);
}

#notes {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.note {
    border-radius: 6px;
    padding: 16px;
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: flex-start;
    background-color: white;
    border-left: 8px solid white;
}
.note.note-label-bleu {
    border-left-color: var(--bleu-400, #0281B1);
}
.note.note-label-vert {
    border-left-color: var(--vert-400, #049753);
}
.note.note-label-rouge {
    border-left-color: var(--rouge-400, #E95822);
}
.note.note-label-jaune {
    border-left-color: var(--jaune-400, #FDCB47);
}
.note.note-label-orange {
    border-left-color: #FEA800;
}
.note.note-label-violet {
    border-left-color: #8950FC;
}
.note.note-label-or {
    border-left-color: var(--Or-400, #BB8805);
}
.note .note-header {
    display: flex;
    align-items: center;
    gap: 15px;
}
.note .note-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.note-grab i {
    color: #85899A;
    cursor: move;
}
.note .note-header .note-user {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-900, #212529);
}
.note .note-header .note-date {
    font-size: 12px;
    color: var(--gray-600, #6C757D);
}
.note .note-body {
    font-size: 12px;
    line-height: 1.5;
    color: var(--gray-800, #343A40);
}
.note .note-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
.note .note-actions .btn {

}
