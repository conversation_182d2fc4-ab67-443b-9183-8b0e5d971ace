.payments .payment {
    margin-bottom: 30px;
    background: white;
    border-radius: 6px;
    box-shadow: 0px 2px 10px 0 rgba(0, 0, 0, 0.08);
    padding: 20px;
    border: 5px solid transparent;
}
.payments .payment.active {
    border: 5px solid #403d50;
}
.payments .payment-header {
    padding: 10px 0;
}
.payments .payment-content {
    display: block;
    position: relative;
    padding: 0px;
    margin-bottom: 0;
    text-align: center;
}
.payments .payment-content img {
    width: 100%;
    border-radius: 10px;
    opacity: .5;
    max-height: 50px;
    width: auto;
    max-width: 100%;
}
.payments .payment.active img.pic,
.payments .payment:hover img.pic {
    opacity: 1;
}
