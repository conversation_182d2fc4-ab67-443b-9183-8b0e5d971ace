@keyframes kt_help_modal_animation {
    0% {
        opacity: 0;
        bottom: -55px
    }
    100% {
        opacity: 1;
        bottom: 100px
    }
}

#btnHelp {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
    transition: all 0.3s;
}
#btnHelp .btn {
    width: 60px;
    height: 60px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.06),0 3px 32px rgba(0,0,0,0.16);
}
@media (max-width: 767px) {
    #btnHelp {
        bottom: 90px;
        right: 20px;
    }
    #btnHelp .btn {
        width: 50px;
        height: 50px;
    }
}

#kt_help_modal {
    bottom: 20px;
    left: 120px;
    max-width: 400px;
    animation: kt_help_modal_animation 0.4s;
    transition: all 0.4s;
}
#kt_help_modal .modal-content {
    height: 500px;
}
#kt_help_modal .modal-content .card.card-custom {
    box-shadow: none;
    overflow: hidden;
}
#kt_help_modal .modal-content .card.card-custom > .card-body {
    overflow-y: auto;
}
#kt_help_modal #help_dashboard {
    margin: 2rem;
}
#kt_help_modal #help_dashboard .card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}
#kt_help_modal #help_dashboard .card-body a {
    display: flex;
    flex-direction: column;
    flex: 1;
    align-self: stretch;
    justify-content: center;
}
#kt_help_modal #help_dashboard a .icon i {
    margin-right: 0;
    padding-right: 0;
    font-size: 20px;
    margin-bottom: 5px;
}
#kt_help_modal #help_content #universes {
    margin: 2rem;
}
#kt_help_modal .navi-link {
    border-radius: 0;
}
#kt_help_modal .article-container {
    padding: 2rem 2.25rem;
}
#kt_help_modal .article-container img {
    max-width: 100%;
}
#kt_help_modal #kt_help_modal_content {
    padding: 0 !important;
}
#kt_help_modal #kt_help_modal_content .card-header {
    padding: 1rem;
}
#kt_help_modal #kt_help_modal_content .card-header h6 {
    text-align: center;
    margin-bottom: 0;
}
#kt_help_modal #kt_help_modal_content .scroll.scroll-pull {
    padding-right: 4px;
    margin-right: -4px;
}
#kt_help_modal .blank-state .blank-state-image {
    margin: 20px 0;
}
#kt_help_modal .blank-state .blank-state-image img {
    max-height: 100px;
}

#kt_help_modal.modal-fs {
    width: calc(100% - 40px);
    height: calc(100% - 120px);
    max-width: 100%;
}
#kt_help_modal.modal-fs .modal-dialog {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
}
#kt_help_modal.modal-fs .modal-content {
    height: 100%;
}
#kt_help_modal.modal-fs .modal-content > .card {
    height: 100%;
}

#kt_help_modal .mark mark {
    color: #FFA800;
    background-color: #FFF4DE;
}

@media (max-width: 767px) {
    #kt_help_modal {
        top: 0;
        bottom: unset;
        height: 100vh;
        width: 100%;
        max-width: 100%;
        left: 0;
        max-height: calc(100vh - 80px);
    }
    #kt_help_modal .modal-dialog {
        max-width: 100%;
        width: 100%;
        height: 100%;
    }
    #kt_help_modal .modal-content {
        height: 100%;
    }
}


#kt_modal_help_search {
    padding: 0;
}
#kt_modal_help_search .input-group-prepend,
#kt_modal_help_search .input-group-append {
    background-color: transparent;
}
#kt_modal_help_search .form-control {
    background-color: transparent;
    outline: none !important;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 0;
    padding: 0;
    border-radius: 0;
}
#kt_modal_help_search .input-group-prepend .input-group-text {
    padding-left: 5px;
}
#kt_modal_help_search .input-group-prepend .input-group-text,
#kt_modal_help_search .input-group-append .input-group-text {
    background-color: transparent;
    border: 0;
}
#kt_modal_help_search .input-group-append {
    cursor: pointer;
}

.card-note.inactive {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
}
#article img {
    max-width: 100%;
}

/* ckeditor5 media embed */
figure.media {
    display: block;
}
