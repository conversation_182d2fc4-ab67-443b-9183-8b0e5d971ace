.payment-form {
  background: #FFFFFF;
  box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  -moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  margin-bottom: 10px;
  padding: 30px;
}
.modal .payment-form {
  box-shadow: none;
  border-radius: 0;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  padding: 10px;
}
.wizard [data-wizard-type="step-info"] {
  display: none; }
.wizard [data-wizard-type="step-info"][data-wizard-state="current"] {
  display: block; }

.wizard [data-wizard-type="step-content"] {
  display: none; }
.wizard [data-wizard-type="step-content"][data-wizard-state="current"] {
  display: block; }

.wizard [data-wizard-type="action-prev"] {
  display: none; }

.wizard [data-wizard-type="action-next"] {
  display: inline-block; }

.wizard [data-wizard-type="action-submit"] {
  display: none; }

.wizard[data-wizard-state="first"] [data-wizard-type="action-prev"] {
  display: none; }

.wizard[data-wizard-state="first"] [data-wizard-type="action-next"] {
  display: inline-block; }

.wizard[data-wizard-state="first"] [data-wizard-type="action-submit"] {
  display: none; }

.wizard[data-wizard-state="between"] [data-wizard-type="action-prev"] {
  display: inline-block; }

.wizard[data-wizard-state="between"] [data-wizard-type="action-next"] {
  display: inline-block; }

.wizard[data-wizard-state="between"] [data-wizard-type="action-submit"] {
  display: none; }

.wizard[data-wizard-state="last"] [data-wizard-type="action-prev"] {
  display: inline-block; }

.wizard[data-wizard-state="last"] [data-wizard-type="action-next"] {
  display: none; }

.wizard[data-wizard-state="last"] [data-wizard-type="action-submit"] {
  display: inline-block; }

.wizard[data-wizard-clickable="true"] .wizard-step {
  cursor: pointer; }
.wizard.wizard-1 .wizard-nav .wizard-steps {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center; }
  .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    flex: 1 1 auto;
    justify-content: center; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step div {
      margin: 0 auto; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label {
      margin-left: 3.5rem;
      margin-right: 3.5rem; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon {
        color: #B5B5C3;
        font-size: 3.75rem; }
        .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon svg g [fill] {
          -webkit-transition: fill 0.3s ease;
          transition: fill 0.3s ease;
          fill: #B5B5C3; }
        .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon svg:hover g [fill] {
          -webkit-transition: fill 0.3s ease;
          transition: fill 0.3s ease; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
        color: #7E8299;
        font-size: 1.1rem;
        font-weight: 500;
        margin-top: 0.75rem; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-arrow {
      color: #FFFFFF;
      font-size: 1.25rem; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-arrow.last {
        display: none; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-icon, .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-icon {
      color: #6993FF; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-icon svg g [fill], .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-icon svg g [fill] {
        -webkit-transition: fill 0.3s ease;
        transition: fill 0.3s ease;
        fill: #6993FF; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-icon svg:hover g [fill], .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-icon svg:hover g [fill] {
        -webkit-transition: fill 0.3s ease;
        transition: fill 0.3s ease; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-title, .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-title {
      color: #6993FF; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-arrow, .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-arrow {
      opacity: 1; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-arrow svg g [fill], .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-arrow svg g [fill] {
        -webkit-transition: fill 0.3s ease;
        transition: fill 0.3s ease;
        fill: #6993FF; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-arrow svg:hover g [fill], .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-arrow svg:hover g [fill] {
        -webkit-transition: fill 0.3s ease;
        transition: fill 0.3s ease; }

@media (min-width: 768px) and (max-width: 1199.98px) {
  .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label {
    margin-left: 1.5rem;
    margin-right: 1.5rem; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon {
      font-size: 3.75rem; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
      margin-top: 0.75rem; }
  .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-arrow {
    font-size: 1.1rem; } }

@media (max-width: 991px) {
  .wizard-steps h3 {
    display: none; }
}
@media (max-width: 767.98px) {
  .wizard.wizard-1 .wizard-nav .wizard-steps {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column; }
    .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step {
      width: 100%;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: justify;
      -ms-flex-pack: justify;
      margin-bottom: 0.5rem;
      margin-top: 0.5rem;
      justify-content: center; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        margin-left: 0;
        margin-right: 0; }
        .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon {
          font-size: 1.5rem;
          margin-right: 0; }
        .wizard-steps h3 {
          display: none; }
        .wizard-steps .wizard-icon-arrow {
          display: none; }
      .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-arrow {
        font-size: 1rem; }
        .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-arrow.last {
          display: block; } }


.wizard-icon-arrow {
  transform: rotate(-45deg);
  background: #232233;
  border-radius: 15px;
  width: 35px;
  height: 35px;
  text-align: center;
  vertical-align: middle;
  line-height: 35px;
  opacity: .3;
  margin: 0 10px;
  flex: 0 0 35px;
}
.wizard-arrow {
  font-size: 16px;
  vertical-align: middle;
  color: #FFFFFF;
  transform: rotate(45deg);
  margin-top: -2px;
}
.wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] + .wizard-icon-arrow,
.wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] + .wizard-icon-arrow {
  opacity: 1;
}

.wizard-step-content h3 {
  font-style: italic;
  font-weight: bold;
  font-size: 20px !important;
}
.wizard label.control-label {
  font-weight: bold;
  font-size: 15px;
  text-transform: uppercase;
  color: #000000;
}

.wizard-step-content-border {
  border: 4px solid rgba(42, 37, 54, 0.07);
  box-sizing: border-box;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}
.wizard-step-content-border .form-group:last-child {
  margin-bottom: 0;
}
.wizard-step-content-border > h4 {
  font-style: italic;
  font-size: 20px !important;
  line-height: 33px;
  text-transform: uppercase;
  color: #6A6D81 !important;
}

.wizard-step-content-user-logged {
  box-shadow: 0 0 20px rgba(56, 71, 109, 0.1);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
}
.wizard-step-content-user-logged p {
  font-size: 14px !important;
}

.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps {
  padding: 10px 0 !important;
}
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label {
  margin-left: 0;
  margin-right: 0;
}
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon {
  font-size: 2.5rem;
}
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-icon,
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
  color: #232233;
  opacity: 0.3;
}
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
  text-transform: uppercase;
  font-style: italic;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 0;
}
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-title,
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-title,
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-icon,
.payment-form .wizard.wizard-1 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-icon {
  opacity: 1;
}
.payment-form .card.card-custom {
  box-shadow: none;
}
#card-products {
  margin-bottom: 20px;
}
#card-products .card-header {
  display: none;
}
#card-products .card-body {
  padding: 0;
}
#card-products .card-footer {
  padding: 2rem 0 0 0;
}
#card-products .card-footer .table tr td:first-child {
  padding-left: 0;
}

.wizard-step-content {
  padding: 45px 0 10px 0;
}
@media (max-width: 767px) {
  .wizard-step-content {
    padding: 45px 0 0 0;
  }
}
.wizard-footer {
  padding: 0;
}
.wizard .wizard-footer .btn {
  width: 100%;
  font-weight: 900;
  font-size: 28px;
  line-height: 42px;
  text-align: center;
  text-transform: uppercase;
  height: 74px;
  white-space: nowrap;
}
.wizard .wizard-footer .btn.btn-primary {
  background: #232233;
  color: #F0F2F9;
}
.wizard .wizard-footer .btn.btn-primary:hover,
.wizard .wizard-footer .btn.btn-primary:focus {
  background-color: #061d37 !important;
  opacity: 0.85;
  box-shadow: none;
}
.wizard .wizard-footer .btn.btn-light-primary {
  background: #FFFFFF;
  color: #181127;
  border: 1px solid #232233;
}
.wizard .wizard-footer .btn.btn-light-primary:hover,
.wizard .wizard-footer .btn.btn-light-primary:focus {
  background: #FFFFFF !important;
  color: #181127 !important;
  border: 1px solid #232233 !important;
  opacity: 0.5;
}

.wizard[data-wizard-state="first"] .wizard-footer-left {
  display: none;
}
.wizard[data-wizard-state="first"] .wizard-footer-right {
  margin-left: 0 !important;
}
.wizard[data-wizard-state="first"] .wizard-footer-right {
  width: 100%;
}
.wizard[data-wizard-state="first"] [data-wizard-type="action-next"] {
  display: block;
  width: 100%;
}


.wizard[data-wizard-state="between"] .wizard-footer-left,
.wizard[data-wizard-state="between"] .wizard-footer-right,
.wizard[data-wizard-state="last"] .wizard-footer-left,
.wizard[data-wizard-state="last"] .wizard-footer-right {
  display: inline-block !important;
  width: 50% !important;
}

#order-form .font-size-sm p,
#order-form .font-size-sm ul li {
  font-size: 1.08rem;
}

@media (max-width: 767px) {
  .payment-form {
    margin-top: 20px;
  }
  .wizard.wizard-1 .wizard-nav .wizard-steps {
    flex-direction: row;
  }
  .wizard-steps h3 {
    display: none;
  }
  .wizard .wizard-footer .btn {
    font-size: 14px;
    line-height: 18px;
  }
}
