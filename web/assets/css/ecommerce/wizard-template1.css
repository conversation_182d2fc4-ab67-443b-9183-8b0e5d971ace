.login.login-3 {
  width: 100%;
  height: 100%;
}
.login.login-3 .login-content {
  background-color: transparent;
}
.wizard.wizard-4 {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column; }
.wizard.wizard-4 .wizard-nav .wizard-steps {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(25% - 0.25rem);
  flex: 0 0 calc(25% - 0.25rem);
  width: calc(25% - 0.25rem);
  background-color: #F3F6F9;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #3F4254;
  padding: 2rem 2.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-number {
  font-size: 1.3rem;
  font-weight: 600;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 2.75rem;
  flex: 0 0 2.75rem;
  height: 2.75rem;
  width: 2.75rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: rgba(105, 147, 255, 0.08);
  color: #6993FF;
  margin-right: 1rem;
  border-radius: 0.5rem; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-title {
  font-size: 1.1rem;
  font-weight: 600; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-desc {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
  background-color: #ffffff; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-number {
  color: #ffffff;
  background-color: #6993FF; }
.wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-label .wizard-title {
  color: #6993FF; }
.wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="2"] .wizard-step {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(50% - 0.25rem);
  flex: 0 0 calc(50% - 0.25rem);
  width: calc(50% - 0.25rem); }
.wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="3"] .wizard-step {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(33.33333% - 0.25rem);
  flex: 0 0 calc(33.33333% - 0.25rem);
  width: calc(33.33333% - 0.25rem); }
.wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="4"] .wizard-step {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(25% - 0.25rem);
  flex: 0 0 calc(25% - 0.25rem);
  width: calc(25% - 0.25rem); }
.wizard.wizard-4 .wizard-nav .wizard-steps[data-total-steps="5"] .wizard-step {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(20% - 0.25rem);
  flex: 0 0 calc(20% - 0.25rem);
  width: calc(20% - 0.25rem); }

@media (max-width: 767.98px) {
  .wizard.wizard-4 .wizard-nav .wizard-steps {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start; }
  .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
    -webkit-box-flex: 0 !important;
    -ms-flex: 0 0 100% !important;
    flex: 0 0 100% !important;
    position: relative;
    width: 100% !important; }
  .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    padding: 0.5rem 2rem; } }


#payment_form .wizard-step-content-border {
  border: 1px solid rgba(42, 37, 54, 0.07);
  box-sizing: border-box;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  background: white;
}
#payment_form .wizard-step-content-border .form-control.form-control-solid {
  background-color: #F3F6F9;
  border-color: #F3F6F9;
  color: #3F4254;
}
#payment_form .wizard-step-content-border .form-group:last-child {
  margin-bottom: 0;
}
#payment_form .wizard-step-content-border > h4 {
  font-size: 20px !important;
  line-height: 26px;
  font-weight: bold !important;
}

@media (max-width: 767px) {
  .wizard.wizard-4 .wizard-nav {
    margin-bottom: 10px;
  }
  .wizard.wizard-4 > .card-custom {
    border-top-left-radius: 0.42rem !important;
    border-top-right-radius: 0.42rem !important;
  }
  .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
    display: none;
    border-radius: 0.5rem;
  }
  .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
    display: block;
  }
}
