<?xml version="1.0" encoding="utf-8"?>
<svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:bx="https://boxy-svg.com">
  <defs>
    <linearGradient id="gradient-1" bx:pinned="true">
      <title>test</title>
      <stop offset="0" style="stop-color: rgb(248, 171, 0);"/>
      <stop offset="1" style="stop-color: rgb(227, 115, 0);"/>
    </linearGradient>
    <linearGradient id="gradient-0" bx:pinned="true">
      <title>Test 2</title>
      <stop offset="0" style="stop-color: rgb(138, 180, 248);"/>
      <stop offset="1" style="stop-color: rgb(66, 133, 244);"/>
    </linearGradient>
    <linearGradient id="gradient-0-0" gradientUnits="userSpaceOnUse" x1="34" y1="0" x2="34" y2="68" xlink:href="#gradient-0"/>
  </defs>
  <rect width="68" height="68" rx="16" style="paint-order: fill; fill-rule: nonzero; fill: url('#gradient-0-0');"/>
  <g transform="matrix(0.015769, 0, 0, 0.015769, 14.528929, 14.526466)" style="">
    <path class="st0" d="M1449.8,2376L1021,1946.7l921.1-930.5l436.7,436.6L1449.8,2376z" style="fill: rgb(255, 255, 255);"/>
    <path class="st1" d="M1452.9,527.1L1016.3,90.4L90.5,1016.2c-120.6,120.5-120.7,315.8-0.2,436.4c0.1,0.1,0.2,0.2,0.2,0.2 l925.8,925.8l428.3-430.3L745,1235.1L1452.9,527.1z" style="fill: rgb(203, 220, 247);"/>
    <path class="st0" d="M2378.7,1016.2L1452.9,90.4c-120.6-120.6-316.1-120.6-436.7,0c-120.6,120.6-120.6,316.1,0,436.6l926.3,925.8 c120.6,120.6,316.1,120.6,436.6,0c120.6-120.6,120.6-316.1,0-436.6L2378.7,1016.2z" style="fill: rgb(255, 255, 255);"/>
    <circle class="st2" cx="1231.2" cy="2163.9" r="306" style="fill: rgb(169, 196, 233);"/>
  </g>
</svg>