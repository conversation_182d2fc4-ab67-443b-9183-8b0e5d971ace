var checkMessagesTimeout;

// Class definition
var KTLayoutChat = function () {
    var dropZoneDocument;
    var _chatAsideEl;
    var _chatAsideOffcanvasObj;

    // Private functions
    var _initAside = function () {
        // Mobile offcanvas for mobile mode
        _chatAsideEl = KTUtil.getById('kt_chat_aside');
        _chatAsideOffcanvasObj = new KTOffcanvas(_chatAsideEl, {
            overlay: true,
            baseClass: 'offcanvas-mobile',
            toggleBy: 'kt_app_chat_toggle'
        });
    };

    // Private functions
    var _init = function (element) {
        var scrollEl = KTUtil.find(element, '.scroll');
        var cardBodyEl = KTUtil.find(element, '.card-body');
        var cardHeaderEl = KTUtil.find(element, '.card-header');
        var cardFooterEl = KTUtil.find(element, '.card-footer');

        if (!scrollEl) {
            return;
        }

        moment.locale('fr');
        moment.updateLocale('fr', {
            relativeTime : {
                future: "dans %s",
                past:   "%s",
                s  : 'A l\'instant',
                ss : '%d secondes',
                m:  "1min",
                mm: "%dmin",
                h:  "1h",
                hh: "%dh",
                d:  "1j",
                dd: "%dj",
                w:  "1 semaine",
                ww: "%d semaines",
                M:  "a mois",
                MM: "%d mois",
                y:  "a an",
                yy: "%d ans"
            }
        });

        // initialize perfect scrollbar(see:  https://github.com/utatti/perfect-scrollbar)
        KTUtil.scrollInit(scrollEl, {
            windowScroll: true, // allow browser scroll when the scroll reaches the end of the side
            mobileNativeScroll: true,  // enable native scroll for mobile
            desktopNativeScroll: false, // disable native scroll and use custom scroll for desktop
            resetHeightOnDestroy: true,  // reset css height on scroll feature destroyed
            handleWindowResize: true, // recalculate hight on window resize
            rememberPosition: true, // remember scroll position in cookie
            height: function() {  // calculate height
                var height;

                if (KTUtil.isBreakpointDown('lg')) { // Mobile mode
                    return KTUtil.hasAttr(scrollEl, 'data-mobile-height') ? parseInt(KTUtil.attr(scrollEl, 'data-mobile-height')) : 400;
                } else if (KTUtil.isBreakpointUp('lg') && KTUtil.hasAttr(scrollEl, 'data-height')) { // Desktop Mode
                    return parseInt(KTUtil.attr(scrollEl, 'data-height'));
                } else {
                    var position = $('#kt_chat_content .scroll').offset();

                    height = window.innerHeight;
                    height = height - parseInt(position.top);

                    /*if (cardHeaderEl) {
                        height = height - parseInt(KTUtil.css(cardHeaderEl, 'height'));
                        height = height - parseInt(KTUtil.css(cardHeaderEl, 'margin-top')) - parseInt(KTUtil.css(cardHeaderEl, 'margin-bottom'));
                    }*/

                    if (cardFooterEl) {
                        height = height - parseInt(KTUtil.css(cardFooterEl, 'height'));
                        height = height - parseInt(KTUtil.css(cardFooterEl, 'margin-top')) - parseInt(KTUtil.css(cardFooterEl, 'margin-bottom'));
                    }
                }

                // Remove additional space
                height = height - 60;
                if (height < 400) {
                    height = 400
                }

                return height;
            }
        });

        setTimeout(function() {
            var messagesEl = KTUtil.find(element, '.messages');
            scrollEl.scrollTop = parseInt(KTUtil.css(messagesEl, 'height'));
        }, 1000);

        setInterval(function() {
            _updateTimes();
        }, 10000);

        $('#kt_chat_aside .room').on('click', function() {
            _displayChatRoom(element, $(this).data('room'));
        });

        KTUtil.on(element, '.card-footer .chat-send', 'click', function(e) {
            _sendMessage(element);
        });

        // attach events
        KTUtil.on(element, '.card-footer textarea', 'keydown', function(e) {
            if (e.keyCode == 13) {
                _sendMessage(element);
                e.preventDefault();
                return false;
            }
        });

        KTUtil.on(element, '.card-footer .chat-send', 'click', function(e) {
            _sendMessage(element);
        });

        dropZoneDocument = _initDropZone(element);

        checkMessagesTimeout = setTimeout(function() {
            _checkMessages(element, true);
        }, 1000);
    }

    var _displayChatRoom = function(element, chatRoomId) {
        clearTimeout(checkMessagesTimeout);
        $('#kt_chat_content .messages').html('');
        $('#kt_chat_aside .room').removeClass('active bg-light');
        $('#kt_chat_aside #room' + chatRoomId).addClass('active bg-light');
        _checkMessages(element, true);
    }

    var _addMessage = function(element, content) {
        var messagesEl = KTUtil.find(element, '.messages');
        var scrollEl = KTUtil.find(element, '.scroll');

        var parser = new DOMParser();
        var doc = parser.parseFromString(content, 'text/html');

        messagesEl.appendChild(doc.querySelector('div'));
        scrollEl.scrollTop = parseInt(KTUtil.css(messagesEl, 'height'));

        var ps;
        if (ps = KTUtil.data(scrollEl).get('ps')) {
            ps.update();
        }
    }

    var _reinitDropZone = function() {
        $(document).find('#kt_chat_upload_document .dropzone-item').css('display', 'none');
        dropZoneDocument.removeAllFiles(true);
    }

    var _initDropZone = function(element) {
        var id = "#kt_chat_upload_document";
        var previewNode = $(id + " .dropzone-item");
        previewNode.id = "";
        var previewTemplate = previewNode.parent('.dropzone-items').html();
        previewNode.remove();

        var config = {
            url: baseDir + '/lib/upload/',
            maxFiles: 1,
            maxFilesize: 100,
            previewTemplate: previewTemplate,
            previewsContainer: id + " .dropzone-items",
            clickable: id + '_select',
            acceptedFiles: "image/*,application/pdf,.doc,.docx"
        };
        var myDropzone = new Dropzone(id, config);

        myDropzone.on("addedfile", function(file) {
            // Hookup the start button
            $(id + ' .dropzone-item').css('display', '');
        });

        // Update the total progress bar
        myDropzone.on("totaluploadprogress", function(progress) {
            if ($(id + ' .progress-bar').length) {
                $(id + ' .progress-bar').css('width', progress + "%");
            }
        });

        myDropzone.on("sending", function(file) {
            // Show the total progress bar when upload starts
            if ($(id + ' .progress-bar').length) {
                $(id + ' .progress-bar').css('opacity', 1);
            }
        });

        // Hide the total progress bar when nothing's uploading anymore
        myDropzone.on("complete", function(file) {
            if (file.status == 'success') {
                let response = JSON.parse(file.xhr.response);
                let fileUrl = response.files[0].name

                var thisProgressBar = id + " .dz-complete";
                setTimeout(function () {
                    $(thisProgressBar + " .progress-bar, " + thisProgressBar + " .progress").css('opacity', '0');
                }, 300);

                _saveFile(element, fileUrl);
            }
        });

        return myDropzone;
    }

    var _saveFile = function(element, fileUrl) {
        var idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
        $.post(
            baseDir + '/ajax/chat/file/',
            { idChatRoom: idChatRoom, fileUrl: fileUrl, CSRFGuard_token:CSRFGuard_token, async: false },
            function(data) {
                if (data.status) {
                    _addMessage(element, data.message);
                    _reinitDropZone();
                } else {
                    ShowToast('error', data.message);
                }
            },
            'json'
        );
    }

    var _sendMessage = function(element) {
        var textarea = KTUtil.find(element, 'textarea');
        var content = textarea.value;

        if (textarea.value.length === 0 ) {
            return;
        }
        textarea.value = '';

        var idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
        $.post(
            baseDir + '/ajax/chat/message/',
            { idChatRoom: idChatRoom, content: content, CSRFGuard_token:CSRFGuard_token, async: false },
            function(data) {
                if (data.status) {
                    _addMessage(element, data.message);
                } else {
                    ShowToast('error', data.message);
                }
            },
            'json'
        );
    }

    var _checkMessages = function(element, replace) {
        var idChatRoom = 0;
        if ($('#kt_chat_aside .room.active').length > 0) {
            idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
        }
        if (!idChatRoom) {
            return;
        }

        var lastMessage = 0;
        if ($('#kt_chat_content .message').length) {
            lastMessage = $('#kt_chat_content .message').last().attr('id').replace('message', '');
        }

        if (replace) {
            KTApp.block('#kt_chat_content', {});
            $('#kt_chat_content .card-header .title').html('<span class="text-dark-75 font-weight-bold font-size-h5 room-title">' + $('#kt_chat_aside .room.active:first .user-name').html() + '</span>');
            $('#kt_chat_content .card-header .sub-title').html($('#kt_chat_aside .room.active:first .user-title').html());
        }

        $.post(
            baseDir + '/ajax/chat/messages/get/',
            { idChatRoom: idChatRoom, lastMessage: lastMessage, newChatRoom: replace, CSRFGuard_token:CSRFGuard_token, async: false },
            function(data) {
                if (data.status) {
                    if (replace) {
                        KTApp.unblock('#kt_chat_content');
                        $('#kt_chat_content .card-header .title').html(data.title);
                        if (data.active) {
                            $('#kt_chat_content .card-footer').removeClass('d-none');
                        } else {
                            $('#kt_chat_content .card-footer').addClass('d-none');
                        }
                        $('#kt_chat_content .card-header .navi').html(data.actions);
                    }

                    if (data.messages) {
                        $.each(data.messages, function (key, message) {
                            _addMessage(element, message);
                        });
                    }
                    checkMessagesTimeout = setTimeout(function() {
                        _checkMessages(element);
                    }, 10000);
                }
            },
            'json'
        );
    }

    var _updateTimes = function() {
        $('.message-time').each(function() {
            let date = moment.unix($(this).data('time'));
            let fromNow = date.fromNow();
            $(this).html(fromNow);
        })
    }

    // Public methods
    return {
        init: function(id) {
            _init(KTUtil.getById(id));
            _initAside();
        },

        setup: function(element) {
            _init(element);
        }
    };
}();

KTLayoutChat.setup(KTUtil.getById('kt_chat_content'));


function CreateConfirmationCloseChatRoom(controller) {
    var idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
    Swal.fire({
        title: __('Confirmation'),
        text: __('Etes-vous sûr de vouloir clore cette conversation ?'),
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: __('Fermer la conversation'),
        cancelButtonText: __('Annuler'),
        reverseButtons: true,
        confirmButtonColor: '#F64E60',
        iconColor: '#F64E60',
    }).then(function(result) {
        if (result.value) {
            window.location.href = baseDir + '/' + controller + '/room/close/' + idChatRoom + '/';
        }
    });
}

function CreateConfirmationLeaveChatRoom(controller) {
    var idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
    Swal.fire({
        title: __('Confirmation'),
        text: __('Etes-vous sûr de vouloir quitter ce groupe ?'),
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: __('Fermer la conversation'),
        cancelButtonText: __('Annuler'),
        reverseButtons: true,
        confirmButtonColor: '#F64E60',
        iconColor: '#F64E60',
    }).then(function(result) {
        if (result.value) {
            window.location.href = baseDir + '/' + controller + '/room/leave/' + idChatRoom + '/';
        }
    });
}

function CreateConfirmationDeleteChatRoom(controller) {
    var idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
    Swal.fire({
        title: __('Confirmation'),
        text: __('Etes-vous sûr de vouloir supprimer cette conversation ?'),
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: __('Supprimer cette conversation'),
        cancelButtonText: __('Annuler'),
        reverseButtons: true,
        confirmButtonColor: '#F64E60',
        iconColor: '#F64E60',
    }).then(function(result) {
        if (result.value) {
            window.location.href = baseDir + '/' + controller + '/room/delete/' + idChatRoom + '/';
        }
    });
}

function DisplayRoomUsers() {
    var idChatRoom = 0;
    if ($('#kt_chat_aside .room.active').length > 0) {
        idChatRoom = $('#kt_chat_aside .room.active:first').data('room');
    }
    if (!idChatRoom) {
        return;
    }

    $('#ModalRoomUsers').modal('show');
    KTApp.block('#ModalRoomUsers .modal-content', {});
    $.post(
        baseDir + '/ajax/chat/room/users/get/',
        { idChatRoom: idChatRoom, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            KTApp.unblock('#ModalRoomUsers .modal-content');
            if (data.status) {
                $('#ModalRoomUsers .modal-body').html(data.modal);

                $('#ModalRoomUsers #group_name').off('change');
                $('#ModalRoomUsers #group_name').on('change', function() {
                    UpdateRoomName(idChatRoom, $(this).val());
                });
            }
        },
        'json'
    );
}

function AddUser(userId, idChatRoom) {
    KTApp.block('#ModalRoomUsers .modal-content', {});
    $.post(
        baseDir + '/ajax/chat/room/user/add/',
        { idChatRoom: idChatRoom, userId: userId, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            if (data.status) {
                DisplayRoomUsers();
                $('#kt_chat_content .room-subtitle').html(data.roomSubTitle);
            } else {
                KTApp.unblock('#ModalRoomUsers .modal-content');
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function RemoveUser(chatUserId, idChatRoom) {
    KTApp.block('#ModalRoomUsers .modal-content', {});
    $.post(
        baseDir + '/ajax/chat/room/user/remove/',
        { idChatRoom: idChatRoom, chatUserId: chatUserId, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            if (data.status) {
                DisplayRoomUsers();
                $('#kt_chat_content .room-subtitle').html(data.roomSubTitle);
            } else {
                KTApp.unblock('#ModalRoomUsers .modal-content');
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function UpdateRoomName(idChatRoom, roomName) {
    $.post(
        baseDir + '/ajax/chat/room/name/',
        { idChatRoom: idChatRoom, roomName: roomName, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            if (data.status) {
                $('#room' + idChatRoom + ' .user-name').html(roomName);
                $('#kt_chat_content .room-title').html(roomName);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}
