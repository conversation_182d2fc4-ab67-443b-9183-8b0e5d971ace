$(document).ready(function () {
    initDropZones();
});

function initDropZones() {
    if ($('.dropzone').length > 0) {
        $('.dropzone').each(function() {
            if ($(this).hasClass('d-none')) {
                return true; //continue
            }

            var dropZoneId = $(this).prop('id');
            initDropZone(dropZoneId);
        });
    }
}

function initDropZone(dropZoneId) {
    var id = '#' + dropZoneId;

    var options = {
        url: baseDir + '/lib/upload/',
        parallelUploads: 1,
        maxFilesize: 10,
        maxFiles: 1,
    };
    if ($(id).hasClass('dropzone-big')) {
        options.addRemoveLinks = true;
        options.dictCancelUpload = __('Annuler');
        options.dictRemoveFile = '<i class="fas fa-trash"></i>' + __('Supprimer');
        options.thumbnailWidth = 300;
        options.thumbnailHeight = 300;

        if ($(id).hasClass('dropzone-img-square')) {
            options.thumbnailWidth = 120;
            options.thumbnailHeight = 120;
        }
    }
    if ($(id).data('nb-files')) {
        options.maxFiles = $(id).data('nb-files');
    }

    // set the preview element template
    if ($(id + " .dropzone-item").length) {
        var previewNode = $(id + " .dropzone-item");
        previewNode.id = "";
        var previewTemplate = previewNode.parent('.dropzone-items').html();
        previewNode.remove();

        options.previewTemplate = previewTemplate;
        options.previewsContainer = id + " .dropzone-items";
    }

    if ($('.dropzone-select').length) {
        options.clickable = '.dropzone-select';
    }

    if ($(id).data('accepted-files')) {
        //image/*,application/pdf,.psd
        options.acceptedFiles = $(id).data('accepted-files');
    }

    var dropzone = new Dropzone(id, options);

    dropzone.on("addedfile", function(file) {
        $(document).find( id + ' .dropzone-item').css('display', '');
    });

    // Update the total progress bar
    dropzone.on("totaluploadprogress", function(progress) {
        $( id + " .progress-bar").css('width', progress + "%");
    });

    dropzone.on("sending", function(file) {
        // Show the total progress bar when upload starts
        $( id + " .progress-bar").css('opacity', "1");
    });

    // Hide the total progress bar when nothing's uploading anymore
    dropzone.on("complete", function(file) {
        var thisProgressBar = id + " .dz-complete";
        setTimeout(function(){
            $( thisProgressBar + " .progress-bar, " + thisProgressBar + " .progress").css('opacity', '0');
        }, 300);

        if (file.status == 'success') {
            var fileName = file.name;
            if (typeof file.xhr.response != 'undefined') {
                var response = JSON.parse(file.xhr.response);
                if (response && typeof response.files[0] != 'undefined') {
                    fileName = response.files[0].name;
                }
            }

            $(file.previewElement).append('<input type="hidden" name="' + dropZoneId + '[]" value="' + fileName + '">');
        }
    });
}
function displayDropZone(id) {
    $('#preview-' + id + ' .btn').tooltip('dispose');
    $('#preview-' + id).remove();
    $('#' + id).removeClass('d-none');
    initDropZone(id);
}
