function getTextColorForBackground(color) {
    let colors = convertHexToInt(color);
    let diff = rgbColorDiff(colors[0], colors[1], colors[2], 0, 0, 0);
    if (diff < 500) {
        return true;
    } else {
        return false;
    }
}
function convertHexToInt(color) {
    color = color.replace('#', '');
    let r = parseInt(color.substr(0,2), 16);
    let g = parseInt(color.substr(2,2), 16);
    let b = parseInt(color.substr(4,2), 16);
    return [r,g,b];
}

function rgbColorDiff(r1, g1, b1, r2, g2, b2) {
    return Math.max(r1,r2) - Math.min(r1,r2) +
        Math.max(g1,g2) - Math.min(g1,g2) +
        Math.max(b1,b2) - Math.min(b1,b2);
}
