"use strict";
var KTWizardTutorial = function () {
    var _wizardEl;
    var _formEl;
    var _wizardObj;

    var _initWizard = function () {
        _wizardObj = new KTWizard(_wizardEl, {
            startStep: 1,
            clickableSteps: false
        });

        // Validation before going to next page
        _wizardObj.on('change', function (wizard) {
            if (wizard.getStep() > wizard.getNewStep()) {
                return; // Skip if stepped back
            }

            if (typeof _validations !== 'undefined') {
                var validator = _validations[wizard.getStep()];
                if (validator) {
                    validator.validate().then(function (status) {
                        if (status == 'Valid') {
                            wizard.goTo(wizard.getNewStep());
                        } else {
                            KTUtil.scrollTo(_wizardEl);
                        }
                    });
                } else {
                    return true;
                }

                return false;
            }

            return true;
        });

        _wizardObj.on('changed', function (wizard) {
            KTUtil.scrollTop();
        });

        _wizardObj.on('submit', function (wizard) {
            if (typeof _validations !== 'undefined') {
                var validator = _validations[wizard.getStep()];
                if (validator) {
                    validator.validate().then(function (status) {
                        if (status == 'Valid') {
                            _submitWizard(wizard);
                            return true;
                        }
                        KTUtil.scrollTo(_wizardEl);
                        return false;
                    });
                } else {
                    _submitWizard(wizard);
                    return true;
                }
            } else {
                _submitWizard(wizard);
                return true;
            }
        });
    }

    var _submitWizard = function (wizard) {
        $('#kt_form_submit').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left');

        if (typeof ajaxAction != 'undefined' && ajaxAction) {
            $('#kt_form .errors-step').addClass('d-none');

            let formData = $('#kt_form').serializeArray();
            formData.push({name: 'CSRFGuard_token', value: CSRFGuard_token});
            $.post(
                baseDir + '/ajax/' + ajaxAction + '/',
                formData,
                function (result) {
                    if (result.status) {
                        _formEl.submit();
                    } else {
                        if (!result.step) {
                            result.step = 1;
                        }
                        $('#kt_form_submit').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left').addClass('px-9');
                        $('#kt_form #errors' + result.step + ' .alert-text').html(result.message);
                        $('#kt_form #errors' + result.step).removeClass('d-none');
                        wizard.goTo(result.step);

                        KTUtil.scrollTop();
                    }
                },
                'json'
            );
        } else {
            _formEl.submit();
        }
    }

    return {
        init: function () {
            _wizardEl = KTUtil.getById('kt_wizard');
            _formEl = KTUtil.getById('kt_form');
            _initWizard();
        }
    };
}();

jQuery(document).ready(function () {
    KTWizardTutorial.init();
});
