$(document).ready(function() {
    $('#marital_status').on('change', function() {
        let marital_status = $('#marital_status option:selected').val();
        $('#matrimonial_regime').closest('.form-group').addClass('d-none');
        $('#pacs_regime').closest('.form-group').addClass('d-none');
        if (marital_status == 'married' || marital_status == 'widowed' || marital_status == 'divorced') {
            $('#matrimonial_regime').closest('.form-group').removeClass('d-none');
        } else if (marital_status == 'pacs') {
            $('#pacs_regime').closest('.form-group').removeClass('d-none');
        }
    });
    $('#marital_status').trigger('change');

    if ($('#checkbox-company_0').length) {
        $("#checkbox-company_0").on("change", function() {
            if ($(this).prop("checked")) {
                $("#company-content").removeClass("d-none");
            } else {
                $("#company-content").addClass("d-none");
            }
        });
    }

    if ($('#capable').length) {
        $("#capable").on("change", function() {
            if ($(this).prop("checked")) {
                $("#capable_content").addClass("d-none");
            } else {
                $("#capable_content").removeClass("d-none");
            }
        });
        $('#capable').trigger('change');
    }
});
