$(document).ready(function() {
    handleConditions();

    $(document).on('click', '.btn-comments', function(e) {
        e.preventDefault();
        let slug = $(this).closest('.card').attr('id').replace('card_', '');
        let requestUserPageId = $('#request_user_page_id').val();
        KTApp.block('#card_' + slug, {});
        $.post(
            baseDir + '/ajax/request/comments/',
            { slug: slug, requestUserPageId: requestUserPageId, CSRFGuard_token: CSRFGuard_token },
            function (result) {
                KTApp.unblock('#card_' + slug);
                if (result.status) {
                    $('#ModalComments #question_slug').val(slug);
                    $('#ModalComments .modal-body').html(result.content);
                    $('#ModalComments').modal('show');
                } else {
                    ShowToast('error', result.message);
                }
            },
            'json'
        );
    });

    $(document).on('click', '#commentFormSubmit', function(e) {
        e.preventDefault();
        let slug = $('#ModalComments #question_slug').val();
        let requestUserPageId = $('#request_user_page_id').val();
        let comment = $('#ModalComments #comment').val();
        KTApp.block('#ModalComments .modal-content', {});
        $.post(
            baseDir + '/ajax/request/comment/add/',
            { slug: slug, requestUserPageId: requestUserPageId, comment: comment, CSRFGuard_token: CSRFGuard_token },
            function (result) {
                console.log(result);
                KTApp.unblock('#ModalComments .modal-content');
                if (result.status) {
                    $('#ModalComments #requestComments .timeline-items').append(result.content);
                    if ($('#noCommentsInfo').length) {
                        $('#noCommentsInfo').remove();
                    }
                    if ($('#card_' + slug).find('.nb-comments').length) {
                        let nbComments = parseInt($('#card_' + slug).find('.nb-comments').text()) + 1;
                        $('#card_' + slug).find('.nb-comments').text(nbComments);
                    }
                    $('#ModalComments #comment').val('');
                } else {
                    ShowToast('error', result.message);
                }
            },
            'json'
        );
    });
});

function handleConditions() {
    if (typeof conditions !== 'undefined') {
        $.each(conditions, function(fieldId, fieldConditions) {
            processField(fieldId, fieldConditions);

            let fieldType = $('input[name="' + fieldId + '"]').attr('type');
            let value;
            if (fieldType == 'radio') {
                value = $('input[name="' + fieldId + '"]:checked').val();
            } else {
                value = $('input[name="' + fieldId + '"]').val();
            }
            if (value) {
                processField(fieldId, fieldConditions, value);
            }

            $('input[name="' + fieldId + '"]').on('change', function () {
                processField(fieldId, fieldConditions, $(this).val());
            });
            $('input[name="' + fieldId + '[]"]').on('change', function () {
                let values = [];
                $('input[name="' + fieldId + '[]"]:checked').each(function() {
                    values.push(this.value);
                });
                processField(fieldId, fieldConditions, values);
            });
        });
    }
}

function processField(fieldId, fieldConditions, value) {
    //console.log('processField : ' + fieldId);
    //console.log(fieldConditions);

    let conditionMet = false;
    $.each(fieldConditions, function(conditionFieldId, fieldsIds) {
        //console.log(conditionFieldId, fieldsIds);
        if (conditionMet) {
            return false; // breaks
        }

        $.each(fieldsIds, function(index, fieldId) {
            if (value && value == conditionFieldId) {
                //console.log('value == conditionFieldId');
                $('#card_' + fieldId).removeClass('d-none');
                conditionMet = true;
            } else if (value && isArray(value) && value.includes(conditionFieldId)) {
                $('#card_' + fieldId).removeClass('d-none');
            } else {
                $('#card_' + fieldId).addClass('d-none');
            }
        });
    });
}

