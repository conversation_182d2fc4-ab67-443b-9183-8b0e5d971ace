var cardButton = document.getElementById('btnValidate');
cardButton.addEventListener('click', function(ev) {
    ev.preventDefault();
    $('#errors').html('');
    setPaymentInProcess(true);
    if (typeof setupIntentSecret !== 'undefined') {
        processStripeAddCard(billingAddress);
    } else {
        processStripePayment(billingAddress);
    }
});

let paymentValidateButton = $('#btnUsePaymentMethodId');
if (paymentValidateButton.length) {
    paymentValidateButton.on('click', function (e) {
        $(this).addClass('spinner spinner-left spinner-white disabled');
    });
}