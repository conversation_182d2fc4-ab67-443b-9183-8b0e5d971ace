var publicKey = $('#stripe_public_key').val();
var stripe = Stripe(publicKey);

if (typeof setupIntentSecret !== 'undefined') {
    const options = {
        clientSecret: setupIntentSecret,
    };
    var elements = stripe.elements(options);
} else {
    var elements = stripe.elements();
}

var cardButton = document.getElementById('btnValidate');

var elementStyles = {
    base: {
        color: '#3F4254',
        fontWeight: 400,
        fontFamily: 'Inter, Helvetica, "sans-serif"',
        fontSize: '14px',
        fontSmoothing: 'antialiased',
        '::placeholder': {
            color: '#B5B5C3',
        },
        ':-webkit-autofill': {
            color: '#e39f48',
        },
    },
    invalid: {
        color: '#E25950',
        '::placeholder': {
            color: '#FFCCA5',
        },
    },
};

var elementClasses = {
    base: 'form-control form-control-solid form-control-lg form-control-stripe',
    focus: 'focused',
    empty: 'empty',
    invalid: 'invalid',
};

var cardNumber = elements.create('cardNumber', {
    style: elementStyles,
    classes: elementClasses,
});
cardNumber.mount('#stripe-card-number');

var cardExpiry = elements.create('cardExpiry', {
    style: elementStyles,
    classes: elementClasses,
});
cardExpiry.mount('#stripe-card-expiry');

var cardCvc = elements.create('cardCvc', {
    style: elementStyles,
    classes: elementClasses,
    placeholder: '123',
});
cardCvc.mount('#stripe-card-cvc');

var stripeInputElements = [cardNumber, cardExpiry, cardCvc];

stripeInputElements.forEach(function(element, idx) {
    element.addEventListener('change', function (event) {
        $('#card-errors').html('');
        $('.form-group').removeClass('has-warning');
        if (event.error) {
            $('.form-group.' + event.elementType).addClass('has-warning');
            $('#card-errors').html('<i class="fa fa-warning"></i> ' + event.error.message);
        }
    });
});

/*cardNumber.on('change', function(event) {
    if (event.brand) {
        setStripeBrandIcon(event.brand);
    }
});*/

$(document).on('click', '#stripe-brand-icons .navi-link', function() {
    $('#stripe-brand-icons .btn').html($(this).find('.navi-icon').html());
    $('#preferred-network').val($(this).data('value'));
});

cardNumber.on('networkschange', function(event) {
    if (event.networks) {
        displayStripeBrandIcons(event.networks);
    } else {
        $('#preferred-network').val('');
        $('#stripe-brand-icons').hide();
    }
});

var stripeCardBrandToPfClass = {
    'visa': 'fab fa-cc-visa',
    'mastercard': 'fab fa-cc-mastercard',
    'amex': 'fab fa-cc-amex',
    'discover': 'fab fa-cc-discover',
    'diners': 'fab fa-cc-diners-club',
    'jcb': 'fab fa-cc-jcb',
    'unknown': 'fas fa-credit-card',
}

function setStripeBrandIcon(brand) {
    var brandIconElement = $('#stripe-brand-icon');
    var pfClass = 'fas fa-credit-card';
    if (brand in stripeCardBrandToPfClass) {
        pfClass = stripeCardBrandToPfClass[brand];
    }
    brandIconElement.removeAttr('class').addClass(pfClass);
}

function displayStripeBrandIcons(brands) {
    var pfClass = 'fas fa-credit-card';
    if (brands[0] in stripeCardBrandToPfClass) {
        pfClass = stripeCardBrandToPfClass[brands[0]];
    }

    $('#stripe-brand-icons .btn').html('<i class="' + pfClass + '"></i>');
    $('#preferred-network').val(brands[0]);

    let content = '<ul class="navi navi-hover no-wrap">';
    brands.forEach(function(brand) {
        let pfClass = 'fas fa-credit-card';
        if (brand in stripeCardBrandToPfClass) {
            pfClass = stripeCardBrandToPfClass[brand];
        }
        content += '<li class="navi-item"><a class="navi-link" data-value="' + brand + '"><span class="navi-icon"><i class="' + pfClass + '"></i></span></a></li>';
    });
    content += '</ul>';
    $('#stripe-brand-icons .dropdown-menu').html(content);
    $('#stripe-brand-icons').show();
}

function processStripePayment(billingAddress, custom, token)
{
    if (!custom) {
        custom = '';
    }
    if (!token) {
        token = '';
    }

    stripe.createPaymentMethod('card', stripeInputElements[0], {
        billing_details: billingAddress
    }).then(function (result) {
        if (result.error) {
            displayError(result.error.message, 'payment');
            setPaymentInProcess(false);
        } else {
            let formData = getFormInputs();
            formData['payment_method_id'] = result.paymentMethod.id;
            formData['CSRFGuard_token'] = CSRFGuard_token;
            if (custom) {
                formData['custom'] = custom;
            }
            if (token) {
                formData['token'] = token;
            }
            $.post(
                baseDir + stripeAjaxRoute,
                formData,
                function (result) {
                    handleStripeServerResponse(result, custom, token);
                },
                'json'
            );
        }
    });
}

function processStripeAddCard(billingAddress)
{
    stripe.confirmCardSetup(setupIntentSecret, {
        payment_method: {
            card: stripeInputElements[0],
            billing_details: billingAddress,
        }
    })
    .then(function(result) {
        if (result.error) {
            displayError(result.error.message, 'payment');
            setPaymentInProcess(false);
        } else {
            let formData = getFormInputs();
            formData['payment_method_id'] = result.setupIntent.payment_method;
            formData['CSRFGuard_token'] = CSRFGuard_token;
            $.post(
                baseDir + stripeAjaxRoute,
                formData,
                function (result) {
                    handleStripeServerResponse(result);
                },
                'json'
            );
        }
    });
}

function handleStripeServerResponse(response, custom, token) {
    if (response.error) {
        displayError(response.error, 'payment');
        setPaymentInProcess(false);
        if (response.error.match(/nom/g) || response.error.match(/email/g)) {
            goToStep(1);
        }
    } else if (typeof response.status !== 'undefined' && !response.status) {
        displayError(response.message, 'payment');
        setPaymentInProcess(false);
    } else if (response.requires_action) {
        // Use Stripe.js to handle required card action
        if (response.redirect_url) {
            window.location.href = response.redirect_url;
            return;
        }

        stripe.handleCardAction(
            response.payment_intent_client_secret
        ).then(function(result) {
            if (result.error) {
                displayError(result.error.message, 'payment');
                setPaymentInProcess(false);
            } else {
                // The card action has been handled
                // The PaymentIntent can be confirmed again on the server
                var formDatas = getFormInputs();
                formDatas['payment_intent_id'] = result.paymentIntent.id;
                formDatas['CSRFGuard_token'] = CSRFGuard_token;
                if (custom) {
                    formDatas['custom'] = custom;
                }
                if (token) {
                    formDatas['token'] = token;
                }

                $.post(
                    baseDir + stripeAjaxRoute,
                    formDatas,
                    function(result) {
                        handleStripeServerResponse(result, custom, token);
                    },
                    'json'
                );
            }
        });
    } else {
        if (typeof redirection !== 'undefined') {
            window.location.href = redirection;
        } else if (response.redirection) {
            window.location.href = response.redirection;
        } else if (response.reference) {
            window.location.href = baseDir + '/checkout/?reference=' + response.reference;
        } else if (response.message) {
            $('#use_payment_method').html('');
            $('#card-form').html('<div class="alert alert-success">' + response.message + '</div>');
        } else {
            window.location.href = baseDir + '/checkout/';
        }
    }
}

function processStripePaymentOneClick(custom, token) {
    setPaymentInProcess(true);

    var formDatas = getFormInputs();
    formDatas['CSRFGuard_token'] = CSRFGuard_token;
    formDatas['custom'] = custom;
    formDatas['token'] = token;
    formDatas['stripe_use_token'] = true;

    $.post(
        baseDir + '/ajax/stripe/charge/',
        formDatas,
        function (result) {
            handleStripeServerResponse(result, custom, token);
        },
        'json'
    );
}
