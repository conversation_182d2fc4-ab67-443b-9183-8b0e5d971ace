function goToStep(step) {
    $('.screen').removeClass('active');
    $('#screen' + step).addClass('active');
}

function setPaymentInProcess(paymentInProcess)
{
    if (paymentInProcess) {
        let text = __('Paiement en cours');
        if ($('.card-add-card-form').length > 0) {
            text = __('Ajout de la carte en cours');
        }
        $('#order_errors').html('');
        $('#btnValidate').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left').html(text);
    } else {
        $('#btnValidate').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left').addClass('px-9').html(__('Valider'));
    }
}

function displayError(errorMessage)
{
    setPaymentInProcess(false);
    $('#errors').html('<div class="alert alert-custom alert-light-danger">' + errorMessage + '</div>');
    goToStep(1);
}

function getFormInputs()
{
    var values = {};
    $.each($('#payment_form').serializeArray(), function(i, field) {
        values[field.name] = field.value;
    });
    return values;
}