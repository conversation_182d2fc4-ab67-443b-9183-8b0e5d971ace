var form = document.querySelector('#checkout');
var threeDSecure;
var postNonce;

if (!firstPaymentAmount) {
    //verifyCard needs an amount
    firstPaymentAmount = 1;
}

braintree.client.create({
    authorization: authorization
}, function(err, clientInstance) {
    if (err) {
        displayError(err.message, 'payment');
        return;
    }
    createHostedFields(clientInstance);
    setUpThreeDSecure(clientInstance);

    if (addPaypal) {
        createPayPalButton(clientInstance);
    }
});

function createHostedFields(clientInstance) {
    braintree.hostedFields.create({
        client: clientInstance,
        styles: {
            'input': {
                'font-size': '14px',
                'color': '#3F4254',
                'font-family': 'Inter, Helvetica, "sans-serif"'
            },
            'input.invalid': {
                'color': '#F64E60'
            },
            'input.valid': {
                'color': '#1BC5BD'
            }
        },
        fields: {
            number: {
                selector: '#braintreeCardNumber',
                placeholder: '4111 1111 1111 1111'
            },
            cvv: {
                selector: '#braintreeCvv',
                placeholder: '123'
            },
            expirationDate: {
                selector: '#braintreeExpirationDate',
                placeholder: 'MM/YYYY'
            }
        }
    }, function (hostedFieldsErr, hostedFieldsInstance) {
        if (hostedFieldsErr) {
            displayError(hostedFieldsErr.message, 'payment');
            return;
        }

        hostedFieldsInstance.on('cardTypeChange', function (event) {
            if (event.cards.length == 1) {
                setBraintreeBrandIcon(event.cards[0].type);
            } else {
                setBraintreeBrandIcon('unknown');
            }
        });

        processBraintreePayment = function (firstPaymentAmount, billingAddress, custom, token) {
            setPaymentInProcess(true);

            hostedFieldsInstance.tokenize(function (tokenizeErr, payload) {
                if (tokenizeErr) {
                    displayError(tokenizeErr.message, 'payment');
                    return;
                }

                var email = $('#email').val();
                threeDSecure.verifyCard({
                    amount: firstPaymentAmount,
                    nonce: payload.nonce,
                    bin: payload.details.bin,
                    email: email,
                    billingAddress: billingAddress,
                    onLookupComplete: function (data, next) {
                        next();
                    }
                }, function (err, response) {
                    if (err) {
                        displayError(err.message, 'payment');
                        return;
                    }

                    if (response.liabilityShiftPossible && !response.liabilityShifted) {
                        displayError(__("L'authentification 3d Secure a échouée, merci de réessayer."), 'payment');
                        return;
                    }

                    var formDatas = getFormInputs();
                    formDatas['token_nonce'] = response.nonce;
                    formDatas['CSRFGuard_token'] = CSRFGuard_token;
                    if (custom) {
                        formDatas['custom'] = custom;
                    }
                    if (token) {
                        formDatas['token'] = token;
                    }
                    $.post(
                        baseDir + braintreeAjaxRoute,
                        formDatas,
                        function(result) {
                            handleBraintreeResponse(result);
                        },
                        'json'
                    );
                });
            });
        };
    });
}

function createPayPalButton(clientInstance)
{
    var deviceData;

    braintree.dataCollector.create({
        client: clientInstance,
        paypal: true
    }, function (err, dataCollectorInstance) {
        if (err) {
            displayError(err.message, 'payment');
            return;
        }

        deviceData = dataCollectorInstance.deviceData;
        $('#deviceData').val(deviceData);
    });

    braintree.paypalCheckout.create({
        client: clientInstance
    }, function (paypalCheckoutErr, paypalCheckoutInstance) {
        if (paypalCheckoutErr) {
            displayError(paypalCheckoutErr.message, 'payment');
            return;
        }

        paypal.Button.render({
            env: environment,
            style: {
                color: 'gold',
                shape: 'rect',
                label: 'pay',
                size: 'responsive',
                tagline: false
            },
            payment: function () {
                setPaymentInProcess(true);
                return paypalCheckoutInstance.createPayment({
                    flow: 'vault',
                    amount: firstPaymentAmount,
                });
            },
            onAuthorize: function (data, actions) {
                return paypalCheckoutInstance.tokenizePayment(data, function (err, payload) {
                    if (err) {
                        displayError(err.message, 'payment');
                        return;
                    }
                    setUpPayPalButton(payload);
                });
            },
            onCancel: function () {
                setPaymentInProcess(false);
            },
            onError: function (err) {
                displayError(err.message, 'payment');
            }
        }, '#paypal-container');
    });
}

function setUpPayPalButton(payload)
{
    setPaymentInProcess(false);

    $('#errors').html('');
    $('#paypal_token_nonce').val(payload.nonce);
    $('#paypal-infos .paypal-customer').html(payload.details.email);
    $('#paypal-infos').show();
    $('#paypal-container').hide();
    $('.card-form .form-group').hide();
}

function startBraintreePayPalPayment(custom, token)
{
    setPaymentInProcess(true);

    var formDatas = getFormInputs();
    formDatas['token_nonce'] = $('#paypal_token_nonce').val();
    formDatas['CSRFGuard_token'] = CSRFGuard_token;
    if (custom) {
        formDatas['custom'] = custom;
    }
    if (token) {
        formDatas['token'] = token;
    }
    $.post(
        baseDir + braintreeAjaxRoute,
        formDatas,
        function (result) {
            handleBraintreeResponse(result);
        },
        'json'
    );
}

function cancelPaypal()
{
    $('#paypal_token_nonce').val('');
    $('#paypal-infos .paypal-customer').html('');
    $('#paypal-infos').hide();
    $('#paypal-container').show();
    $('.card-form .form-group').show();
    setPaymentInProcess(false);
}

function setUpThreeDSecure(clientInstance)
{
    braintree.threeDSecure.create({
        version: 2, // Will use 3DS 2 whenever possible
        client: clientInstance
    }, function (threeDSecureErr, threeDSecureInstance) {
        if (threeDSecureErr) {
            displayError(threeDSecureErr.message, 'payment');
            return;
        }

        threeDSecure = threeDSecureInstance;
    });
}

function handleBraintreeResponse(response) {
    if (response.error) {
        $('#errors').html('<div class="alert alert-danger">' + response.error + '</div>');
        setPaymentInProcess(false);
        if (response.error.match(/nom/g) || response.error.match(/email/g)) {
            goToStep(1);
        }
        return;
    }

    if (typeof redirection !== 'undefined') {
        window.location.href = redirection;
    } else if (response.redirection) {
        window.location.href = response.redirection;
    } else if (response.reference) {
        window.location.href = baseDir + '/checkout/?reference=' + response.reference;
    } else if (response.message) {
        $('#card-form').html('<div class="alert alert-success">' + response.message + '</div>');
    } else {
        window.location.href = baseDir + '/checkout/';
    }
}

var braintreeCardBrandToPfClass = {
    'visa': 'fab fa-cc-visa',
    'master-card': 'fab fa-cc-mastercard',
    'american-express': 'fab fa-cc-amex',
    'discover': 'fab fa-cc-discover',
    'diners-club': 'fab fa-cc-diners-club',
    'jcb': 'fab fa-cc-jcb',
    'unknown': 'fas fa-credit-card',
}

function setBraintreeBrandIcon(brand) {
    var brandIconElement = $('#braintree-brand-icon');
    var pfClass = 'fas fa-credit-card';
    if (brand in braintreeCardBrandToPfClass) {
        pfClass = braintreeCardBrandToPfClass[brand];
    }
    brandIconElement.removeAttr('class').addClass(pfClass);
}

function processBraintreePaymentOneClick(custom, token)
{
    setPaymentInProcess(true);

    var formDatas = getFormInputs();
    formDatas['CSRFGuard_token'] = CSRFGuard_token;
    formDatas['custom'] = custom;
    formDatas['token'] = token;
    formDatas['use_card'] = true;

    $.post(
        baseDir + '/ajax/braintree/charge/',
        formDatas,
        function (result) {
            handleBraintreeResponse(result);
        },
        'json'
    );
}
