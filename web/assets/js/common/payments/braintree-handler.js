$(document).ready(function() {
    $('#btnValidate').on('click', function() {
        processBraintreePayment(firstPaymentAmount, billingAddress);
    });
});

function setUpPayPalButton(payload)
{
    setPaymentInProcess(false);

    $('#errors').html('');
    $('#paypal_token_nonce').val(payload.nonce);
    $('#paypal-infos .paypal-customer').html(payload.details.email);
    $('#paypal-infos').show();
    $('#paypal-container').hide();
    $('.card-form .form-group').hide();

    $('#btnValidate').off('click', processBraintreePayment);
    $('#btnValidate').on('click', startBraintreePayPalPayment);
}

function cancelPaypal()
{
    $('#paypal_token_nonce').val('');
    $('#paypal-infos .paypal-customer').html('');
    $('#paypal-infos').hide();
    $('#paypal-container').show();
    $('.card-form .form-group').show();
    setPaymentInProcess(false);
    $('#btnValidate').off('click', startBraintreePayPalPayment);
    $('#btnValidate').on('click', processBraintreePayment);
}