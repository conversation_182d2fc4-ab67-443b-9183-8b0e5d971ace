let offcanvasPanel;
let offcanvasId = 'offcanvas_panel';

$(document).ready(function() {
    let offcanvasElement = KTUtil.getById(offcanvasId);
    offcanvasPanel = new KTOffcanvas(offcanvasElement, {
        overlay: true,
        baseClass: 'offcanvas',
        placement: 'right',
        closeBy: offcanvasId + '_close',
        toggleBy: offcanvasId + '_toggle'
    });

    $(document).on('click', 'a[data-action="ajax"]', function(e) {
        e.preventDefault();
        displayOffcanvasPanel('', '', true);
        $.get(
            $(this).data('href'),
            { CSRFGuard_token:CSRFGuard_token },
            function(data) {
                if (data.status) {
                    displayOffcanvasPanel(data.title, data.html, false);
                    if (data.actions) {
                        $('#' + offcanvasId + ' .offcanvas-header-actions').html(data.actions);
                    }
                } else {
                    KTApp.unblock('#' + offcanvasId);
                    ShowToast('error', data.message);
                }
            },
            'json'
        ).fail(function(response) {
            hideOffcanvasPanel();
            let responseText = JSON.parse(response.responseText);
            ShowToast('error', responseText.message);
        });
        return false;
    });

    $(document).on('submit', '#' + offcanvasId + ' form', function(e) {
        e.preventDefault();
        processPanelForm(offcanvasId);
        return false;
    });

    if ($('.offcanvas-panel').length) {
        $('.offcanvas-panel').each(function() {
            let id = $(this).attr('id');

            $(this).find('form').on('submit', function (e) {
                e.preventDefault();
                processPanelForm(id);
                return false;
            });
        });
    }
});

function processPanelForm(id) {
    KTApp.block('#' + id, {});
    let formData = $('#' + id + ' form').serializeArray();
    $.post(
        baseDir + '/ajax/modal/form/',
        formData,
        function(result) {
            if (result.status) {
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else {
                    window.location.reload();
                }
            } else {
                KTApp.unblock('#' + id);
                $('#' + id).find('button[type="submit"]').removeClass('spinner spinner-left').prop('disabled', false);
                ShowToast('error', result.message);
            }
        },
        'json'
    ).fail(function(response) {
        KTApp.unblock('#' + id);
        let responseText = JSON.parse(response.responseText);
        ShowToast('error', responseText.message);
    });
}

function displayOffcanvasPanel(title, html, block) {
    $('#' + offcanvasId + ' h4').html(title);
    $('#' + offcanvasId + ' .offcanvas-content').html(html);
    $('#' + offcanvasId + ' .offcanvas-header-actions').html('');
    offcanvasPanel.show();
    if (block) {
        KTApp.block('#' + offcanvasId, {});
    } else {
        KTApp.unblock('#' + offcanvasId);
    }
}

function hideOffcanvasPanel() {
    offcanvasPanel.hide();
    KTApp.unblock('#offcanvas-panel');
}
