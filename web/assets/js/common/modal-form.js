$(document).ready(function() {
    if ($('.modal-form').length) {
        $('.modal-form').each(function() {
            let id = $(this).attr('id');

            if ($(this).hasClass('modal-form-tutorial')) {
                let formId = $(this).find('form').attr('id');
                let form = KTUtil.getById(formId);
                form.submit = function(){
                    processModalForm(id);
                    return false;
                };
            } else {
                $(this).find('form').on('submit', function (e) {
                    e.preventDefault();
                    processModalForm(id);
                    return false;
                });
            }
        });
    }
});

function openModalForm(id) {
    $('#ModalForm' + id).modal('show');
}

function processModalForm(id) {
    KTApp.block('#' + id + ' .modal-content', {});
    $('#' + id + ' #errors').html('');
    let formData = $('#' + id + ' form').serializeArray();
    $.post(
        baseDir + '/ajax/modal/form/',
        formData,
        function(result) {
            if (result.status) {
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else {
                    window.location.reload();
                }
            } else {
                $('#' + id).animate({ scrollTop: 0 }, 'slow');
                KTApp.unblock('#' + id + ' .modal-content');
                $('#' + id).find('button[type="submit"]').removeClass('spinner spinner-left').prop('disabled', false);
                $('#' + id + ' #errors').html('<div class="alert alert-custom alert-light-danger">' + result.message + '</div>');
            }
        },
        'json'
    ).fail(function(response) {
        $('#' + id).animate({ scrollTop: 0 }, 'slow');
        KTApp.unblock('#' + id + ' .modal-content');
        let responseText = JSON.parse(response.responseText);
        ShowToast('error', responseText.message);
    });
}
