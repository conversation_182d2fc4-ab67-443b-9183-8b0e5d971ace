"use strict";

var firstPaymentAmount = 0;
var custom = '';
var token = '';
var wizardObj;

const strongPassword = function () {
    return {
        validate: function (input) {
            const value = input.value;
            if (value === '') {
                return {
                    valid: true,
                };
            }

            // Check the password strength
            if (value.length < 12) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any uppercase character
            if (value === value.toLowerCase()) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any uppercase character
            if (value === value.toUpperCase()) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any digit
            if (value.search(/[0-9]/) < 0) {
                return {
                    valid: false,
                };
            }

            let specialCars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
            if (!specialCars.test(value)) {
                return {
                    valid: false,
                };
            }

            return {
                valid: true,
            };
        },
    };
};

$(document).ready(function() {
    initWizard();
    initAttributes();

    $('[data-rel="select2"],[rel="select2"]').select2({
        language: (appLanguage == 'fr_FR' ? "fr" : "en"),
    });

    if ($('input[name="installment"]').length > 0) {
        setInstallments();
    }
    if ($('input[name="id_payment"]').length > 0) {
        setPaymentTypes();
    }

    if ($('.company-choice').length > 0) {
        $('input[name="company_choice"]').on('change', function() {
            if ($(this).val() == 'not_has_company') {
                $('.company-container').addClass('d-none');
            } else {
                $('.company-container').removeClass('d-none');
            }
        })
    }

    $('select#country').on('change', function() {
        updateCart();
    });

    if ($('input[name="tva_intracom"]').length > 0) {
        var timerTvaIntracom;
        $('input[name="tva_intracom"]').on('input', function(e) {
            var tvaIntracom = $(this).val();
            if(tvaIntracom !== '' && tvaIntracom.length >= 3) {
                clearTimeout(timerTvaIntracom);
                timerTvaIntracom = setTimeout(function() {
                    updateCart();
                }, 700);
            }
        });
    }

    $('.toggle-password').each(function() {
        let passwordField = $($(this).data('target'));
        $(this).on('click', function () {
            let type = passwordField.attr("type") === "password" ? "text" : "password";
            passwordField.attr("type", type);
            $(this).toggleClass('fa-eye-slash');
        });
    });
});

function initWizard() {
    if ($('#kt_wizard').length > 0) {
        var _formEl = KTUtil.getById('kt_wizard');
        var _validations = [];
        var stepCustomerId = $('#step_customer').data('step');

        FormValidation.validators.checkPassword = strongPassword;

        var fieldsStep1 = {};
        var fieldsStep2 = {};
        var fieldsStep3 = {};
        var fieldsStep4 = {};

        var fieldsStep1 = {
            first_name: {
                validators: {
                    notEmpty: {
                        message: __('Veuillez entrer votre prénom')
                    }
                }
            },
            last_name: {
                validators: {
                    notEmpty: {
                        message: __('Veuillez entrer votre nom')
                    }
                }
            },
            email: {
                validators: {
                    notEmpty: {
                        message: __('Veuillez entrer votre adresse email')
                    },
                    emailAddress: {
                        message: __('Veuillez entrer une adresse email valide')
                    }
                }
            }
        };


        if (($('#kt_form #password').length > 0 && $('#kt_form #password').attr('required')) || ($('#payment_form #password').length > 0 && $('#payment_form #password').attr('required'))) {
            fieldsStep1.password = {
                validators: {
                    notEmpty: {
                        message: __('Veuillez entrer un mot de passe')
                    },
                    checkPassword: {
                        message:  __('Veuillez indiquer un mot de passe de 12 caractères minimum, comportant au moins une lettre majuscule, un chiffre et un caractère spécial (parmi #@&:.;,+=-_(){}<>/!?).')
                    },
                }
            };
            fieldsStep1.password2 = {
                validators: {
                    notEmpty: {
                        message: __('Veuillez confirmer le mot de passe')
                    },
                    checkPassword: {
                        message:  __('Veuillez indiquer un mot de passe de 12 caractères minimum, comportant au moins une lettre majuscule, un chiffre et un caractère spécial (parmi #@&:.;,+=-_(){}<>/!?).')
                    },
                    identical: {
                        compare: function () {
                            return document.getElementById('password').value;
                        },
                        message: __('Les 2 mots de passe sont différents'),
                    },
                }
            };
        }

        /*if (stepCustomerId == 1) {
            fieldsStep1 = fieldsStep2;
            fieldsStep2 = {};
        }*/

        $('#kt_form input[type="text"], #kt_form input[type="email"], #kt_form textarea, #payment_form input[type="text"], #payment_form input[type="email"], #payment_form textarea').each(function() {
            if ($(this).attr('required')) {
                let inputName = $(this).attr('name');
                if ((stepCustomerId == 1 && typeof fieldsStep1[inputName] === 'undefined') || typeof fieldsStep2[inputName] === 'undefined') {
                    let validator = {
                        validators: {
                            notEmpty: {
                                message: __('Veuillez remplir cette information')
                            }
                        }
                    };

                    let idStep = $(this).closest('.row-step').data('step');
                    if (idStep == 1) {
                        fieldsStep1[inputName] = validator;
                    } else if (idStep == 2) {
                        fieldsStep2[inputName] = validator;
                    } else if (idStep == 3) {
                        fieldsStep3[inputName] = validator;
                    } else {
                        fieldsStep4[inputName] = validator;
                    }
                }
            }
        });

        $('#kt_form input[type="radio"], #kt_form input[type="checkbox"], #payment_form input[type="radio"], #payment_form input[type="checkbox"]').each(function() {
            if ($(this).attr('required')) {
                let inputName = $(this).attr('name');
                let validator = {
                    validators: {
                        choice: {
                            min:1,
                            message: (inputName == 'cgv-accept' ? __('Veuillez accepter les conditions générales de vente') : __('Veuillez sélectionner une option'))
                        }
                    }
                };

                let idStep = $(this).closest('.row-step').data('step');
                if (idStep == 1) {
                    fieldsStep1[inputName] = validator;
                } else if (idStep == 2) {
                    fieldsStep2[inputName] = validator;
                } else if (idStep == 3) {
                    fieldsStep3[inputName] = validator;
                } else {
                    fieldsStep4[inputName] = validator;
                }
            }
        });

        if (fieldsStep1) {
            _validations[1] = FormValidation.formValidation(
                _formEl,
                {
                    fields: fieldsStep1,
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap({
                            eleValidClass: '',
                        })
                    }
                }
            );
        }

        if (fieldsStep2) {
            _validations[2] = FormValidation.formValidation(
                _formEl,
                {
                    fields: fieldsStep2,
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap({
                            eleValidClass: '',
                        })
                    }
                }
            );
        }

        if (fieldsStep3) {
            _validations[3] = FormValidation.formValidation(
                _formEl,
                {
                    fields: fieldsStep3,
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap({
                            eleValidClass: '',
                        })
                    }
                }
            );
        }

        if (fieldsStep4) {
            _validations[4] = FormValidation.formValidation(
                _formEl,
                {
                    fields: fieldsStep4,
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap({
                            eleValidClass: '',
                        })
                    }
                }
            );
        }

        wizardObj = new KTWizard('kt_wizard', {
            startStep: 1, // initial active step number
            clickableSteps: false  // allow step clicking
        });

        // Validation before going to next page
        wizardObj.on('change', function (wizard) {
            if (wizard.getStep() > wizard.getNewStep()) {
                return; // Skip if stepped back
            }

            var validator = _validations[wizard.getStep()];
            if (validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        wizard.goTo(wizard.getNewStep());
                    } else {
                        if (wizard.getStep() == 1 && $('.wizard-step-user-hidden').length > 0 && $('.wizard-step-user-hidden').hasClass('d-none')) {
                            displayCustomerFields();
                        }
                    }
                    var wizardId = KTUtil.getById('kt_wizard');
                    KTUtil.scrollTo(wizardId);
                });
            } else {
                return true;
            }

            return false;
        });

        // Change event
        wizardObj.on('changed', function (wizard) {
            var wizardId = KTUtil.getById('kt_wizard');
            KTUtil.scrollTo(wizardId);
        });

        // Submit event
        wizardObj.on('submit', function (wizard) {
            validateForm();
        });
    }
}

function initAttributes() {
    if ($('select.product-attribute').length > 0) {
        $('select.product-attribute').on('change', function () {
            updateCart();
        });
    }

    if ($('.product-attribute-color').length > 0) {
        $('.product-attribute-color').on('click', function () {
            $('.product-attribute-color .symbol').removeClass('border-primary');
            $(this).find('.symbol').addClass('border-primary');
            let target = $(this).data('target');
            let value = $(this).data('value');
            $('#' + target).val(value);
            updateCart();
        });
    }

    $('#card-products [data-rel="tooltip"]').tooltip();
}

function increaseQty(id_product) {
    var actualQuantity = $('#quantity' + id_product).val();
    actualQuantity++;
    $('#quantity' + id_product).val(actualQuantity);
    updateCart();
}

function decreaseQty(id_product) {
    var actualQuantity = $('#quantity' + id_product).val();
    actualQuantity--;
    $('#quantity' + id_product).val(actualQuantity);
    updateCart();
}

function setInstallments()
{
    $('input[name="installment"]').on('change', function() {
        $('.installment').hide();
        $('#installment' + $(this).data('installment')).show();

        var firstPaymentType = $('#installment' + $(this).data('installment')).find('input[name="id_payment"]').first();
        $('#' + firstPaymentType.prop('id')).trigger('change');
    });
}

function setPaymentTypes()
{
    $('input[name="id_payment"]').on('change', function() {
        $('.payment').hide();
        $(this).prop('checked', true);
        $('.payment.' + $(this).data('type')).show();
    });
}

function goToStep(stepTitle) {
    var step = stepTitle;
    if ($('#step_' + stepTitle).length > 0) {
        step = $('#step_' + stepTitle).data('step');
    }
    wizardObj.goTo(step);
}

function getFormInputs()
{
    var values = {};
    $.each($('#kt_form, #payment_form').serializeArray(), function(i, field) {
        if (field.name.endsWith('[]')) {
            var fieldName = field.name;
            fieldName = fieldName.substring(0, fieldName.length - 2);
            if (!(fieldName in values)) {
                values[fieldName] = [];
            }
            values[fieldName].push(field.value);
        } else {
            values[field.name] = field.value;
        }
    });
    $.each($('#card-products').find('select, textarea, input').serializeArray(), function(i, field) {
        values[field.name] = field.value;
    });
    $.each($('.block_products input'), function(i, field) {
        values[field.name] = field.value;
    });
    $.each($('.block_products select'), function(i, field) {
        values[field.name] = field.value;
    });
    return values;
}

function setPaymentInProcess(paymentInProcess)
{
    if (paymentInProcess) {
        $('.alert-text').html('');
        $('.alert-text').parent().addClass('d-none');
        $('#kt_form_submit').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left disabled').html(__('Traitement'));
        $('#btnValidate').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left disabled').html(__('Traitement'));
    } else {
        $('#kt_form_submit').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left disabled').addClass('px-9').html(__('Valider'));
        $('#btnValidate').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left disabled').addClass('px-9').html(__('Valider'));
    }
}

function displayError(errorMessage, step)
{
    if (!step) {
        step = 'cart';
    }

    if (step == 'cart' && !$('#step_cart').length && $('#step_product').length) {
        step = 'product';
    }
    if (step == 'product' && !$('#step_product').length && $('#step_cart').length) {
        step = 'cart';
    }
    if (step == 'address' && !$('#step_address').length && $('#step_customer').length) {
        step = 'customer';
    }

    setPaymentInProcess(false);

    let target = '#order_errors_' + step;
    if (!$('#order_errors_' + step).length) {
        if ($('#errors' + step).length) {
            target = '#errors' + step;
        } else if ($('#step_' + step).length) {
            target = '#errors' + $('#step_' + step).data('step');
        }
    }

    if (!$(target + ' .alert-text').length) {
        var alert = '<div class="alert alert-custom alert-light-danger fade show mb-5" role="alert">\n' +
            '    <div class="alert-icon"><i class="fas fa-exclamation-triangle"></i></div>\n' +
            '    <div class="alert-text">' + errorMessage + '</div>\n' +
            '    <div class="alert-close">\n' +
            '        <button type="button" class="close" data-dismiss="alert" aria-label="Close">\n' +
            '            <span aria-hidden="true"><i class="fas fa-times"></i></span>\n' +
            '        </button>\n' +
            '    </div>\n' +
            '</div>';
        $(target).html(alert);
    } else {
        $(target + ' .alert-text').html(errorMessage);
    }

    $(target).removeClass('d-none');
    $(target + ' .alert').removeClass('d-none');

    goToStep(step);
}

function validateForm()
{
    setPaymentInProcess(true);

    var formDatas = getFormInputs();
    formDatas['id_product'] = id_product;
    formDatas['CSRFGuard_token'] = CSRFGuard_token;
    formDatas['url'] = window.location.href;

    $.post(
        baseDir + '/ajax/shop/product/order/',
        formDatas,
        function(result) {
            if (result.valid) {
                firstPaymentAmount = result.firstPaymentAmount;
                $('#btnValidate').html(__('Paiement'));
                if (result.paymentType == 'braintree') {
                    if (result.paymentSubType == 'paypal') {
                        startBraintreePayPalPayment(result.custom, result.token);
                    } else if (result.paymentSubType == 'oneclick') {
                        processBraintreePaymentOneClick(result.custom, result.token);
                    } else {
                        processBraintreePayment(result.firstPaymentAmount, result.braintreeBillingAddress, result.custom, result.token);
                    }
                } else if (result.paymentType == 'stripe') {
                    if (result.paymentSubType == 'oneclick') {
                        processStripePaymentOneClick(result.custom, result.token);
                    } else {
                        processStripePayment(result.stripeBillingAddress, result.custom, result.token);
                    }
                } else if (result.paymentType == 'mollie') {
                    if (result.paymentSubType == 'oneclick') {
                        processMolliePaymentOneClick(result.custom, result.token);
                    } else if (result.paymentSubType == 'paypal') {
                        processMolliePaypal(result.custom);
                    } else {
                        processMolliePayment(result.custom);
                    }
                } else if (result.paymentType == 'free') {
                    processFreeOrder(result.custom);
                } else {
                    displayError(__('Une erreur est survenue.'), 'payment');
                }
            } else {
                if (typeof result.step !== 'undefined') {
                    displayError(result.message, result.step);
                } else {
                    displayError(result.message, 'product');
                }
            }
        },
        'json'
    );
}

function displayDiscountForm()
{
    $('#formAddDiscount').show();
}
function hideDiscountForm()
{
    $('#formAddDiscount').hide();
}

function addDiscount() {
    $('#formAddDiscount #discountError').html('');
    var discount = $('#formAddDiscount #discount').val();
    if (discount == '') {
        $('#formAddDiscount #discountError').html('<div class="alert alert-custom alert-light-danger">' + __('Veuillez entrer le code de votre bon de réduction') + '</div>');
    } else {
        $.post(
            baseDir + '/ajax/shop/discount/add/',
            {
                id_product: id_product,
                discount: discount,
                CSRFGuard_token: CSRFGuard_token
            },
            function(data) {
                if (data.status) {
                    updateCart();
                } else {
                    $('#formAddDiscount #discountError').html('<div class="alert alert-custom alert-light-danger">'+data.message+'</div>');
                }
            },
            'json'
        );
    }
}

function deleteDiscount() {
    $('.discount').remove();
    $.post(
        baseDir + '/ajax/shop/discount/delete/',
        {
            id_product: id_product,
            CSRFGuard_token: CSRFGuard_token
        },
        function(data) {
            if (data.status) {
                updateCart();
            } else {
                $('#formAddDiscount #discountError').html('<div class="alert alert-custom alert-light-danger">'+data.message+'</div>');
            }
        },
        'json'
    );
}

function getCart()
{
    var values = {};
    $('#card-products :input').each(function() {
        values[this.name] = $(this).val();
    });
    return values;
}

function updateCart() {
    var formDatas = getFormInputs();
    formDatas['id_product'] = id_product;
    formDatas['CSRFGuard_token'] = CSRFGuard_token;
    formDatas['cart'] = getCart();

    addLoader();

    $.post(
        baseDir + '/ajax/shop/cart/update/',
        formDatas,
        function(data) {
            removeLoader();
            if (data.status) {
                $('#card-products').replaceWith(data.blockProducts);
                initAttributes();
                if (data.alert) {
                    if (data.step) {
                        displayError(data.alert, data.step);
                    } else {
                        displayError(data.alert, 'customer');
                    }
                }
            } else {
                displayError(data.message, 'customer');
            }
        },
        'json'
    );
}

function addLoader()
{
    KTApp.block('#card-products', {});
    $('.wizard-footer button').attr('disabled', true);
}

function removeLoader()
{
    KTApp.unblock('#card-products', {});
    $('.wizard-footer button').attr('disabled', false);
}

function displayCustomerFields() {
    $('.wizard-step-content-user-logged').addClass('d-none');
    $('.wizard-step-user-hidden').removeClass('d-none');
}

function processFreeOrder(custom) {
    let formData = getFormInputs();
    formData['CSRFGuard_token'] = CSRFGuard_token;
    formData['custom'] = custom;

    $.post(
        baseDir + '/ajax/free-order/process/',
        formData,
        function (result) {
            if (result.status) {
                window.location.href = result.redirection;
            } else {
                displayError(result.message, 'payment');
                setPaymentInProcess(false);
            }
        },
        'json'
    );
}
