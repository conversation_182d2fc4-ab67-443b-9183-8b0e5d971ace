function __(s) {
    let text = s;
    if (arguments.length == 1) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i == 0) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}

function n__(s, p, c) {
    let text = c == 1 ? s : p;
    if (arguments.length == 3) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i <= 2) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}