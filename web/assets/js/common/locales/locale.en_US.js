i18next.init({ debug: false, keySeparator: false, nsSeparator: false, returnEmptyString: false, lng: 'en_US', resources: {'en_US' : { translation: {"Une erreur est survenue.":"","Une erreur est survenue":"","Veuillez entrer votre adresse email":"","Erreur":"","Veuillez entrer une adresse email valide":"","Connexion":"","Veuillez entrer un mot de passe":"","Envoyer les notifications par email":"","R\u00e9ponse":"","Aide":"","Rechercher":"","Supprimer":"","Veuillez entrer le code de votre bon de r\u00e9duction":"","Annuler":"","Valider":"","Aper\u00e7u":"","S\u00e9lectionn\u00e9":"","S\u00e9lectionner":"","Ajouter un renseignement":"","Fermer":"","R\u00e9initialiser":"","F\u00e9licitations !":"","Voir ce client":"","Modifier un renseignement":"","Recherche":"","Confirmation":"","Reconnexion":"","Veuillez entrer votre mot de passe":"","Ne pas envoyer de notifications par email":"","Tapez votre r\u00e9ponse ici...":"","Aucun r\u00e9sultat trouv\u00e9.":"","abonnements s\u00e9lectionn\u00e9s":"","1 abonnement s\u00e9lectionn\u00e9":"","\u00c9l\u00e9ments par page":"","transactions s\u00e9lectionn\u00e9es":"","1 transaction s\u00e9lectionn\u00e9e":"","clients s\u00e9lectionn\u00e9s":"","1 clients s\u00e9lectionn\u00e9":"","commissions s\u00e9lectionn\u00e9es":"","1 commission s\u00e9lectionn\u00e9e":"","T\u00e9l\u00e9charger le fichier":"","Merci d'indiquer le nom du produit":"","Merci d'indiquer le montant du produit":"","Merci de s\u00e9lectionner une r\u00e8gle de TVA":"","Merci de s\u00e9lectionner un type de produit":"","La position des moyens de paiement a \u00e9t\u00e9 enregistr\u00e9 avec succ\u00e8s":"","Traitement...":"","Le renseignement a bien \u00e9t\u00e9 ajout\u00e9":"","Cr\u00e9ation du fichier PDF... Encore quelques secondes...":"","Etes-vous s\u00fbr de vouloir supprimer ce ticket ?":"","Ticket supprim\u00e9 avec succ\u00e8s":"","R\u00e9glages sauvegard\u00e9s":"","Etes-vous s\u00fbr de vouloir r\u00e9initialiser tous les param\u00e8tres ?":"","Veuillez entrer au moins 3 caract\u00e8res":"","La position des liens a \u00e9t\u00e9 enregistr\u00e9e avec succ\u00e8s":"","Vous \u00eates connect\u00e9 en tant qu'affili\u00e9. Cliquez sur le bouton ci-dessous pour vous reconnecter en tant qu'administrateur.":"","Vous \u00eates connect\u00e9 en tant que client. Cliquez sur le bouton ci-dessous pour vous reconnecter en tant qu'administrateur.":"","Paiement en cours":"","L'authentification 3d Secure a \u00e9chou\u00e9e, merci de r\u00e9essayer.":"","Copi\u00e9 !":"","Veuillez entrer votre pr\u00e9nom":"","Veuillez entrer votre nom":"","Veuillez entrer l'adresse de votre compte":"","Nom Pr\u00e9nom":"","Veuillez entrer votre adresse email valide":"","Une erreur est survenue, v\u00e9rifiez que vous avez bien indiqu\u00e9 une adresse email valide":"","Une ou plusieurs erreurs sont survenues, v\u00e9rifiez que vous avez bien indiqu\u00e9 votre adresse email et votre mot de passe":""} }, }});

function __(s) {
    let text = i18next.t(s);
    if (arguments.length == 1) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i == 0) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}

function n__(s, p, c) {
    let text = i18next.t(s, {count: c});
    if (arguments.length == 3) {
        return text;
    }
    let argumentsArray = [];
    for (let i in arguments) {
        if (i <= 2) {
            continue;
        }
        argumentsArray.push(arguments[i]);
    }
    return vsprintf(text, argumentsArray);
}