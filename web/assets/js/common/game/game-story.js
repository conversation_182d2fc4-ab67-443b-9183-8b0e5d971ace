let idStep = 0;
let storyWorking = false;
let typeWriterInterval;
let stepTimeout;
let step = steps[idStep];

$(document).ready(function() {
    displayNewMessage();
    initScroll();
});

function restartStory() {
    idStep = 0;
    step = steps[idStep];
    displayNewMessage();
}

function displayNewMessage() {
    displayModal();

    let avatar = getAvatar();
    let stepUser = getUserName();
    let content = '' +
    '\t\t\t<div class="new-message-container animate__animated animate__fadeInUp">\n' +
    '\t\t\t\t<div class="new-message-header">\n' +
    '\t\t\t\t\t<h6>' + __('Nouveau message') + '</h6>\n' +
    '\t\t\t\t</div>\n' +
    '\t\t\t\t<div class="new-message-body">\n' +
    '\t\t\t\t\t<div class="symbol symbol-60 symbol-circle mr-4"><img src="' + avatar + '"></div>\n' +
    '\t\t\t\t\t<div class="new-message-content" id="new-message-content"><p><strong>' + stepUser + '</strong>' + step.content + '</p></div>\n' +
    '\t\t\t\t</div>\n' +
    '\t\t\t</div>\n' +
    '\t\t\t<div class="d-flex flex-column animate__animated animate__fadeIn animate__delay-1s">\n' +
    '\t\t\t\t<button type="button" class="btn btn-warning btn-lg font-weight-bold" onclick="startStory();">' + __('Répondre') + '</button>\n' +
    '\t\t\t\t<button type="button" class="btn btn-link" data-dismiss="modal">' + __('Ignorer') + '</button>\n' +
    '\t\t\t</div>\n';
    $('#storyModal .new-message').append(content);
}

function startStory() {
    $('#storyModal .new-message').addClass('d-none');
    $('#storyModal .story-container').removeClass('d-none');
    displayStep(true);
    setDocumentClick();
}

function endStory() {
    $('#storyModal .story-footer button').html('Terminer');
}

function goToStep(stepId) {
    if (storyWorking) {
        return;
    }

    step = steps[stepId];
    if (!step) {
        endStory();
        return;
    }
    idStep = stepId;
    displayStep();
}

function goToNextStep() {
    if (storyWorking) {
        return;
    }
    if (step.type == 'option') {
        return;
    }
    idStep++;
    step = steps[idStep];
    if (!step) {
        endStory();
        return;
    }
    if (step.type == 'option') {
        displayStep(true);
    } else {
        displayStep();
    }
}

function goToPrevStep() {
    if (storyWorking) {
        return;
    }
    idStep--;
    if (idStep < 0) {
        idStep = 0;
        return;
    }
    step = steps[idStep];
    if (!step) {
        return;
    }
    displayStep();
}

function displayStep(instant) {
    storyWorking = true;
    if (instant) {
        if (!$('#step' + idStep).length) {
            let stepMessage = getStepMessage();
            $('#storyModal .story-messages').append(stepMessage);
        } else {
            let stepContent = getStepContent();
            $('#step' + idStep + ' .message-content').html(stepContent);
        }
        scrollMessages();
        storyWorking = false;
        goToNextStep();
    } else {
        let stepMessage = getStepMessage(true);
        $('#storyModal .story-messages').append(stepMessage);
        scrollMessages();
        stepTimeout = setTimeout(function() {
            let stepContent = getStepContent();
            $('#step' + idStep + ' .message-content').html(stepContent);
            scrollMessages();
            storyWorking = false;
            setTimeout(function() {
                goToNextStep();
            }, 1500);
        }, 3000);
    }
}

function getAvatar()
{
    let avatar = admin.avatar;
    if (step.user == 'user') {
        avatar = user.avatar;
    }
    return avatar;
}

function getUserName() {
    let userName = admin.name;
    if (step.user == 'user') {
        userName = user.name;
    }
    return userName;
}

function getStepMessage(dots) {
    let stepMessage = getStepContent();
    if (dots) {
        stepMessage = '<div class="text"><div class="dot-flashing"></div></div>';
    }
    let userName = getUserName();
    let avatar = getAvatar();
    let content = '' +
        '<div class="d-flex flex-column mb-5 message ' + (step.user == 'user' ? 'me' : '') + '" id="step' + idStep + '">\n' +
        '\t<div class="d-flex align-items-center">\n' +
        '\t\t<div class="symbol symbol-circle symbol-40">\n' +
        '\t\t\t<img alt="' + userName + '" src="' + avatar + '">\n' +
        '\t\t</div>\n' +
        '\t\t<div class="name">\n' +
        '\t\t\t<span class="text-dark-75 text-hover-primary font-weight-bold font-size-h6">' + userName + '</span>\n' +
        '\t\t</div>\n' +
        '\t</div>\n' +
        '\t<div class="message-content">' + stepMessage + '</div>\n' +
        '</div>';
    return content;
}

function getStepContent() {
    if (!step.type || step.type == 'text') {
        return '<div class="text">' + step.content + '</div>';
    }
    if (step.type == 'image') {
        return '<div class="image" onclick="displayImageModal(\'' + step.content + '\');"><img src="' + step.content + '"></div>';
    }
    if (step.type == 'video') {
        return '<div class="video" onclick="displayVideoModal(\'' + step.content + '\');"><img src="' + step.thumbnail + '"></div>';
    }
    if (step.type == 'option') {
        let content = '';
        $.each(step.options, function(index, option) {
            content += '<button class="btn btn-primary btn-block" onclick="goToStep(' + option.step + ');">' + option.content + '</button>';
        });
        return content;
    }
}

function displayModal() {
    let iphoneHeight = 868;
    let bodyHeight = $('body').height();
    let scale = '';
    if (iphoneHeight > bodyHeight) {
        let ratio = bodyHeight / iphoneHeight;
        scale = 'style="transform: scale(' + ratio + '); transform-origin: top;"';
    }

    let modal = '<div class="modal fade show" id="storyModal" tabindex="-1" role="dialog" aria-labelledby="storyModal" aria-modal="true" style="display: block;">\n' +
        '\t<div class="modal-dialog modal-dialog-centered modal-lg modal-story" role="document">\n' +
        '\t\t<div class="modal-content">\n' +
        '\t\t\t<div class="modal-body">\n' +
        '\t\t\t\t<div id="iphone-x">\n' +
        '\t\t\t\t\t<div class="device device-iphone-x" ' + scale + '>\n' +
        '\t\t\t\t\t\t<div class="device-content">\n' +
        '\t\t\t\t\t\t\t<div class="new-message">\n' +
        '\t\t\t\t\t\t\t\t<div class="new-message-overlay"></div>\n' +
        '\t\t\t\t\t\t\t</div>\n' +
        '\t\t\t\t\t\t\t<div class="story-container d-none">\n' +
        '\t\t\t\t\t\t\t\t<div class="story-header">\n' +
        '\t\t\t\t\t\t\t\t\t<h6>' + __('Messages') + '</h6>\n' +
        '\t\t\t\t\t\t\t\t</div>\n' +
        '\t\t\t\t\t\t\t\t<div class="story-messages" id="story-messages"></div>\n' +
        '\t\t\t\t\t\t\t\t<div class="story-footer">\n' +
        '\t\t\t\t\t\t\t\t\t<button type="button" class="btn btn-secondary btn-block" onclick="closeModal();">Fermer</button>\n' +
        '\t\t\t\t\t\t\t\t</div>\n' +
        '\t\t\t\t\t\t\t</div>\n' +
        '\t\t\t\t\t\t</div>\n' +
        '\t\t\t\t\t\t<div class="device-stripe"></div>\n' +
        '\t\t\t\t\t\t<div class="device-header"></div>\n' +
        '\t\t\t\t\t\t<div class="device-sensors"></div>\n' +
        '\t\t\t\t\t\t<div class="device-btns"></div>\n' +
        '\t\t\t\t\t\t<div class="device-power"></div>\n' +
        '\t\t\t\t\t\t<div class="device-home"></div>\n' +
        '\t\t\t\t\t</div>\n' +
        '\t\t\t\t</div>\n' +
        '\t\t\t</div>\n' +
        '\t\t</div>\n' +
        '\t</div>\n' +
        '</div>';
    $('body').append(modal);
    $('#storyModal').modal({
        backdrop: 'static',
        keyboard: false
    });
    $('#storyModal').on('hidden.bs.modal', function () {
        $('#storyModal').remove();
        $('#storyModal').off('click');
    });
}

function displayImageModal(imgUrl) {
    $('#ModalImage').remove();
    let modal = '\n' +
        '<div class="modal fade" id="ModalImage" tabindex="-1" role="dialog" aria-labelledby="ModalImage" aria-hidden="true">\n' +
        '    <div class="modal-dialog modal-dialog-centered modal-xl modal-image" role="document">\n' +
        '        <div class="modal-content">\n' +
        '            <div class="modal-header justify-content-end">\n' +
        '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
        '                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
        '                </button>\n' +
        '            </div>\n' +
        '            <div class="modal-body p-0"><img src="' + imgUrl + '"></div>\n' +
        '        </div>\n' +
        '    </div>\n' +
        '</div>';
    $('body').append(modal);
    $('#ModalImage').on('hidden.bs.modal', function() {
        $('#ModalImage').remove();
    });
    $('#ModalImage').modal();
}

function displayVideoModal(videoUrl) {
    $('#ModalVideo').remove();
    let modal = '\n' +
        '<div class="modal fade" id="ModalVideo" tabindex="-1" role="dialog" aria-labelledby="ModalVideo" aria-hidden="true">\n' +
        '    <div class="modal-dialog modal-dialog-centered modal-xl modal-video" role="document">\n' +
        '        <div class="modal-content">\n' +
        '            <div class="modal-header justify-content-end">\n' +
        '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
        '                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
        '                </button>\n' +
        '            </div>\n' +
        '            <div class="modal-body p-0">' +
        '               <div class="embed-responsive embed-responsive-16by9">\n' +
        '                   <iframe class="embed-responsive-item rounded border" src="' + videoUrl + '" width="854" height="480" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>\n' +
        '               </div>\n' +
        '            </div>\n' +
        '        </div>\n' +
        '    </div>\n' +
        '</div>';
    $('body').append(modal);
    $('#ModalVideo').on('hidden.bs.modal', function() {
        $('#ModalVideo').remove();
    });
    $('#ModalVideo').modal();
}

function setDocumentClick() {
    $('#storyModal').on('click', function(e) {
        let target = $(e.target);
        if (!target.is('button')) {
            if (storyWorking) {
                clearTimeout(stepTimeout);
                displayStep(true);
            }
        }
    });
}

function closeModal() {
    $('#storyModal').modal('hide');
}

function initScroll() {
    $('.story-messages').scroll(function () {
        let st = $(this).scrollTop();
        let scrollHeight = $('.story-messages')[0].scrollHeight;
        let height = $('.story-messages').outerHeight();
        if (st + height >= scrollHeight) {
            $('.story-messages').removeClass('scrolled');
        } else if (st + height < scrollHeight) {
            $('.story-messages').addClass('scrolled');
        }
    });
}
function scrollMessages() {
    if ($('.story-messages').hasClass('scrolled')) {
        return;
    }

    $(".story-messages").animate({scrollTop: $(".story-messages")[0].scrollHeight}, 500);
    $('.story-messages').removeClass('scrolled');
}

/*function writeText(content) {
    storyWorking = true;

    let lines = content.split("<br>");

    let iSpeed = 30; // time delay of print out
    let rowIndex = 0; // start printing array at this posision
    let lineLength = lines[rowIndex].length; // the length of the text array
    let iTextPos = 0;
    let sContents = '';

    let destination = document.getElementById("story-content");
    typeWriterInterval = setInterval(function() {
        let iRow = 0;
        sContents = ' ';
        while ( iRow < rowIndex ) {
            sContents += lines[iRow++] + '<br />';
        }
        destination.innerHTML = sContents + lines[rowIndex].substring(0, iTextPos) + "_";
        if (iTextPos++ == lineLength) {
            iTextPos = 0;
            rowIndex++;
            if ( rowIndex != lines.length ) {
                lineLength = lines[rowIndex].length;
            } else {
                destination.innerHTML = destination.innerHTML.slice(0, -1);
                clearInterval(typeWriterInterval);
                storyWorking = false;
            }
        }
    }, iSpeed);
}*/
