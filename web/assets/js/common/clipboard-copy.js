$(document).ready(function() {
	$('.btn-copy').each(function() {
		let id = $(this).attr('id');
		let clipboard = new Clipboard('#' + id);
		clipboard.on('success', function(e) {
			let btnText = $('#' + id).html();
			$('#' + id).html(__('Copié !'));
			setTimeout(function() {
				$('#' + id).html(btnText);
			}, 5000);
			if ($('#' + id).closest('.code-link').find('code').length) {
				let code = $('#' + id).closest('.code-link').find('code');
				code.addClass('bg-success text-inverse-success');
				code.find('a').addClass('text-inverse-success');
				setTimeout(function() {
					code.removeClass('bg-success text-inverse-success');
					code.find('a').removeClass('text-inverse-success');
				}, 5000);
			}
		});
	});
});
