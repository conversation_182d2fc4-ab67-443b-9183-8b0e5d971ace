$(document).ready(function() {
    HandleAddressesInputs();
});

function HandleAddressesInputs() {
    $('.input-group-address').each(function() {
        let target = $(this).find('input').attr('id');
        let zipTarget = $(this).find('input').data('target-zip');
        let cityTarget = $(this).find('input').data('target-city');
        /*let addressValue = $('#' + target).val();
        let zipValue = $('#' + zipTarget).val();
        let cityValue = $('#' + cityTarget).val();*/

        /*$(document).on('click', '#' + target, function () {
            addressValue = $('#' + target).val();
            zipValue = $('#' + zipTarget).val();
            cityValue = $('#' + cityTarget).val();
        });*/

        $(document).on('focus', '#' + target, function () {
            if ($('#' + target + '_results').text().length) {
                showAddressesResults(target);
            }
        });

        $(document).on('input', '#' + target, function () {
            let address = $(this).val();
            if (address.length <= 2) {
                hideAddressesResults(target);
                return;
            }

            $.ajax({
                type: 'GET',
                url: 'https://api-adresse.data.gouv.fr/search/',
                data: { q: address, limit: 20 },
                success: function (data) {
                    let results = '';
                    let features = data.features;

                    if (!features.length) {
                        results += '<p class="text-center mb-0">' + __('Aucun résultat') + '</p>';
                        showAddressesResults(target, results);
                        return;
                    }

                    $.each(features, function(index, feature) {
                        let addressName = feature.properties.name + ', ' + feature.properties.postcode + ' ' + feature.properties.city;
                        results += '<li data-address="' + feature.properties.name + '" data-zip="' + feature.properties.postcode + '" data-city="' + feature.properties.city + '">' + addressName + '</li>';
                    });

                    showAddressesResults(target, results);
                }
            });
        });

        $(document).on('click', '#' + target + '_results li', function () {
            /*addressValue = $(this).data('address');
            zipValue = $(this).data('zip');
            cityValue = $(this).data('city');*/
            $('#' + target).val($(this).data('address'));
            $('#' + zipTarget).val($(this).data('zip'));
            $('#' + cityTarget).val($(this).data('city'));
            hideAddressesResults(target);

            //update inputs in wizard
            if ($('#' + target).hasClass('is-invalid')) {
                $('#' + target).removeClass('is-invalid');
                $('#' + target).closest('.form-group').find('.fv-plugins-message-container').html('');
            }
            if ($('#' + zipTarget).hasClass('is-invalid')) {
                $('#' + zipTarget).removeClass('is-invalid');
                $('#' + zipTarget).closest('.form-group').find('.fv-plugins-message-container').html('');
            }
            if ($('#' + cityTarget).hasClass('is-invalid')) {
                $('#' + cityTarget).removeClass('is-invalid');
                $('#' + cityTarget).closest('.form-group').find('.fv-plugins-message-container').html('');
            }
        });

        $(document).on('click', 'body', function (e) {
            if (!$(e.target).is('#' + target) && !$(e.target).is('#' + target + '_results') && !$(e.target).is('#' + target + '_results li')) {
                hideAddressesResults(target);
                /*$('#' + target).val(addressValue);
                $('#' + zipTarget).val(zipValue);
                $('#' + cityTarget).val(cityValue);*/
            }
        });
    });
}

function showAddressesResults(target, results) {
    if (results) {
        $('#' + target + '_results').html(results);
    }
    $('#' + target + '_results').show().scrollTop(0);
}

function hideAddressesResults(target) {
    $('#' + target + '_results').hide();
}
