/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,n,i){function o(a,s){if(!n[a]){if(!t[a]){var l="function"==typeof require&&require;if(!s&&l)return l(a,!0);if(r)return r(a,!0);var d=new Error("Cannot find module '"+a+"'");throw d.code="MODULE_NOT_FOUND",d}var u=n[a]={exports:{}};t[a][0].call(u.exports,function(e){var n=t[a][1][e];return o(n||e)},u,u.exports,e,t,n,i)}return n[a].exports}for(var r="function"==typeof require&&require,a=0;a<i.length;a++)o(i[a]);return o}({1:[function(e,t,n){},{}],2:[function(e,t,n){(function(n){var i,o=void 0!==n?n:"undefined"!=typeof window?window:{},r=e(1);"undefined"!=typeof document?i=document:(i=o["__GLOBAL_DOCUMENT_CACHE@4"])||(i=o["__GLOBAL_DOCUMENT_CACHE@4"]=r),t.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],3:[function(e,t,n){(function(e){var n;n="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(e,t,n){!function(e){function n(){}function i(e,t){return function(){e.apply(t,arguments)}}function o(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],u(e,this)}function r(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,o._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var i;try{i=n(e._value)}catch(e){return void s(t.promise,e)}a(t.promise,i)}else(1===e._state?a:s)(t.promise,e._value)})):e._deferreds.push(t)}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof o)return e._state=3,e._value=t,void l(e);if("function"==typeof n)return void u(i(n,t),e)}e._state=1,e._value=t,l(e)}catch(t){s(e,t)}}function s(e,t){e._state=2,e._value=t,l(e)}function l(e){2===e._state&&0===e._deferreds.length&&o._immediateFn(function(){e._handled||o._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)r(e,e._deferreds[t]);e._deferreds=null}function d(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function u(e,t){var n=!1;try{e(function(e){n||(n=!0,a(t,e))},function(e){n||(n=!0,s(t,e))})}catch(e){if(n)return;n=!0,s(t,e)}}var c=setTimeout;o.prototype.catch=function(e){return this.then(null,e)},o.prototype.then=function(e,t){var i=new this.constructor(n);return r(this,new d(e,t,i)),i},o.all=function(e){var t=Array.prototype.slice.call(e);return new o(function(e,n){function i(r,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,function(e){i(r,e)},n)}t[r]=a,0==--o&&e(t)}catch(e){n(e)}}if(0===t.length)return e([]);for(var o=t.length,r=0;r<t.length;r++)i(r,t[r])})},o.resolve=function(e){return e&&"object"==typeof e&&e.constructor===o?e:new o(function(t){t(e)})},o.reject=function(e){return new o(function(t,n){n(e)})},o.race=function(e){return new o(function(t,n){for(var i=0,o=e.length;i<o;i++)e[i].then(t,n)})},o._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){c(e,0)},o._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},o._setImmediateFn=function(e){o._immediateFn=e},o._setUnhandledRejectionFn=function(e){o._unhandledRejectionFn=e},void 0!==t&&t.exports?t.exports=o:e.Promise||(e.Promise=o)}(this)},{}],5:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),r=e(15),a=e(26),s={lang:"en",en:r.EN};s.language=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(null!==t&&void 0!==t&&t.length){if("string"!=typeof t[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2}(\-[a-z]{2})?$/i.test(t[0]))throw new TypeError("Language code must have format `xx` or `xx-xx`");s.lang=t[0],void 0===s[t[0]]?(t[1]=null!==t[1]&&void 0!==t[1]&&"object"===i(t[1])?t[1]:{},s[t[0]]=(0,a.isObjectEmpty)(t[1])?r.EN:t[1]):null!==t[1]&&void 0!==t[1]&&"object"===i(t[1])&&(s[t[0]]=t[1])}return s.lang},s.t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e&&e.length){var n=void 0,o=void 0,r=s.language(),l=function(e,t,n){return"object"!==(void 0===e?"undefined":i(e))||"number"!=typeof t||"number"!=typeof n?e:function(){return[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>0&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])>=2&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100==1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100==2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100==3||(arguments.length<=0?void 0:arguments[0])%100==4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])>6&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100>=3&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:(arguments.length<=0?void 0:arguments[0])%100>=11?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>1&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100>10&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10==2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}]}()[n].apply(null,[t].concat(e))};return void 0!==s[r]&&(n=s[r][e],null!==t&&"number"==typeof t&&(o=s[r]["mejs.plural-form"],n=l.apply(null,[n,t,o]))),!n&&s.en&&(n=s.en[e],null!==t&&"number"==typeof t&&(o=s.en["mejs.plural-form"],n=l.apply(null,[n,t,o]))),n=n||e,null!==t&&"number"==typeof t&&(n=n.replace("%1",t)),(0,a.escapeHTML)(n)}return e},o.default.i18n=s,"undefined"!=typeof mejsL10n&&o.default.i18n.language(mejsL10n.language,mejsL10n.strings),n.default=s},{15:15,26:26,7:7}],6:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=i(e(3)),s=i(e(2)),l=i(e(7)),d=e(26),u=e(27),c=e(8),f=e(24),p=function e(t,n,i){var p=this;o(this,e);var m=this;i=Array.isArray(i)?i:null,m.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain",customError:""},n=Object.assign(m.defaults,n),m.mediaElement=s.default.createElement(n.fakeNodeName);var h=t,v=!1;if("string"==typeof t?m.mediaElement.originalNode=s.default.getElementById(t):(m.mediaElement.originalNode=t,h=t.id),void 0===m.mediaElement.originalNode||null===m.mediaElement.originalNode)return null;m.mediaElement.options=n,h=h||"mejs_"+Math.random().toString().slice(2),m.mediaElement.originalNode.setAttribute("id",h+"_from_mejs");var y=m.mediaElement.originalNode.tagName.toLowerCase();["video","audio"].indexOf(y)>-1&&!m.mediaElement.originalNode.getAttribute("preload")&&m.mediaElement.originalNode.setAttribute("preload","none"),m.mediaElement.originalNode.parentNode.insertBefore(m.mediaElement,m.mediaElement.originalNode),m.mediaElement.appendChild(m.mediaElement.originalNode);var g=function(e,t){if(l.default.html5media.mediaTypes.indexOf(t)>-1&&"https:"===a.default.location.protocol&&f.IS_IOS){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var t=(a.default.URL||a.default.webkitURL).createObjectURL(this.response);return m.mediaElement.originalNode.setAttribute("src",t),t}return e},n.open("GET",e),n.responseType="blob",n.send()}return e},b=void 0;if(null!==i)b=i;else if(null!==m.mediaElement.originalNode)switch(b=[],m.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":b.push({type:"",src:m.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var E=m.mediaElement.originalNode.children.length,S=m.mediaElement.originalNode.getAttribute("src");if(S){var w=m.mediaElement.originalNode,x=(0,u.formatType)(S,w.getAttribute("type"));b.push({type:x,src:g(S,x)})}for(var P=0;P<E;P++){var T=m.mediaElement.originalNode.children[P];if("source"===T.tagName.toLowerCase()){var C=T.getAttribute("src"),_=(0,u.formatType)(C,T.getAttribute("type"));b.push({type:_,src:g(C,_)})}}}m.mediaElement.id=h,m.mediaElement.renderers={},m.mediaElement.events={},m.mediaElement.promises=[],m.mediaElement.renderer=null,m.mediaElement.rendererName=null,m.mediaElement.changeRenderer=function(e,t){var n=p,i=Object.keys(t[0]).length>2?t[0]:t[0].src;if(void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&n.mediaElement.renderer.name===e)return n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.show(),n.mediaElement.renderer.setSrc(i),!0;void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&(n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.hide());var o=n.mediaElement.renderers[e],r=null;if(void 0!==o&&null!==o)return o.show(),o.setSrc(i),n.mediaElement.renderer=o,n.mediaElement.rendererName=e,!0;for(var a=n.mediaElement.options.renderers.length?n.mediaElement.options.renderers:c.renderer.order,s=0,l=a.length;s<l;s++){var d=a[s];if(d===e){r=c.renderer.renderers[d];var u=Object.assign(r.options,n.mediaElement.options);return o=r.create(n.mediaElement,u,t),o.name=e,n.mediaElement.renderers[r.name]=o,n.mediaElement.renderer=o,n.mediaElement.rendererName=e,o.show(),!0}}return!1},m.mediaElement.setSize=function(e,t){void 0!==m.mediaElement.renderer&&null!==m.mediaElement.renderer&&m.mediaElement.renderer.setSize(e,t)},m.mediaElement.createErrorMessage=function(e,t){e=e||"",t=Array.isArray(t)?t:[];var n=s.default.createElement("div");n.className="me_cannotplay",n.style.width="100%",n.style.height="100%";var i=m.mediaElement.options.customError;if(!i){var o=m.mediaElement.originalNode.getAttribute("poster");o&&(i+='<img src="'+o+'" width="100%" height="100%" alt="'+l.default.i18n.t("mejs.download-file")+'">'),e&&(i+="<p>"+e+"</p>");for(var r=0,a=t.length;r<a;r++){var d=t[r];i+='<a href="'+d.src+'" data-type="'+d.type+'"><span>'+l.default.i18n.t("mejs.download-file")+": "+d.src+"</span></a>"}}n.innerHTML=i,console.error(e),m.mediaElement.originalNode.parentNode.insertBefore(n,m.mediaElement.originalNode),m.mediaElement.originalNode.style.display="none",v=!0};var k=l.default.html5media.properties,N=l.default.html5media.methods,A=function(e,t,n,i){var o=e[t],r=function(){return n.apply(e,[o])},a=function(t){return o=i.apply(e,[t])};Object.defineProperty(e,t,{get:r,set:a})},L=function(){return void 0!==m.mediaElement.renderer&&null!==m.mediaElement.renderer?m.mediaElement.renderer.getSrc():null},F=function(e){var t=[];if("string"==typeof e)t.push({src:e,type:e?(0,u.getTypeFromFile)(e):""});else if("object"===(void 0===e?"undefined":r(e))&&void 0!==e.src){var n=(0,u.absolutizeUrl)(e.src),i=e.type,o=Object.assign(e,{src:n,type:""!==i&&null!==i&&void 0!==i||!n?i:(0,u.getTypeFromFile)(n)});t.push(o)}else if(Array.isArray(e))for(var a=0,s=e.length;a<s;a++){var l=(0,u.absolutizeUrl)(e[a].src),f=e[a].type,p=Object.assign(e[a],{src:l,type:""!==f&&null!==f&&void 0!==f||!l?f:(0,u.getTypeFromFile)(l)});t.push(p)}var h=c.renderer.select(t,m.mediaElement.options.renderers.length?m.mediaElement.options.renderers:[]),v=void 0;return m.mediaElement.paused||(m.mediaElement.pause(),v=(0,d.createEvent)("pause",m.mediaElement),m.mediaElement.dispatchEvent(v)),m.mediaElement.originalNode.setAttribute("src",t[0].src||""),m.mediaElement.querySelector(".me_cannotplay")&&m.mediaElement.querySelector(".me_cannotplay").remove(),null===h&&t[0].src?(v=(0,d.createEvent)("error",m.mediaElement),v.message="No renderer found",m.mediaElement.createErrorMessage(v.message,t),void m.mediaElement.dispatchEvent(v)):t[0].src?m.mediaElement.changeRenderer(h.rendererName,t):null};A(m.mediaElement,"src",L,F),m.mediaElement.getSrc=L,m.mediaElement.setSrc=F;for(var j=0,I=k.length;j<I;j++)!function(e){if("src"!==e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1),n=function(){return void 0!==m.mediaElement.renderer&&null!==m.mediaElement.renderer&&"function"==typeof m.mediaElement.renderer["get"+t]?m.mediaElement.renderer["get"+t]():null},i=function(e){void 0!==m.mediaElement.renderer&&null!==m.mediaElement.renderer&&"function"==typeof m.mediaElement.renderer["set"+t]&&m.mediaElement.renderer["set"+t](e)};A(m.mediaElement,e,n,i),m.mediaElement["get"+t]=n,m.mediaElement["set"+t]=i}}(k[j]);for(var M=0,O=N.length;M<O;M++)!function(e){m.mediaElement[e]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];if(void 0!==m.mediaElement.renderer&&null!==m.mediaElement.renderer&&"function"==typeof m.mediaElement.renderer[e])if("play"===e)if(m.mediaElement.promises.length)Promise.all(m.mediaElement.promises).then(function(){setTimeout(function(){m.mediaElement.renderer[e](n)},250)}).catch(function(e){if(void 0===m.mediaElement.renderer||null===m.mediaElement.renderer){var t=(0,d.createEvent)("error",m.mediaElement);t.message=e,m.mediaElement.dispatchEvent(t),m.mediaElement.createErrorMessage(e,b)}});else try{m.mediaElement.renderer[e](n)}catch(e){m.mediaElement.createErrorMessage()}else try{m.mediaElement.renderer[e](n)}catch(e){m.mediaElement.createErrorMessage()}return null}}(N[M]);return m.mediaElement.addEventListener=function(e,t){m.mediaElement.events[e]=m.mediaElement.events[e]||[],m.mediaElement.events[e].push(t)},m.mediaElement.removeEventListener=function(e,t){if(!e)return m.mediaElement.events={},!0;var n=m.mediaElement.events[e];if(!n)return!0;if(!t)return m.mediaElement.events[e]=[],!0;for(var i=0;i<n.length;i++)if(n[i]===t)return m.mediaElement.events[e].splice(i,1),!0;return!1},m.mediaElement.dispatchEvent=function(e){var t=m.mediaElement.events[e.type];if(t)for(var n=0;n<t.length;n++)t[n].apply(null,[e])},b.length&&(m.mediaElement.src=b),m.mediaElement.promises.length?Promise.all(m.mediaElement.promises).then(function(){m.mediaElement.options.success&&m.mediaElement.options.success(m.mediaElement,m.mediaElement.originalNode)}).catch(function(){v&&m.mediaElement.options.error&&m.mediaElement.options.error(m.mediaElement,m.mediaElement.originalNode)}):(m.mediaElement.options.success&&m.mediaElement.options.success(m.mediaElement,m.mediaElement.originalNode),v&&m.mediaElement.options.error&&m.mediaElement.options.error(m.mediaElement,m.mediaElement.originalNode)),m.mediaElement};a.default.MediaElement=p,n.default=p},{2:2,24:24,26:26,27:27,3:3,7:7,8:8}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=function(e){return e&&e.__esModule?e:{default:e}}(e(3)),o={};o.version="4.1.3",o.html5media={properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]},i.default.mejs=o,n.default=o},{3:3}],8:[function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0}),n.renderer=void 0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),s=function(){function e(){i(this,e),this.renderers={},this.order=[]}return r(e,[{key:"add",value:function(e){if(void 0===e.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[e.name]=e,this.order.push(e.name)}},{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=t.length;if(t=t.length?t:this.order,!n){var i=[/^(html5|native)/i,/^flash/i,/iframe$/i],o=function(e){for(var t=0,n=i.length;t<n;t++)if(i[t].test(e))return t;return i.length};t.sort(function(e,t){return o(e)-o(t)})}for(var r=0,a=t.length;r<a;r++){var s=t[r],l=this.renderers[s];if(null!==l&&void 0!==l)for(var d=0,u=e.length;d<u;d++)if("function"==typeof l.canPlayType&&"string"==typeof e[d].type&&l.canPlayType(e[d].type))return{rendererName:l.name,src:e[d].src}}return null}},{key:"order",set:function(e){if(!Array.isArray(e))throw new TypeError("order must be an array of strings.");this._order=e},get:function(){return this._order}},{key:"renderers",set:function(e){if(null!==e&&"object"!==(void 0===e?"undefined":o(e)))throw new TypeError("renderers must be an array of objects.");this._renderers=e},get:function(){return this._renderers}}]),e}(),l=n.renderer=new s;a.default.Renderers=l},{7:7}],9:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(2)),a=i(e(5)),s=e(17),l=i(s),d=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(e(24)),u=e(26),c=e(25),f=e(27);Object.assign(s.config,{usePluginFullScreen:!0,fullscreenText:null}),Object.assign(l.default.prototype,{isFullScreen:!1,isNativeFullScreen:!1,isInIframe:!1,isPluginClickThroughCreated:!1,fullscreenMode:"",containerSizeTimeout:null,buildfullscreen:function(e){if(e.isVideo){e.isInIframe=o.default.location!==o.default.parent.location,e.detectFullscreenMode();var t=this,n=(0,u.isString)(t.options.fullscreenText)?t.options.fullscreenText:a.default.t("mejs.fullscreen"),i=r.default.createElement("div");if(i.className=t.options.classPrefix+"button "+t.options.classPrefix+"fullscreen-button",i.innerHTML='<button type="button" aria-controls="'+t.id+'" title="'+n+'" aria-label="'+n+'" tabindex="0"></button>',t.addControlElement(i,"fullscreen"),i.addEventListener("click",function(){d.HAS_TRUE_NATIVE_FULLSCREEN&&d.IS_FULLSCREEN||e.isFullScreen?e.exitFullScreen():e.enterFullScreen()}),e.fullscreenBtn=i,t.globalBind("keydown",function(n){27===(n.which||n.keyCode||0)&&(d.HAS_TRUE_NATIVE_FULLSCREEN&&d.IS_FULLSCREEN||t.isFullScreen)&&e.exitFullScreen()}),t.normalHeight=0,t.normalWidth=0,d.HAS_TRUE_NATIVE_FULLSCREEN){var s=function(){e.isFullScreen&&(d.isFullScreen()?(e.isNativeFullScreen=!0,e.setControlsSize()):(e.isNativeFullScreen=!1,e.exitFullScreen()))};e.globalBind(d.FULLSCREEN_EVENT_NAME,s)}}},detectFullscreenMode:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName),n="";return d.HAS_TRUE_NATIVE_FULLSCREEN&&t?n="native-native":d.HAS_TRUE_NATIVE_FULLSCREEN&&!t?n="plugin-native":e.usePluginFullScreen&&d.SUPPORT_POINTER_EVENTS&&(n="plugin-click"),e.fullscreenMode=n,n},cleanfullscreen:function(e){e.exitFullScreen()},enterFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(html5|native)/i.test(e.media.rendererName),n=getComputedStyle(e.container);if(d.IS_IOS&&d.HAS_IOS_FULLSCREEN&&"function"==typeof e.media.originalNode.webkitEnterFullscreen&&e.media.originalNode.canPlayType((0,f.getTypeFromFile)(e.media.getSrc())))e.media.originalNode.webkitEnterFullscreen();else{if((0,c.addClass)(r.default.documentElement,e.options.classPrefix+"fullscreen"),(0,c.addClass)(e.container,e.options.classPrefix+"container-fullscreen"),e.normalHeight=parseFloat(n.height),e.normalWidth=parseFloat(n.width),"native-native"!==e.fullscreenMode&&"plugin-native"!==e.fullscreenMode||(d.requestFullScreen(e.container),e.isInIframe&&setTimeout(function t(){if(e.isNativeFullScreen){var n=o.default.innerWidth||r.default.documentElement.clientWidth||r.default.body.clientWidth,i=screen.width;Math.abs(i-n)>.002*i?e.exitFullScreen():setTimeout(t,500)}},1e3)),e.container.style.width="100%",e.container.style.height="100%",e.containerSizeTimeout=setTimeout(function(){e.container.style.width="100%",e.container.style.height="100%",e.setControlsSize()},500),t)e.node.style.width="100%",e.node.style.height="100%";else for(var i=e.container.querySelectorAll("iframe, embed, object, video"),a=i.length,s=0;s<a;s++)i[s].style.width="100%",i[s].style.height="100%";e.options.setDimensions&&"function"==typeof e.media.setSize&&e.media.setSize(screen.width,screen.height);for(var l=e.layers.children,p=l.length,m=0;m<p;m++)l[m].style.width="100%",l[m].style.height="100%";e.fullscreenBtn&&((0,c.removeClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen"),(0,c.addClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen")),e.setControlsSize(),e.isFullScreen=!0;var h=Math.min(screen.width/e.width,screen.height/e.height),v=e.container.querySelector("."+e.options.classPrefix+"captions-text");v&&(v.style.fontSize=100*h+"%",v.style.lineHeight="normal",e.container.querySelector("."+e.options.classPrefix+"captions-position").style.bottom="45px");var y=(0,u.createEvent)("enteredfullscreen",e.container);e.container.dispatchEvent(y)}},exitFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName);if(clearTimeout(e.containerSizeTimeout),d.HAS_TRUE_NATIVE_FULLSCREEN&&(d.IS_FULLSCREEN||e.isFullScreen)&&d.cancelFullScreen(),(0,c.removeClass)(r.default.documentElement,e.options.classPrefix+"fullscreen"),(0,c.removeClass)(e.container,e.options.classPrefix+"container-fullscreen"),e.options.setDimensions){if(e.container.style.width=e.normalWidth+"px",e.container.style.height=e.normalHeight+"px",t)e.node.style.width=e.normalWidth+"px",e.node.style.height=e.normalHeight+"px";else for(var n=e.container.querySelectorAll("iframe, embed, object, video"),i=n.length,o=0;o<i;o++)n[o].style.width=e.normalWidth+"px",n[o].style.height=e.normalHeight+"px";"function"==typeof e.media.setSize&&e.media.setSize(e.normalWidth,e.normalHeight);for(var a=e.layers.children,s=a.length,l=0;l<s;l++)a[l].style.width=e.normalWidth+"px",a[l].style.height=e.normalHeight+"px"}e.fullscreenBtn&&((0,c.removeClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen"),(0,c.addClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen")),e.setControlsSize(),e.isFullScreen=!1;var f=e.container.querySelector("."+e.options.classPrefix+"captions-text");f&&(f.style.fontSize="",f.style.lineHeight="",e.container.querySelector("."+e.options.classPrefix+"captions-position").style.bottom="");var p=(0,u.createEvent)("exitedfullscreen",e.container);e.container.dispatchEvent(p)}})},{17:17,2:2,24:24,25:25,26:26,27:27,3:3,5:5}],10:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=e(17),a=i(r),s=i(e(5)),l=e(26),d=e(25);Object.assign(r.config,{playText:null,pauseText:null}),Object.assign(a.default.prototype,{buildplaypause:function(e,t,n,i){function r(e){"play"===e?((0,d.removeClass)(p,a.options.classPrefix+"play"),(0,d.removeClass)(p,a.options.classPrefix+"replay"),(0,d.addClass)(p,a.options.classPrefix+"pause"),m.setAttribute("title",f),m.setAttribute("aria-label",f)):((0,d.removeClass)(p,a.options.classPrefix+"pause"),(0,d.removeClass)(p,a.options.classPrefix+"replay"),(0,d.addClass)(p,a.options.classPrefix+"play"),m.setAttribute("title",c),m.setAttribute("aria-label",c))}var a=this,u=a.options,c=(0,l.isString)(u.playText)?u.playText:s.default.t("mejs.play"),f=(0,l.isString)(u.pauseText)?u.pauseText:s.default.t("mejs.pause"),p=o.default.createElement("div");p.className=a.options.classPrefix+"button "+a.options.classPrefix+"playpause-button "+a.options.classPrefix+"play",p.innerHTML='<button type="button" aria-controls="'+a.id+'" title="'+c+'" aria-label="'+f+'" tabindex="0"></button>',p.addEventListener("click",function(){i.paused?i.play():i.pause()});var m=p.querySelector("button");a.addControlElement(p,"playpause"),r("pse"),i.addEventListener("loadedmetadata",function(){-1===i.rendererName.indexOf("flash")&&r("pse")}),i.addEventListener("play",function(){r("play")}),i.addEventListener("playing",function(){r("play")}),i.addEventListener("pause",function(){r("pse")}),i.addEventListener("ended",function(){e.options.loop||((0,d.removeClass)(p,a.options.classPrefix+"pause"),(0,d.removeClass)(p,a.options.classPrefix+"play"),(0,d.addClass)(p,a.options.classPrefix+"replay"),m.setAttribute("title",c),m.setAttribute("aria-label",c))})}})},{17:17,2:2,25:25,26:26,5:5}],11:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=e(17),a=i(r),s=i(e(5)),l=e(24),d=e(29),u=e(25);Object.assign(r.config,{enableProgressTooltip:!0,useSmoothHover:!0}),Object.assign(a.default.prototype,{buildprogress:function(e,t,n,i){var r=0,a=!1,c=!1,f=this,p=e.options.autoRewind,m=e.options.enableProgressTooltip?'<span class="'+f.options.classPrefix+'time-float"><span class="'+f.options.classPrefix+'time-float-current">00:00</span><span class="'+f.options.classPrefix+'time-float-corner"></span></span>':"",h=o.default.createElement("div");h.className=f.options.classPrefix+"time-rail",h.innerHTML='<span class="'+f.options.classPrefix+"time-total "+f.options.classPrefix+'time-slider"><span class="'+f.options.classPrefix+'time-buffering"></span><span class="'+f.options.classPrefix+'time-loaded"></span><span class="'+f.options.classPrefix+'time-current"></span><span class="'+f.options.classPrefix+'time-hovered no-hover"></span><span class="'+f.options.classPrefix+'time-handle"><span class="'+f.options.classPrefix+'time-handle-content"></span></span>'+m+"</span>",f.addControlElement(h,"progress"),t.querySelector("."+f.options.classPrefix+"time-buffering").style.display="none",f.rail=t.querySelector("."+f.options.classPrefix+"time-rail"),f.total=t.querySelector("."+f.options.classPrefix+"time-total"),f.loaded=t.querySelector("."+f.options.classPrefix+"time-loaded"),f.current=t.querySelector("."+f.options.classPrefix+"time-current"),f.handle=t.querySelector("."+f.options.classPrefix+"time-handle"),f.timefloat=t.querySelector("."+f.options.classPrefix+"time-float"),f.timefloatcurrent=t.querySelector("."+f.options.classPrefix+"time-float-current"),f.slider=t.querySelector("."+f.options.classPrefix+"time-slider"),f.hovered=t.querySelector("."+f.options.classPrefix+"time-hovered"),f.newTime=0,f.forcedHandlePause=!1,f.setTransformStyle=function(e,t){e.style.transform=t,e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t};var v=function(t){var n=getComputedStyle(f.total),i=(0,u.offset)(f.total),o=parseFloat(n.width),r=function(){return void 0!==n.webkitTransform?"webkitTransform":void 0!==n.mozTransform?"mozTransform ":void 0!==n.oTransform?"oTransform":void 0!==n.msTransform?"msTransform":"transform"}(),s=function(){return"WebKitCSSMatrix"in window?"WebKitCSSMatrix":"MSCSSMatrix"in window?"MSCSSMatrix":"CSSMatrix"in window?"CSSMatrix":void 0}(),c=0,p=0,m=void 0;if(m=t.originalEvent&&t.originalEvent.changedTouches?t.originalEvent.changedTouches[0].pageX:t.changedTouches?t.changedTouches[0].pageX:t.pageX,f.getDuration()&&(m<i.left?m=i.left:m>o+i.left&&(m=o+i.left),p=m-i.left,c=p/o,f.newTime=c<=.02?0:c*f.getDuration(),a&&null!==f.getCurrentTime()&&f.newTime.toFixed(4)!==f.getCurrentTime().toFixed(4)&&(f.setCurrentRailHandle(f.newTime),f.updateCurrent(f.newTime)),!l.IS_IOS&&!l.IS_ANDROID&&f.timefloat)){if(p<0&&(p=0),f.options.useSmoothHover&&null!==s&&void 0!==window[s]){var h=new window[s](getComputedStyle(f.handle)[r]).m41,v=p/parseFloat(getComputedStyle(f.total).width)-h/parseFloat(getComputedStyle(f.total).width);f.hovered.style.left=h+"px",f.setTransformStyle(f.hovered,"scaleX("+v+")"),f.hovered.setAttribute("pos",p),v>=0?(0,u.removeClass)(f.hovered,"negative"):(0,u.addClass)(f.hovered,"negative")}f.timefloat.style.left=p+"px",f.timefloatcurrent.innerHTML=(0,d.secondsToTimeCode)(f.newTime,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength),f.timefloat.style.display="block"}},y=function(){var t=f.getCurrentTime(),n=s.default.t("mejs.time-slider"),o=(0,d.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength),r=f.getDuration();f.slider.setAttribute("role","slider"),f.slider.tabIndex=0,i.paused?(f.slider.setAttribute("aria-label",n),f.slider.setAttribute("aria-valuemin",0),f.slider.setAttribute("aria-valuemax",r),f.slider.setAttribute("aria-valuenow",t),f.slider.setAttribute("aria-valuetext",o)):(f.slider.removeAttribute("aria-label"),f.slider.removeAttribute("aria-valuemin"),f.slider.removeAttribute("aria-valuemax"),f.slider.removeAttribute("aria-valuenow"),f.slider.removeAttribute("aria-valuetext"))},g=function(){new Date-r>=1e3&&i.play()},b=function(){a&&null!==f.getCurrentTime()&&f.newTime.toFixed(4)!==f.getCurrentTime().toFixed(4)&&(f.setCurrentTime(f.newTime),e.setCurrentRail(),f.updateCurrent(f.newTime)),f.forcedHandlePause&&f.media.play(),f.forcedHandlePause=!1};f.slider.addEventListener("focus",function(){e.options.autoRewind=!1}),f.slider.addEventListener("blur",function(){e.options.autoRewind=p}),f.slider.addEventListener("keydown",function(t){if(new Date-r>=1e3&&(c=i.paused),f.options.keyActions.length){var n=t.which||t.keyCode||0,o=f.getDuration(),a=e.options.defaultSeekForwardInterval(i),s=e.options.defaultSeekBackwardInterval(i),d=f.getCurrentTime();switch(n){case 37:case 40:f.getDuration()!==1/0&&(d-=s);break;case 39:case 38:f.getDuration()!==1/0&&(d+=a);break;case 36:d=0;break;case 35:d=o;break;case 32:return void(l.IS_FIREFOX||(i.paused?i.play():i.pause()));case 13:return void(i.paused?i.play():i.pause());default:return}d=d<0?0:d>=o?o:Math.floor(d),r=new Date,c||i.pause(),d<f.getDuration()&&!c&&setTimeout(g,1100),f.setCurrentTime(d),t.preventDefault(),t.stopPropagation()}});var E=["mousedown","touchstart"];f.slider.addEventListener("dragstart",function(){return!1});for(var S=0,w=E.length;S<w;S++)f.slider.addEventListener(E[S],function(e){if(f.forcedHandlePause=!1,f.getDuration()!==1/0&&(1===e.which||0===e.which)){i.paused||(f.media.pause(),f.forcedHandlePause=!0),a=!0,v(e);for(var t=["mouseup","touchend"],n=0,o=t.length;n<o;n++)f.container.addEventListener(t[n],function(e){var t=e.target;(t===f.slider||t.closest("."+f.options.classPrefix+"time-slider"))&&v(e)});f.globalBind("mouseup.dur touchend.dur",function(){b(),a=!1,f.timefloat&&(f.timefloat.style.display="none"),f.globalUnbind("mousemove.dur touchmove.dur mouseup.dur touchend.dur")})}});f.slider.addEventListener("mouseenter",function(e){e.target===f.slider&&f.getDuration()!==1/0&&(f.container.addEventListener("mousemove",function(e){var t=e.target;(t===f.slider||t.closest("."+f.options.classPrefix+"time-slider"))&&v(e)}),!f.timefloat||l.IS_IOS||l.IS_ANDROID||(f.timefloat.style.display="block"),f.hovered&&!l.IS_IOS&&!l.IS_ANDROID&&f.options.useSmoothHover&&(0,u.removeClass)(f.hovered,"no-hover"))}),f.slider.addEventListener("mouseleave",function(){f.getDuration()!==1/0&&(a||(f.globalUnbind("mousemove.dur"),f.timefloat&&(f.timefloat.style.display="none"),f.hovered&&f.options.useSmoothHover&&(0,u.addClass)(f.hovered,"no-hover")))}),i.addEventListener("progress",function(n){var i=t.querySelector("."+f.options.classPrefix+"broadcast");if(f.getDuration()!==1/0)i&&(f.slider.style.display="",i.remove()),e.setProgressRail(n),f.forcedHandlePause||e.setCurrentRail(n);else if(!i){var r=o.default.createElement("span");r.className=f.options.classPrefix+"broadcast",r.innerText=s.default.t("mejs.live-broadcast"),f.slider.style.display="none"}}),i.addEventListener("timeupdate",function(n){var i=t.querySelector("."+f.options.classPrefix+"broadcast");if(f.getDuration()!==1/0)i&&(f.slider.style.display="",i.remove()),e.setProgressRail(n),f.forcedHandlePause||e.setCurrentRail(n),y();else if(!i){var r=o.default.createElement("span");r.className=f.options.classPrefix+"broadcast",r.innerText=s.default.t("mejs.live-broadcast"),t.querySelector("."+f.options.classPrefix+"time-rail").appendChild(r),f.slider.style.display="none"}}),f.container.addEventListener("controlsresize",function(t){f.getDuration()!==1/0&&(e.setProgressRail(t),f.forcedHandlePause||e.setCurrentRail(t))})},setProgressRail:function(e){var t=this,n=void 0!==e?e.detail.target||e.target:t.media,i=null;n&&n.buffered&&n.buffered.length>0&&n.buffered.end&&t.getDuration()?i=n.buffered.end(n.buffered.length-1)/t.getDuration():n&&void 0!==n.bytesTotal&&n.bytesTotal>0&&void 0!==n.bufferedBytes?i=n.bufferedBytes/n.bytesTotal:e&&e.lengthComputable&&0!==e.total&&(i=e.loaded/e.total),null!==i&&(i=Math.min(1,Math.max(0,i)),t.loaded&&t.setTransformStyle(t.loaded,"scaleX("+i+")"))},setCurrentRailHandle:function(e){var t=this;t.setCurrentRailMain(t,e)},setCurrentRail:function(){var e=this;e.setCurrentRailMain(e)},setCurrentRailMain:function(e,t){if(void 0!==e.getCurrentTime()&&e.getDuration()){var n=void 0===t?e.getCurrentTime():t;if(e.total&&e.handle){var i=parseFloat(getComputedStyle(e.total).width),o=Math.round(i*n/e.getDuration()),r=o-Math.round(e.handle.offsetWidth/2);if(r=r<0?0:r,e.setTransformStyle(e.current,"scaleX("+o/i+")"),e.setTransformStyle(e.handle,"translateX("+r+"px)"),e.options.useSmoothHover&&!(0,u.hasClass)(e.hovered,"no-hover")){var a=parseInt(e.hovered.getAttribute("pos")),s=(a=isNaN(a)?0:a)/i-r/i;e.hovered.style.left=r+"px",e.setTransformStyle(e.hovered,"scaleX("+s+")"),s>=0?(0,u.removeClass)(e.hovered,"negative"):(0,u.addClass)(e.hovered,"negative")}}}}})},{17:17,2:2,24:24,25:25,29:29,5:5}],12:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=e(17),a=i(r),s=e(29),l=e(25);Object.assign(r.config,{duration:0,timeAndDurationSeparator:"<span> | </span>"}),Object.assign(a.default.prototype,{buildcurrent:function(e,t,n,i){var r=this,a=o.default.createElement("div");a.className=r.options.classPrefix+"time",a.setAttribute("role","timer"),a.setAttribute("aria-live","off"),a.innerHTML='<span class="'+r.options.classPrefix+'currenttime">'+(0,s.secondsToTimeCode)(0,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength)+"</span>",r.addControlElement(a,"current"),i.addEventListener("timeupdate",function(){r.controlsAreVisible&&e.updateCurrent()})},buildduration:function(e,t,n,i){var r=this;if(t.lastChild.querySelector("."+r.options.classPrefix+"currenttime"))t.querySelector("."+r.options.classPrefix+"time").innerHTML+=r.options.timeAndDurationSeparator+'<span class="'+r.options.classPrefix+'duration">'+(0,s.secondsToTimeCode)(r.options.duration,r.options.alwaysShowHours,r.options.showTimecodeFrameCount,r.options.framesPerSecond,r.options.secondsDecimalLength)+"</span>";else{t.querySelector("."+r.options.classPrefix+"currenttime")&&(0,l.addClass)(t.querySelector("."+r.options.classPrefix+"currenttime").parentNode,r.options.classPrefix+"currenttime-container");var a=o.default.createElement("div");a.className=r.options.classPrefix+"time "+r.options.classPrefix+"duration-container",a.innerHTML='<span class="'+r.options.classPrefix+'duration">'+(0,s.secondsToTimeCode)(r.options.duration,r.options.alwaysShowHours,r.options.showTimecodeFrameCount,r.options.framesPerSecond,r.options.secondsDecimalLength)+"</span>",r.addControlElement(a,"duration")}i.addEventListener("timeupdate",function(){r.controlsAreVisible&&e.updateDuration()})},updateCurrent:function(){var e=this,t=e.getCurrentTime();isNaN(t)&&(t=0);var n=(0,s.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength);n.length>5?(0,l.addClass)(e.container,e.options.classPrefix+"long-video"):(0,l.removeClass)(e.container,e.options.classPrefix+"long-video"),e.controls.querySelector("."+e.options.classPrefix+"currenttime")&&(e.controls.querySelector("."+e.options.classPrefix+"currenttime").innerText=n)},updateDuration:function(){var e=this,t=e.getDuration();(isNaN(t)||t===1/0||t<0)&&(e.media.duration=e.options.duration=t=0),e.options.duration>0&&(t=e.options.duration);var n=(0,s.secondsToTimeCode)(t,e.options.alwaysShowHours,e.options.showTimecodeFrameCount,e.options.framesPerSecond,e.options.secondsDecimalLength);n.length>5?(0,l.addClass)(e.container,e.options.classPrefix+"long-video"):(0,l.removeClass)(e.container,e.options.classPrefix+"long-video"),e.controls.querySelector("."+e.options.classPrefix+"duration")&&t>0&&(e.controls.querySelector("."+e.options.classPrefix+"duration").innerHTML=n)}})},{17:17,2:2,25:25,29:29}],13:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=i(e(7)),a=i(e(5)),s=e(17),l=i(s),d=e(29),u=e(26),c=e(25);Object.assign(s.config,{startLanguage:"",tracksText:null,chaptersText:null,tracksAriaLive:!1,hideCaptionsButtonWhenEmpty:!0,toggleCaptionsButtonWhenOnlyOne:!1,slidesSelector:""}),Object.assign(l.default.prototype,{hasChapters:!1,buildtracks:function(e,t,n,i){if(e.tracks.length||e.trackFiles&&0!==!e.trackFiles.length){var r=this,s=r.options.tracksAriaLive?' role="log" aria-live="assertive" aria-atomic="false"':"",l=(0,u.isString)(r.options.tracksText)?r.options.tracksText:a.default.t("mejs.captions-subtitles"),d=(0,u.isString)(r.options.chaptersText)?r.options.chaptersText:a.default.t("mejs.captions-chapters"),f=null===e.trackFiles?e.tracks.length:e.trackFiles.length;if(r.domNode.textTracks)for(var p=r.domNode.textTracks.length-1;p>=0;p--)r.domNode.textTracks[p].mode="hidden";r.cleartracks(e),e.captions=o.default.createElement("div"),e.captions.className=r.options.classPrefix+"captions-layer "+r.options.classPrefix+"layer",e.captions.innerHTML='<div class="'+r.options.classPrefix+"captions-position "+r.options.classPrefix+'captions-position-hover"'+s+'><span class="'+r.options.classPrefix+'captions-text"></span></div>',e.captions.style.display="none",n.insertBefore(e.captions,n.firstChild),e.captionsText=e.captions.querySelector("."+r.options.classPrefix+"captions-text"),e.captionsButton=o.default.createElement("div"),e.captionsButton.className=r.options.classPrefix+"button "+r.options.classPrefix+"captions-button",e.captionsButton.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+l+'" aria-label="'+l+'" tabindex="0"></button><div class="'+r.options.classPrefix+"captions-selector "+r.options.classPrefix+'offscreen"><ul class="'+r.options.classPrefix+'captions-selector-list"><li class="'+r.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+r.options.classPrefix+'captions-selector-input" name="'+e.id+'_captions" id="'+e.id+'_captions_none" value="none" checked disabled><label class="'+r.options.classPrefix+"captions-selector-label "+r.options.classPrefix+'captions-selected" for="'+e.id+'_captions_none">'+a.default.t("mejs.none")+"</label></li></ul></div>",r.addControlElement(e.captionsButton,"tracks"),e.captionsButton.querySelector("."+r.options.classPrefix+"captions-selector-input").disabled=!1,e.chaptersButton=o.default.createElement("div"),e.chaptersButton.className=r.options.classPrefix+"button "+r.options.classPrefix+"chapters-button",e.chaptersButton.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+d+'" aria-label="'+d+'" tabindex="0"></button><div class="'+r.options.classPrefix+"chapters-selector "+r.options.classPrefix+'offscreen"><ul class="'+r.options.classPrefix+'chapters-selector-list"></ul></div>';for(var m=0,h=0;h<f;h++){var v=e.tracks[h].kind;"subtitles"===v||"captions"===v?m++:"chapters"!==v||t.querySelector("."+r.options.classPrefix+"chapter-selector")||e.captionsButton.parentNode.insertBefore(e.chaptersButton,e.captionsButton)}e.trackToLoad=-1,e.selectedTrack=null,e.isLoadingTrack=!1;for(var y=0;y<f;y++){var g=e.tracks[y].kind;"subtitles"!==g&&"captions"!==g||e.addTrackButton(e.tracks[y].trackId,e.tracks[y].srclang,e.tracks[y].label)}e.loadNextTrack();var b=["mouseenter","focusin"],E=["mouseleave","focusout"];if(r.options.toggleCaptionsButtonWhenOnlyOne&&1===m)e.captionsButton.addEventListener("click",function(){var t="none";null===e.selectedTrack&&(t=e.tracks[0].trackId),e.setTrack(t)});else{for(var S=e.captionsButton.querySelectorAll("."+r.options.classPrefix+"captions-selector-label"),w=e.captionsButton.querySelectorAll("input[type=radio]"),x=0,P=b.length;x<P;x++)e.captionsButton.addEventListener(b[x],function(){(0,c.removeClass)(this.querySelector("."+r.options.classPrefix+"captions-selector"),r.options.classPrefix+"offscreen")});for(var T=0,C=E.length;T<C;T++)e.captionsButton.addEventListener(E[T],function(){(0,c.addClass)(this.querySelector("."+r.options.classPrefix+"captions-selector"),r.options.classPrefix+"offscreen")});for(var _=0,k=w.length;_<k;_++)w[_].addEventListener("click",function(){e.setTrack(this.value)});for(var N=0,A=S.length;N<A;N++)S[N].addEventListener("click",function(){var e=(0,c.siblings)(this,function(e){return"INPUT"===e.tagName})[0],t=(0,u.createEvent)("click",e);e.dispatchEvent(t)});e.captionsButton.addEventListener("keydown",function(e){e.stopPropagation()})}for(var L=0,F=b.length;L<F;L++)e.chaptersButton.addEventListener(b[L],function(){this.querySelector("."+r.options.classPrefix+"chapters-selector-list").children.length&&(0,c.removeClass)(this.querySelector("."+r.options.classPrefix+"chapters-selector"),r.options.classPrefix+"offscreen")});for(var j=0,I=E.length;j<I;j++)e.chaptersButton.addEventListener(E[j],function(){(0,c.addClass)(this.querySelector("."+r.options.classPrefix+"chapters-selector"),r.options.classPrefix+"offscreen")});e.chaptersButton.addEventListener("keydown",function(e){e.stopPropagation()}),e.options.alwaysShowControls?(0,c.addClass)(e.container.querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover"):(e.container.addEventListener("controlsshown",function(){(0,c.addClass)(e.container.querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover")}),e.container.addEventListener("controlshidden",function(){i.paused||(0,c.removeClass)(e.container.querySelector("."+r.options.classPrefix+"captions-position"),r.options.classPrefix+"captions-position-hover")})),i.addEventListener("timeupdate",function(){e.displayCaptions()}),""!==e.options.slidesSelector&&(e.slidesContainer=o.default.querySelectorAll(e.options.slidesSelector),i.addEventListener("timeupdate",function(){e.displaySlides()}))}},cleartracks:function(e){e&&(e.captions&&e.captions.remove(),e.chapters&&e.chapters.remove(),e.captionsText&&e.captionsText.remove(),e.captionsButton&&e.captionsButton.remove(),e.chaptersButton&&e.chaptersButton.remove())},rebuildtracks:function(){var e=this;e.findTracks(),e.buildtracks(e,e.controls,e.layers,e.media)},findTracks:function(){var e=this,t=null===e.trackFiles?e.node.querySelectorAll("track"):e.trackFiles,n=t.length;e.tracks=[];for(var i=0;i<n;i++){var o=t[i],r=o.getAttribute("srclang").toLowerCase()||"",a=e.id+"_track_"+i+"_"+o.getAttribute("kind")+"_"+r;e.tracks.push({trackId:a,srclang:r,src:o.getAttribute("src"),kind:o.getAttribute("kind"),label:o.getAttribute("label")||"",entries:[],isLoaded:!1})}},setTrack:function(e){for(var t=this,n=t.captionsButton.querySelectorAll('input[type="radio"]'),i=t.captionsButton.querySelectorAll("."+t.options.classPrefix+"captions-selected"),o=t.captionsButton.querySelector('input[value="'+e+'"]'),r=0,a=n.length;r<a;r++)n[r].checked=!1;for(var s=0,l=i.length;s<l;s++)(0,c.removeClass)(i[s],t.options.classPrefix+"captions-selected");o.checked=!0;for(var d=(0,c.siblings)(o,function(e){return(0,c.hasClass)(e,t.options.classPrefix+"captions-selector-label")}),f=0,p=d.length;f<p;f++)(0,c.addClass)(d[f],t.options.classPrefix+"captions-selected");if("none"===e)t.selectedTrack=null,(0,c.removeClass)(t.captionsButton,t.options.classPrefix+"captions-enabled");else for(var m=0,h=t.tracks.length;m<h;m++){var v=t.tracks[m];if(v.trackId===e){null===t.selectedTrack&&(0,c.addClass)(t.captionsButton,t.options.classPrefix+"captions-enabled"),t.selectedTrack=v,t.captions.setAttribute("lang",t.selectedTrack.srclang),t.displayCaptions();break}}var y=(0,u.createEvent)("captionschange",t.media);y.detail.caption=t.selectedTrack,t.media.dispatchEvent(y)},loadNextTrack:function(){var e=this;++e.trackToLoad<e.tracks.length?(e.isLoadingTrack=!0,e.loadTrack(e.trackToLoad)):(e.isLoadingTrack=!1,e.checkForTracks())},loadTrack:function(e){var t=this,n=t.tracks[e];void 0===n||void 0===n.src&&""===n.src||(0,c.ajax)(n.src,"text",function(e){n.entries="string"==typeof e&&/<tt\s+xml/gi.exec(e)?r.default.TrackFormatParser.dfxp.parse(e):r.default.TrackFormatParser.webvtt.parse(e),n.isLoaded=!0,t.enableTrackButton(n),t.loadNextTrack(),"slides"===n.kind?t.setupSlides(n):"chapters"!==n.kind||t.hasChapters||(t.drawChapters(n),t.hasChapters=!0)},function(){t.removeTrackButton(n.trackId),t.loadNextTrack()})},enableTrackButton:function(e){var t=this,n=e.srclang,i=o.default.getElementById(""+e.trackId);if(i){var s=e.label;""===s&&(s=a.default.t(r.default.language.codes[n])||n),i.disabled=!1;for(var l=(0,c.siblings)(i,function(e){return(0,c.hasClass)(e,t.options.classPrefix+"captions-selector-label")}),d=0,f=l.length;d<f;d++)l[d].innerHTML=s;if(t.options.startLanguage===n){i.checked=!0;var p=(0,u.createEvent)("click",i);i.dispatchEvent(p)}}},removeTrackButton:function(e){var t=o.default.getElementById(""+e);if(t){var n=t.closest("li");n&&n.remove()}},addTrackButton:function(e,t,n){var i=this;""===n&&(n=a.default.t(r.default.language.codes[t])||t),i.captionsButton.querySelector("ul").innerHTML+='<li class="'+i.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+i.options.classPrefix+'captions-selector-input" name="'+i.id+'_captions" id="'+e+'" value="'+e+'" disabled><label class="'+i.options.classPrefix+'captions-selector-label"for="'+e+'">'+n+"</label></li>"},checkForTracks:function(){var e=this,t=!1;if(e.options.hideCaptionsButtonWhenEmpty){for(var n=0,i=e.tracks.length;n<i;n++){var o=e.tracks[n].kind;if(("subtitles"===o||"captions"===o)&&e.tracks[n].isLoaded){t=!0;break}}e.captionsButton.style.display=t?"":"none",e.setControlsSize()}},displayCaptions:function(){if(void 0!==this.tracks){var e=this,t=e.selectedTrack;if(null!==t&&t.isLoaded){var n=e.searchTrackPosition(t.entries,e.media.currentTime);if(n>-1)return e.captionsText.innerHTML=function(e){var t=o.default.createElement("div");t.innerHTML=e;for(var n=t.getElementsByTagName("script"),i=n.length;i--;)n[i].remove();for(var r=t.getElementsByTagName("*"),a=0,s=r.length;a<s;a++)for(var l=r[a].attributes,d=Array.prototype.slice.call(l),u=0,c=d.length;u<c;u++)d[u].name.startsWith("on")||d[u].value.startsWith("javascript")?r[a].remove():"style"===d[u].name&&r[a].removeAttribute(d[u].name);return t.innerHTML}(t.entries[n].text),e.captionsText.className=e.options.classPrefix+"captions-text "+(t.entries[n].identifier||""),e.captions.style.display="",void(e.captions.style.height="0px");e.captions.style.display="none"}else e.captions.style.display="none"}},setupSlides:function(e){var t=this;t.slides=e,t.slides.entries.imgs=[t.slides.entries.length],t.showSlide(0)},showSlide:function(e){var t=this,n=this;if(void 0!==n.tracks&&void 0!==n.slidesContainer){var i=n.slides.entries[e].text,r=n.slides.entries[e].imgs;if(void 0===r||void 0===r.fadeIn){var a=o.default.createElement("img");a.src=i,a.addEventListener("load",function(){var e=t,i=(0,c.siblings)(e,function(e){return i(e)});e.style.display="none",n.slidesContainer.innerHTML+=e.innerHTML,(0,c.fadeIn)(n.slidesContainer.querySelector(a));for(var o=0,r=i.length;o<r;o++)(0,c.fadeOut)(i[o],400)}),n.slides.entries[e].imgs=r=a}else if(!(0,c.visible)(r)){var s=(0,c.siblings)(self,function(e){return s(e)});(0,c.fadeIn)(n.slidesContainer.querySelector(r));for(var l=0,d=s.length;l<d;l++)(0,c.fadeOut)(s[l])}}},displaySlides:function(){var e=this;if(void 0!==this.slides){var t=e.slides,n=e.searchTrackPosition(t.entries,e.media.currentTime);n>-1&&e.showSlide(n)}},drawChapters:function(e){var t=this,n=e.entries.length;if(n){t.chaptersButton.querySelector("ul").innerHTML="";for(var i=0;i<n;i++)t.chaptersButton.querySelector("ul").innerHTML+='<li class="'+t.options.classPrefix+'chapters-selector-list-item" role="menuitemcheckbox" aria-live="polite" aria-disabled="false" aria-checked="false"><input type="radio" class="'+t.options.classPrefix+'captions-selector-input" name="'+t.id+'_chapters" id="'+t.id+"_chapters_"+i+'" value="'+e.entries[i].start+'" disabled><label class="'+t.options.classPrefix+'chapters-selector-label"for="'+t.id+"_chapters_"+i+'">'+e.entries[i].text+"</label></li>";for(var o=t.chaptersButton.querySelectorAll('input[type="radio"]'),r=t.chaptersButton.querySelectorAll("."+t.options.classPrefix+"chapters-selector-label"),a=0,s=o.length;a<s;a++)o[a].disabled=!1,o[a].checked=!1,o[a].addEventListener("click",function(){var e=this,n=t.chaptersButton.querySelectorAll("li"),i=(0,c.siblings)(e,function(e){return(0,c.hasClass)(e,t.options.classPrefix+"chapters-selector-label")})[0];e.checked=!0,e.parentNode.setAttribute("aria-checked",!0),(0,c.addClass)(i,t.options.classPrefix+"chapters-selected"),(0,c.removeClass)(t.chaptersButton.querySelector("."+t.options.classPrefix+"chapters-selected"),t.options.classPrefix+"chapters-selected");for(var o=0,r=n.length;o<r;o++)n[o].setAttribute("aria-checked",!1);t.media.setCurrentTime(parseFloat(e.value)),t.media.paused&&t.media.play()});for(var l=0,d=r.length;l<d;l++)r[l].addEventListener("click",function(){var e=(0,c.siblings)(this,function(e){return"INPUT"===e.tagName})[0],t=(0,u.createEvent)("click",e);e.dispatchEvent(t)})}},searchTrackPosition:function(e,t){for(var n=0,i=e.length-1,o=void 0,r=void 0,a=void 0;n<=i;){if(o=n+i>>1,r=e[o].start,a=e[o].stop,t>=r&&t<a)return o;r<t?n=o+1:r>t&&(i=o-1)}return-1}}),r.default.language={codes:{af:"mejs.afrikaans",sq:"mejs.albanian",ar:"mejs.arabic",be:"mejs.belarusian",bg:"mejs.bulgarian",ca:"mejs.catalan",zh:"mejs.chinese","zh-cn":"mejs.chinese-simplified","zh-tw":"mejs.chines-traditional",hr:"mejs.croatian",cs:"mejs.czech",da:"mejs.danish",nl:"mejs.dutch",en:"mejs.english",et:"mejs.estonian",fl:"mejs.filipino",fi:"mejs.finnish",fr:"mejs.french",gl:"mejs.galician",de:"mejs.german",el:"mejs.greek",ht:"mejs.haitian-creole",iw:"mejs.hebrew",hi:"mejs.hindi",hu:"mejs.hungarian",is:"mejs.icelandic",id:"mejs.indonesian",ga:"mejs.irish",it:"mejs.italian",ja:"mejs.japanese",ko:"mejs.korean",lv:"mejs.latvian",lt:"mejs.lithuanian",mk:"mejs.macedonian",ms:"mejs.malay",mt:"mejs.maltese",no:"mejs.norwegian",fa:"mejs.persian",pl:"mejs.polish",pt:"mejs.portuguese",ro:"mejs.romanian",ru:"mejs.russian",sr:"mejs.serbian",sk:"mejs.slovak",sl:"mejs.slovenian",es:"mejs.spanish",sw:"mejs.swahili",sv:"mejs.swedish",tl:"mejs.tagalog",th:"mejs.thai",tr:"mejs.turkish",uk:"mejs.ukrainian",vi:"mejs.vietnamese",cy:"mejs.welsh",yi:"mejs.yiddish"}},r.default.TrackFormatParser={webvtt:{pattern:/^((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{1,3})?) --\> ((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{3})?)(.*)$/,parse:function(e){for(var t=e.split(/\r?\n/),n=[],i=void 0,o=void 0,r=void 0,a=0,s=t.length;a<s;a++){if((i=this.pattern.exec(t[a]))&&a<t.length){for(a-1>=0&&""!==t[a-1]&&(r=t[a-1]),o=t[++a],a++;""!==t[a]&&a<t.length;)o=o+"\n"+t[a],a++;o=o.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),n.push({identifier:r,start:0===(0,d.convertSMPTEtoSeconds)(i[1])?.2:(0,d.convertSMPTEtoSeconds)(i[1]),stop:(0,d.convertSMPTEtoSeconds)(i[3]),text:o,settings:i[5]})}r=""}return n}},dfxp:{parse:function(e){var t=(e=$(e).filter("tt")).firstChild,n=t.querySelectorAll("p"),i=e.getElementById(""+t.attr("style")),o=[],r=void 0;if(i.length){i.removeAttribute("id");var a=i.attributes;if(a.length){r={};for(var s=0,l=a.length;s<l;s++)r[a[s].name.split(":")[1]]=a[s].value}}for(var u=0,c=n.length;u<c;u++){var f=void 0,p={start:null,stop:null,style:null,text:null};if(n.eq(u).attr("begin")&&(p.start=(0,d.convertSMPTEtoSeconds)(n.eq(u).attr("begin"))),!p.start&&n.eq(u-1).attr("end")&&(p.start=(0,d.convertSMPTEtoSeconds)(n.eq(u-1).attr("end"))),n.eq(u).attr("end")&&(p.stop=(0,d.convertSMPTEtoSeconds)(n.eq(u).attr("end"))),!p.stop&&n.eq(u+1).attr("begin")&&(p.stop=(0,d.convertSMPTEtoSeconds)(n.eq(u+1).attr("begin"))),r){f="";for(var m in r)f+=m+":"+r[m]+";"}f&&(p.style=f),0===p.start&&(p.start=.2),p.text=n.eq(u).innerHTML.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),o.push(p)}return o}}}},{17:17,2:2,25:25,26:26,29:29,5:5,7:7}],14:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=e(17),a=i(r),s=i(e(5)),l=e(24),d=e(26),u=e(25);Object.assign(r.config,{muteText:null,unmuteText:null,allyVolumeControlText:null,hideVolumeOnTouchDevices:!0,audioVolume:"horizontal",videoVolume:"vertical",startVolume:.8}),Object.assign(a.default.prototype,{buildvolume:function(e,t,n,i){if(!l.IS_ANDROID&&!l.IS_IOS||!this.options.hideVolumeOnTouchDevices){var r=this,a=r.isVideo?r.options.videoVolume:r.options.audioVolume,c=(0,d.isString)(r.options.muteText)?r.options.muteText:s.default.t("mejs.mute"),f=(0,d.isString)(r.options.unmuteText)?r.options.unmuteText:s.default.t("mejs.unmute"),p=(0,d.isString)(r.options.allyVolumeControlText)?r.options.allyVolumeControlText:s.default.t("mejs.volume-help-text"),m=o.default.createElement("div");if(m.className=r.options.classPrefix+"button "+r.options.classPrefix+"volume-button "+r.options.classPrefix+"mute",m.innerHTML="horizontal"===a?'<button type="button" aria-controls="'+r.id+'" title="'+c+'" aria-label="'+c+'" tabindex="0"></button>':'<button type="button" aria-controls="'+r.id+'" title="'+c+'" aria-label="'+c+'" tabindex="0"></button><a href="javascript:void(0);" class="'+r.options.classPrefix+'volume-slider" aria-label="'+s.default.t("mejs.volume-slider")+'" aria-valuemin="0" aria-valuemax="100" role="slider" aria-orientation="vertical"><span class="'+r.options.classPrefix+'offscreen">'+p+'</span><div class="'+r.options.classPrefix+'volume-total"><div class="'+r.options.classPrefix+'volume-current"></div><div class="'+r.options.classPrefix+'volume-handle"></div></div></a>',r.addControlElement(m,"volume"),"horizontal"===a){var h=o.default.createElement("a");h.className=r.options.classPrefix+"horizontal-volume-slider",h.href="javascript:void(0);",h.setAttribute("aria-label",s.default.t("mejs.volume-slider")),h.setAttribute("aria-valuemin",0),h.setAttribute("aria-valuemax",100),h.setAttribute("role","slider"),h.innerHTML+='<span class="'+r.options.classPrefix+'offscreen">'+p+'</span><div class="'+r.options.classPrefix+'horizontal-volume-total"><div class="'+r.options.classPrefix+'horizontal-volume-current"></div><div class="'+r.options.classPrefix+'horizontal-volume-handle"></div></div>',m.parentNode.insertBefore(h,m.nextSibling)}var v=!1,y=!1,g=!1,b=function(){var e=Math.floor(100*i.volume);E.setAttribute("aria-valuenow",e),E.setAttribute("aria-valuetext",e+"%")},E="vertical"===a?r.container.querySelector("."+r.options.classPrefix+"volume-slider"):r.container.querySelector("."+r.options.classPrefix+"horizontal-volume-slider"),S="vertical"===a?r.container.querySelector("."+r.options.classPrefix+"volume-total"):r.container.querySelector("."+r.options.classPrefix+"horizontal-volume-total"),w="vertical"===a?r.container.querySelector("."+r.options.classPrefix+"volume-current"):r.container.querySelector("."+r.options.classPrefix+"horizontal-volume-current"),x="vertical"===a?r.container.querySelector("."+r.options.classPrefix+"volume-handle"):r.container.querySelector("."+r.options.classPrefix+"horizontal-volume-handle"),P=function(e){if(null!==e&&!isNaN(e)&&void 0!==e){if(e=Math.max(0,e),0===(e=Math.min(e,1))){(0,u.removeClass)(m,r.options.classPrefix+"mute"),(0,u.addClass)(m,r.options.classPrefix+"unmute");var t=m.firstElementChild;t.setAttribute("title",f),t.setAttribute("aria-label",f)}else{(0,u.removeClass)(m,r.options.classPrefix+"unmute"),(0,u.addClass)(m,r.options.classPrefix+"mute");var n=m.firstElementChild;n.setAttribute("title",c),n.setAttribute("aria-label",c)}var i=100*e+"%",o=getComputedStyle(x);"vertical"===a?(w.style.bottom=0,w.style.height=i,x.style.bottom=i,x.style.marginBottom=-parseFloat(o.height)/2+"px"):(w.style.left=0,w.style.width=i,x.style.left=i,x.style.marginLeft=-parseFloat(o.width)/2+"px")}},T=function(e){var t=(0,u.offset)(S),n=getComputedStyle(S);g=!0;var o=null;if("vertical"===a){var r=parseFloat(n.height);if(o=(r-(e.pageY-t.top))/r,0===t.top||0===t.left)return}else{var s=parseFloat(n.width);o=(e.pageX-t.left)/s}o=Math.max(0,o),o=Math.min(o,1),P(o),i.setMuted(0===o),i.setVolume(o),e.preventDefault(),e.stopPropagation()},C=function(){i.muted?(P(0),(0,u.removeClass)(m,r.options.classPrefix+"mute"),(0,u.addClass)(m,r.options.classPrefix+"unmute")):(P(i.volume),(0,u.removeClass)(m,r.options.classPrefix+"unmute"),(0,u.addClass)(m,r.options.classPrefix+"mute"))};m.addEventListener("mouseenter",function(e){e.target===m&&(E.style.display="block",y=!0,e.preventDefault(),e.stopPropagation())}),m.addEventListener("focusin",function(){E.style.display="block",y=!0}),m.addEventListener("focusout",function(e){e.relatedTarget&&(!e.relatedTarget||e.relatedTarget.matches("."+r.options.classPrefix+"volume-slider"))||"vertical"!==a||(E.style.display="none")}),m.addEventListener("mouseleave",function(){y=!1,v||"vertical"!==a||(E.style.display="none")}),m.addEventListener("focusout",function(){y=!1}),m.addEventListener("keydown",function(e){if(r.options.keyActions.length){var t=e.which||e.keyCode||0,n=i.volume;switch(t){case 38:n=Math.min(n+.1,1);break;case 40:n=Math.max(0,n-.1);break;default:return!0}v=!1,P(n),i.setVolume(n),e.preventDefault(),e.stopPropagation()}}),m.querySelector("button").addEventListener("click",function(){i.setMuted(!i.muted);var e=(0,d.createEvent)("volumechange",i);i.dispatchEvent(e)}),E.addEventListener("dragstart",function(){return!1}),E.addEventListener("mouseover",function(){y=!0}),E.addEventListener("focusin",function(){E.style.display="block",y=!0}),E.addEventListener("focusout",function(){y=!1,v||"vertical"!==a||(E.style.display="none")}),E.addEventListener("mousedown",function(e){T(e),r.globalBind("mousemove.vol",function(e){var t=e.target;v&&(t===E||t.closest("vertical"===a?"."+r.options.classPrefix+"volume-slider":"."+r.options.classPrefix+"horizontal-volume-slider"))&&T(e)}),r.globalBind("mouseup.vol",function(){v=!1,r.globalUnbind("mousemove.vol mouseup.vol"),y||"vertical"!==a||(E.style.display="none")}),v=!0,e.preventDefault(),e.stopPropagation()}),i.addEventListener("volumechange",function(e){v||C(),b()});var _=!1;i.addEventListener("rendererready",function(){g||setTimeout(function(){_=!0,(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0),i.setVolume(e.options.startVolume),r.setControlsSize()},250)}),i.addEventListener("loadedmetadata",function(){setTimeout(function(){g||_||((0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0),i.setVolume(e.options.startVolume),r.setControlsSize()),_=!1},250)}),(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0,C()),r.container.addEventListener("controlsresize",function(){C()})}}})},{17:17,2:2,24:24,25:25,26:26,5:5}],15:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"Aucun","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"Anglais","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"Français","mejs.galician":"Galician","mejs.german":"Allemand","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italien","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguais","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Espagnol","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],16:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(7));"undefined"!=typeof jQuery?r.default.$=o.default.jQuery=o.default.$=jQuery:"undefined"!=typeof Zepto?r.default.$=o.default.Zepto=o.default.$=Zepto:"undefined"!=typeof ender&&(r.default.$=o.default.ender=o.default.$=ender)},{3:3,7:7}],17:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0}),n.config=void 0;var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),s=i(e(3)),l=i(e(2)),d=i(e(7)),u=i(e(6)),c=i(e(5)),f=e(24),p=e(26),m=e(29),h=e(27),v=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(e(25));d.default.mepIndex=0,d.default.players={};var y=n.config={poster:"",showPosterWhenEnded:!1,showPosterWhenPaused:!1,defaultVideoWidth:480,defaultVideoHeight:270,videoWidth:-1,videoHeight:-1,defaultAudioWidth:400,defaultAudioHeight:40,defaultSeekBackwardInterval:function(e){return.05*e.getDuration()},defaultSeekForwardInterval:function(e){return.05*e.getDuration()},setDimensions:!0,audioWidth:-1,audioHeight:-1,loop:!1,autoRewind:!0,enableAutosize:!0,timeFormat:"",alwaysShowHours:!1,showTimecodeFrameCount:!1,framesPerSecond:25,alwaysShowControls:!1,hideVideoControlsOnLoad:!1,hideVideoControlsOnPause:!1,clickToPlayPause:!0,controlsTimeoutDefault:1500,controlsTimeoutMouseEnter:2500,controlsTimeoutMouseLeave:1e3,iPadUseNativeControls:!1,iPhoneUseNativeControls:!1,AndroidUseNativeControls:!1,features:["playpause","current","progress","duration","tracks","volume","fullscreen"],isVideo:!0,stretching:"auto",classPrefix:"mejs__",enableKeyboard:!0,pauseOtherPlayers:!0,secondsDecimalLength:0,keyActions:[{keys:[32,179],action:function(e,t){f.IS_FIREFOX||(t.paused||t.ended?t.play():t.pause())}},{keys:[38],action:function(e,t){(e.container.querySelector("."+y.classPrefix+"volume-button>button").matches(":focus")||e.container.querySelector("."+y.classPrefix+"volume-slider").matches(":focus"))&&(e.container.querySelector("."+y.classPrefix+"volume-slider").style.display=""),e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.min(t.volume+.1,1);t.setVolume(n),n>0&&t.setMuted(!1)}},{keys:[40],action:function(e,t){(e.container.querySelector("."+y.classPrefix+"volume-button>button").matches(":focus")||e.container.querySelector("."+y.classPrefix+"volume-slider").matches(":focus"))&&(e.container.querySelector("."+y.classPrefix+"volume-slider").style.display=""),e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.max(t.volume-.1,0);t.setVolume(n),n<=.1&&t.setMuted(!0)}},{keys:[37,227],action:function(e,t){if(!isNaN(t.duration)&&t.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.max(t.currentTime-e.options.defaultSeekBackwardInterval(t),0);t.setCurrentTime(n)}}},{keys:[39,228],action:function(e,t){if(!isNaN(t.duration)&&t.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var n=Math.min(t.currentTime+e.options.defaultSeekForwardInterval(t),t.duration);t.setCurrentTime(n)}}},{keys:[70],action:function(e,t,n,i){i.ctrlKey||void 0!==e.enterFullScreen&&(e.isFullScreen?e.exitFullScreen():e.enterFullScreen())}},{keys:[77],action:function(e){e.container.querySelector("."+y.classPrefix+"volume-slider").style.display="",e.isVideo&&(e.showControls(),e.startControlsTimer()),e.media.muted?e.setMuted(!1):e.setMuted(!0)}}]};d.default.MepDefaults=y;var g=function(){function e(t,n){o(this,e);var i=this,r="string"==typeof t?l.default.getElementById(t):t;if(i.hasFocus=!1,i.controlsAreVisible=!0,i.controlsEnabled=!0,i.controlsTimer=null,!(i instanceof e))return new e(r,n);if(i.node=i.media=r,i.node){if(void 0!==i.media.player)return i.media.player;if(void 0===n){var a=i.node.getAttribute("data-mejsoptions");n=a?JSON.parse(a):{}}i.options=Object.assign({},y,n),i.options.loop&&!i.media.getAttribute("loop")?(i.media.loop=!0,i.node.loop=!0):i.media.loop&&(i.options.loop=!0),i.options.timeFormat||(i.options.timeFormat="mm:ss",i.options.alwaysShowHours&&(i.options.timeFormat="hh:mm:ss"),i.options.showTimecodeFrameCount&&(i.options.timeFormat+=":ff")),(0,m.calculateTimeFormat)(0,i.options,i.options.framesPerSecond||25),i.id="mep_"+d.default.mepIndex++,d.default.players[i.id]=i;var s=Object.assign({},i.options,{success:function(e,t){i._meReady(e,t)},error:function(e){i._handleError(e)}}),g=i.node.tagName.toLowerCase();if(i.isDynamic="audio"!==g&&"video"!==g&&"iframe"!==g,i.isVideo=i.isDynamic?i.options.isVideo:"audio"!==g&&i.options.isVideo,i.mediaFiles=null,i.trackFiles=null,f.IS_IPAD&&i.options.iPadUseNativeControls||f.IS_IPHONE&&i.options.iPhoneUseNativeControls)i.node.setAttribute("controls",!0),f.IS_IPAD&&i.node.getAttribute("autoplay")&&i.play();else if(!(i.isVideo||!i.isVideo&&i.options.features.length)||f.IS_ANDROID&&i.options.AndroidUseNativeControls)i.isVideo||i.options.features.length||(i.node.style.display="none");else{i.node.removeAttribute("controls");var b=i.isVideo?c.default.t("mejs.video-player"):c.default.t("mejs.audio-player"),E=l.default.createElement("span");if(E.className=i.options.classPrefix+"offscreen",E.innerText=b,i.media.parentNode.insertBefore(E,i.media),i.container=l.default.createElement("div"),i.container.id=i.id,i.container.className=i.options.classPrefix+"container "+i.options.classPrefix+"container-keyboard-inactive "+i.media.className,i.container.tabIndex=0,i.container.setAttribute("role","application"),i.container.setAttribute("aria-label",b),i.container.innerHTML='<div class="'+i.options.classPrefix+'inner"><div class="'+i.options.classPrefix+'mediaelement"></div><div class="'+i.options.classPrefix+'layers"></div><div class="'+i.options.classPrefix+'controls"></div><div class="'+i.options.classPrefix+'clear"></div></div>',i.container.addEventListener("focus",function(e){if(!i.controlsAreVisible&&!i.hasFocus&&i.controlsEnabled){i.showControls(!0);var t=(0,p.isNodeAfter)(e.relatedTarget,i.container)?"."+i.options.classPrefix+"controls ."+i.options.classPrefix+"button:last-child > button":"."+i.options.classPrefix+"playpause-button > button";i.container.querySelector(t).focus()}}),i.node.parentNode.insertBefore(i.container,i.node),i.options.features.length||(i.container.style.background="transparent",i.container.querySelector("."+i.options.classPrefix+"controls").style.display="none"),i.isVideo&&"fill"===i.options.stretching&&!v.hasClass(i.container.parentNode,i.options.classPrefix+"fill-container")){i.outerContainer=i.media.parentNode;var S=l.default.createElement("div");S.className=i.options.classPrefix+"fill-container",i.container.parentNode.insertBefore(S,i.container),S.appendChild(i.container)}if(f.IS_ANDROID&&v.addClass(i.container,i.options.classPrefix+"android"),f.IS_IOS&&v.addClass(i.container,i.options.classPrefix+"ios"),f.IS_IPAD&&v.addClass(i.container,i.options.classPrefix+"ipad"),f.IS_IPHONE&&v.addClass(i.container,i.options.classPrefix+"iphone"),v.addClass(i.container,i.isVideo?i.options.classPrefix+"video":i.options.classPrefix+"audio"),f.IS_SAFARI&&!f.IS_IOS){v.addClass(i.container,i.options.classPrefix+"hide-cues");for(var w=i.node.cloneNode(),x=i.node.children,P=[],T=[],C=0,_=x.length;C<_;C++){var k=x[C];!function(){switch(k.tagName.toLowerCase()){case"source":var e={};Array.prototype.slice.call(k.attributes).forEach(function(t){e[t.name]=t.value}),e.type=(0,h.formatType)(e.src,e.type),P.push(e);break;case"track":k.mode="hidden",T.push(k);break;default:w.appendChild(k)}}()}i.node.remove(),i.node=i.media=w,P.length&&(i.mediaFiles=P),T.length&&(i.trackFiles=T)}i.container.querySelector("."+i.options.classPrefix+"mediaelement").appendChild(i.node),i.media.player=i,i.controls=i.container.querySelector("."+i.options.classPrefix+"controls"),i.layers=i.container.querySelector("."+i.options.classPrefix+"layers");var N=i.isVideo?"video":"audio",A=N.substring(0,1).toUpperCase()+N.substring(1);i.options[N+"Width"]>0||i.options[N+"Width"].toString().indexOf("%")>-1?i.width=i.options[N+"Width"]:""!==i.node.style.width&&null!==i.node.style.width?i.width=i.node.style.width:i.node.getAttribute("width")?i.width=i.node.getAttribute("width"):i.width=i.options["default"+A+"Width"],i.options[N+"Height"]>0||i.options[N+"Height"].toString().indexOf("%")>-1?i.height=i.options[N+"Height"]:""!==i.node.style.height&&null!==i.node.style.height?i.height=i.node.style.height:i.node.getAttribute("height")?i.height=i.node.getAttribute("height"):i.height=i.options["default"+A+"Height"],i.initialAspectRatio=i.height>=i.width?i.width/i.height:i.height/i.width,i.setPlayerSize(i.width,i.height),s.pluginWidth=i.width,s.pluginHeight=i.height}if(new u.default(i.media,s,i.mediaFiles),void 0!==i.container&&i.options.features.length&&i.controlsAreVisible&&!i.options.hideVideoControlsOnLoad){var L=(0,p.createEvent)("controlsshown",i.container);i.container.dispatchEvent(L)}return i}}return a(e,[{key:"showControls",value:function(e){var t=this;if(e=void 0===e||e,!t.controlsAreVisible&&t.isVideo){if(e)!function(){v.fadeIn(t.controls,200,function(){v.removeClass(t.controls,t.options.classPrefix+"offscreen");var e=(0,p.createEvent)("controlsshown",t.container);t.container.dispatchEvent(e)});for(var e=t.container.querySelectorAll("."+t.options.classPrefix+"control"),n=0,i=e.length;n<i;n++)!function(n,i){v.fadeIn(e[n],200,function(){v.removeClass(e[n],t.options.classPrefix+"offscreen")})}(n)}();else{v.removeClass(t.controls,t.options.classPrefix+"offscreen"),t.controls.style.display="",t.controls.style.opacity=1;for(var n=t.container.querySelectorAll("."+t.options.classPrefix+"control"),i=0,o=n.length;i<o;i++)v.removeClass(n[i],t.options.classPrefix+"offscreen"),n[i].style.display="";var r=(0,p.createEvent)("controlsshown",t.container);t.container.dispatchEvent(r)}t.controlsAreVisible=!0,t.setControlsSize()}}},{key:"hideControls",value:function(e,t){var n=this;if(e=void 0===e||e,!0===t||!(!n.controlsAreVisible||n.options.alwaysShowControls||n.media.paused&&4===n.media.readyState&&(!n.options.hideVideoControlsOnLoad&&n.media.currentTime<=0||!n.options.hideVideoControlsOnPause&&n.media.currentTime>0)||n.isVideo&&!n.options.hideVideoControlsOnLoad&&!n.media.readyState||n.media.ended)){if(e)!function(){v.fadeOut(n.controls,200,function(){v.addClass(n.controls,n.options.classPrefix+"offscreen"),n.controls.style.display="";var e=(0,p.createEvent)("controlshidden",n.container);n.container.dispatchEvent(e)});for(var e=n.container.querySelectorAll("."+n.options.classPrefix+"control"),t=0,i=e.length;t<i;t++)!function(t,i){v.fadeOut(e[t],200,function(){v.addClass(e[t],n.options.classPrefix+"offscreen"),e[t].style.display=""})}(t)}();else{v.addClass(n.controls,n.options.classPrefix+"offscreen"),n.controls.style.display="",n.controls.style.opacity=0;for(var i=n.container.querySelectorAll("."+n.options.classPrefix+"control"),o=0,r=i.length;o<r;o++)v.addClass(i[o],n.options.classPrefix+"offscreen"),i[o].style.display="";var a=(0,p.createEvent)("controlshidden",n.container);n.container.dispatchEvent(a)}n.controlsAreVisible=!1}}},{key:"startControlsTimer",value:function(e){var t=this;e=void 0!==e?e:t.options.controlsTimeoutDefault,t.killControlsTimer("start"),t.controlsTimer=setTimeout(function(){t.hideControls(),t.killControlsTimer("hide")},e)}},{key:"killControlsTimer",value:function(){var e=this;null!==e.controlsTimer&&(clearTimeout(e.controlsTimer),delete e.controlsTimer,e.controlsTimer=null)}},{key:"disableControls",value:function(){var e=this;e.killControlsTimer(),e.controlsEnabled=!1,e.hideControls(!1,!0)}},{key:"enableControls",value:function(){var e=this;e.controlsEnabled=!0,e.showControls(!1)}},{key:"_meReady",value:function(e,t){var n=this,i=t.getAttribute("autoplay"),o=!(void 0===i||null===i||"false"===i),r=null!==e.rendererName&&/(native|html5)/i.test(n.media.rendererName);if(n.controls&&n.enableControls(),n.container&&n.container.querySelector("."+n.options.classPrefix+"overlay-play")&&(n.container.querySelector("."+n.options.classPrefix+"overlay-play").style.display=""),!n.created){if(n.created=!0,n.media=e,n.domNode=t,!(f.IS_ANDROID&&n.options.AndroidUseNativeControls||f.IS_IPAD&&n.options.iPadUseNativeControls||f.IS_IPHONE&&n.options.iPhoneUseNativeControls)){if(!n.isVideo&&!n.options.features.length)return o&&r&&n.play(),void(n.options.success&&("string"==typeof n.options.success?s.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n)));n.buildposter(n,n.controls,n.layers,n.media),n.buildkeyboard(n,n.controls,n.layers,n.media),n.buildoverlays(n,n.controls,n.layers,n.media),n.findTracks(),n.featurePosition={};for(var a=0,u=n.options.features.length;a<u;a++){var c=n.options.features[a];if(n["build"+c])try{n["build"+c](n,n.controls,n.layers,n.media)}catch(e){console.error("error building "+c,e)}}var h=(0,p.createEvent)("controlsready",n.container);n.container.dispatchEvent(h),n.setPlayerSize(n.width,n.height),n.setControlsSize(),n.isVideo&&(n.clickToPlayPauseCallback=function(){if(n.options.clickToPlayPause){var e=n.container.querySelector("."+n.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");n.media.paused&&t?n.pause():n.media.paused?n.play():n.pause(),e.setAttribute("aria-pressed",!t)}},n.createIframeLayer(),n.media.addEventListener("click",n.clickToPlayPauseCallback),!f.IS_ANDROID&&!f.IS_IOS||n.options.alwaysShowControls?(n.container.addEventListener("mouseenter",function(){n.controlsEnabled&&(n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter)))}),n.container.addEventListener("mousemove",function(){n.controlsEnabled&&(n.controlsAreVisible||n.showControls(),n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseEnter))}),n.container.addEventListener("mouseleave",function(){n.controlsEnabled&&(n.media.paused||n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))})):n.node.addEventListener("touchstart",function(){n.controlsAreVisible?n.hideControls(!1):n.controlsEnabled&&n.showControls(!1)}),n.options.hideVideoControlsOnLoad&&n.hideControls(!1),o&&!n.options.alwaysShowControls&&n.hideControls(),n.options.enableAutosize&&n.media.addEventListener("loadedmetadata",function(e){var t=void 0!==e?e.detail.target||e.target:n.media;n.options.videoHeight<=0&&!n.domNode.getAttribute("height")&&null!==t&&!isNaN(t.videoHeight)&&(n.setPlayerSize(t.videoWidth,t.videoHeight),n.setControlsSize(),n.media.setSize(t.videoWidth,t.videoHeight))})),n.media.addEventListener("play",function(){n.hasFocus=!0;for(var e in d.default.players)if(d.default.players.hasOwnProperty(e)){var t=d.default.players[e];t.id===n.id||!n.options.pauseOtherPlayers||t.paused||t.ended||(t.pause(),t.hasFocus=!1)}}),n.media.addEventListener("ended",function(){if(n.options.autoRewind)try{n.media.setCurrentTime(0),setTimeout(function(){var e=n.container.querySelector("."+n.options.classPrefix+"overlay-loading");e&&e.parentNode&&(e.parentNode.style.display="none")},20)}catch(e){}"function"==typeof n.media.renderer.stop?n.media.renderer.stop():n.media.pause(),n.setProgressRail&&n.setProgressRail(),n.setCurrentRail&&n.setCurrentRail(),n.options.loop?n.play():!n.options.alwaysShowControls&&n.controlsEnabled&&n.showControls()}),n.media.addEventListener("loadedmetadata",function(){(0,m.calculateTimeFormat)(n.duration,n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.isFullScreen||(n.setPlayerSize(n.width,n.height),n.setControlsSize())});var y=null;n.media.addEventListener("timeupdate",function(){isNaN(n.media.getDuration())||y===n.media.getDuration()||(y=n.media.getDuration(),(0,m.calculateTimeFormat)(y,n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.setControlsSize())}),n.container.addEventListener("click",function(e){v.addClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive")}),n.container.addEventListener("focusin",function(e){v.removeClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive"),!n.isVideo||f.IS_ANDROID||f.IS_IOS||!n.controlsEnabled||n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter))}),n.container.addEventListener("focusout",function(e){setTimeout(function(){e.relatedTarget&&n.keyboardAction&&!e.relatedTarget.closest("."+n.options.classPrefix+"container")&&(n.keyboardAction=!1,!n.isVideo||n.options.alwaysShowControls||n.media.paused||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))},0)}),setTimeout(function(){n.setPlayerSize(n.width,n.height),n.setControlsSize()},0),n.globalBind("resize",function(){n.isFullScreen||f.HAS_TRUE_NATIVE_FULLSCREEN&&l.default.webkitIsFullScreen||n.setPlayerSize(n.width,n.height),n.setControlsSize()})}o&&r&&n.play(),n.options.success&&("string"==typeof n.options.success?s.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n))}}},{key:"_handleError",value:function(e){var t=this;t.controls&&t.disableControls();var n=t.layers.querySelector("."+t.options.classPrefix+"overlay-play");n&&(n.style.display="none"),t.options.error&&t.options.error(e)}},{key:"setPlayerSize",value:function(e,t){var n=this;if(!n.options.setDimensions)return!1;switch(void 0!==e&&(n.width=e),void 0!==t&&(n.height=t),n.options.stretching){case"fill":n.isVideo?n.setFillMode():n.setDimensions(n.width,n.height);break;case"responsive":n.setResponsiveMode();break;case"none":n.setDimensions(n.width,n.height);break;default:!0===n.hasFluidMode()?n.setResponsiveMode():n.setDimensions(n.width,n.height)}}},{key:"hasFluidMode",value:function(){var e=this;return-1!==e.height.toString().indexOf("%")||e.node&&e.node.style.maxWidth&&"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width||e.node&&e.node.currentStyle&&"100%"===e.node.currentStyle.maxWidth}},{key:"setResponsiveMode",value:function(){var e=this,t=function(){for(var t=void 0,n=e.container;n;){try{if(f.IS_FIREFOX&&"html"===n.tagName.toLowerCase()&&s.default.self!==s.default.top&&null!==s.default.frameElement)return s.default.frameElement;t=n.parentElement}catch(e){t=n.parentElement}if(t&&v.visible(t))return t;n=t}return null}(),n=t?getComputedStyle(t,null):getComputedStyle(l.default.body,null),i=function(){return e.isVideo?e.media.videoWidth&&e.media.videoWidth>0?e.media.videoWidth:e.node.getAttribute("width")?e.node.getAttribute("width"):e.options.defaultVideoWidth:e.options.defaultAudioWidth}(),o=function(){return e.isVideo?e.media.videoHeight&&e.media.videoHeight>0?e.media.videoHeight:e.node.getAttribute("height")?e.node.getAttribute("height"):e.options.defaultVideoHeight:e.options.defaultAudioHeight}(),r=function(){var t=1;return e.isVideo?(t=e.media.videoWidth&&e.media.videoWidth>0&&e.media.videoHeight&&e.media.videoHeight>0?e.height>=e.width?e.media.videoWidth/e.media.videoHeight:e.media.videoHeight/e.media.videoWidth:e.initialAspectRatio,(isNaN(t)||t<.01||t>100)&&(t=1),t):t}(),a=parseFloat(n.height),d=void 0,u=parseFloat(n.width);if(d=e.isVideo?"100%"===e.height?parseFloat(u*o/i,10):e.height>=e.width?parseFloat(u/r,10):parseFloat(u*r,10):o,isNaN(d)&&(d=a),e.container.parentNode.length>0&&"body"===e.container.parentNode.tagName.toLowerCase()&&(u=s.default.innerWidth||l.default.documentElement.clientWidth||l.default.body.clientWidth,d=s.default.innerHeight||l.default.documentElement.clientHeight||l.default.body.clientHeight),d&&u){e.container.style.width=u+"px",e.container.style.height=d+"px",e.node.style.width="100%",e.node.style.height="100%",e.isVideo&&e.media.setSize&&e.media.setSize(u,d);for(var c=e.layers.children,p=0,m=c.length;p<m;p++)c[p].style.width="100%",c[p].style.height="100%"}}},{key:"setFillMode",value:function(){var e=this,t=void 0,n=!1;try{s.default.self!==s.default.top?(n=!0,t=s.default.frameElement):t=e.outerContainer}catch(n){t=e.outerContainer}var i=getComputedStyle(t);"none"!==e.node.style.height&&e.node.style.height!==e.height&&(e.node.style.height="auto"),"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width&&(e.node.style.maxWidth="none"),"none"!==e.node.style.maxHeight&&e.node.style.maxHeight!==e.height&&(e.node.style.maxHeight="none"),e.node.currentStyle&&("100%"===e.node.currentStyle.height&&(e.node.currentStyle.height="auto"),"100%"===e.node.currentStyle.maxWidth&&(e.node.currentStyle.maxWidth="none"),"100%"===e.node.currentStyle.maxHeight&&(e.node.currentStyle.maxHeight="none")),n||parseFloat(i.width)||(t.style.width=e.media.offsetWidth+"px"),n||parseFloat(i.height)||(t.style.height=e.media.offsetHeight+"px"),i=getComputedStyle(t);var o=parseFloat(i.width),r=parseFloat(i.height);e.setDimensions("100%","100%");var a=e.container.querySelector(e.options.classPrefix+"poster img");a&&(a.style.display="");for(var l=e.container.querySelectorAll("object, embed, iframe, video"),d=e.height,u=e.width,c=o,f=d*o/u,p=u*r/d,m=r,h=p>o==!1,v=h?Math.floor(c):Math.floor(p),y=h?Math.floor(f):Math.floor(m),g=h?o+"px":v+"px",b=h?y+"px":r+"px",E=0,S=l.length;E<S;E++)l[E].style.height=b,l[E].style.width=g,e.media.setSize&&e.media.setSize(g,b),l[E].style.marginLeft=Math.floor((o-v)/2)+"px",l[E].style.marginTop=0}},{key:"setDimensions",value:function(e,t){var n=this;e=(0,p.isString)(e)&&e.indexOf("%")>-1?e:parseFloat(e)+"px",t=(0,p.isString)(t)&&t.indexOf("%")>-1?t:parseFloat(t)+"px",n.container.style.width=e,n.container.style.height=t;for(var i=n.layers.children,o=0,r=i.length;o<r;o++)i[o].style.width=e,i[o].style.height=t}},{key:"setControlsSize",value:function(){var e=this;if(v.visible(e.container))if(e.rail&&v.visible(e.rail)){for(var t=e.total?getComputedStyle(e.total,null):null,n=t?parseFloat(t.marginLeft)+parseFloat(t.marginRight):0,i=getComputedStyle(e.rail),o=parseFloat(i.marginLeft)+parseFloat(i.marginRight),r=0,a=v.siblings(e.rail,function(t){return t!==e.rail}),s=a.length,l=0;l<s;l++)r+=a[l].offsetWidth;r+=n+(0===n?2*o:o)+1,e.container.style.minWidth=r+"px";var d=parseFloat(e.controls.offsetWidth);e.rail.style.width=(r>d?0:d-r)+"px";var u=(0,p.createEvent)("controlsresize",e.container);e.container.dispatchEvent(u)}else{for(var c=e.controls.children,f=0,m=0,h=c.length;m<h;m++)f+=c[m].offsetWidth;e.container.style.minWidth=f+"px"}}},{key:"addControlElement",value:function(e,t){var n=this;if(void 0!==n.featurePosition[t]){var i=n.controls.children[n.featurePosition[t]-1];i.parentNode.insertBefore(e,i.nextSibling)}else{n.controls.appendChild(e);for(var o=n.controls.children,r=0,a=o.length;r<a;r++)if(e==o[r]){n.featurePosition[t]=r;break}}}},{key:"createIframeLayer",value:function(){var e=this;if(e.isVideo&&null!==e.media.rendererName&&e.media.rendererName.indexOf("iframe")>-1&&!l.default.getElementById(e.media.id+"-iframe-overlay")){var t=l.default.createElement("div"),n=l.default.getElementById(e.media.id+"_"+e.media.rendererName);t.id=e.media.id+"-iframe-overlay",t.className=e.options.classPrefix+"iframe-overlay",t.addEventListener("click",function(t){e.options.clickToPlayPause&&(e.media.paused?e.media.play():e.media.pause(),t.preventDefault(),t.stopPropagation())}),n.parentNode.insertBefore(t,n)}}},{key:"resetSize",value:function(){var e=this;setTimeout(function(){e.setPlayerSize(e.width,e.height),e.setControlsSize()},50)}},{key:"setPoster",value:function(e){var t=this,n=t.container.querySelector("."+t.options.classPrefix+"poster"),i=n.querySelector("img");i||((i=l.default.createElement("img")).className=t.options.classPrefix+"poster-img",i.width="100%",i.height="100%",n.appendChild(i)),i.setAttribute("src",e),n.style.backgroundImage='url("'+e+'")'}},{key:"changeSkin",value:function(e){var t=this;t.container.className=t.options.classPrefix+"container "+e,t.setPlayerSize(t.width,t.height),t.setControlsSize()}},{key:"globalBind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:l.default;if((e=(0,p.splitEvents)(e,n.id)).d)for(var o=e.d.split(" "),r=0,a=o.length;r<a;r++)o[r].split(".").reduce(function(e,n){return i.addEventListener(n,t,!1),n},"");if(e.w)for(var d=e.w.split(" "),u=0,c=d.length;u<c;u++)d[u].split(".").reduce(function(e,n){return s.default.addEventListener(n,t,!1),n},"")}},{key:"globalUnbind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:l.default;if((e=(0,p.splitEvents)(e,n.id)).d)for(var o=e.d.split(" "),r=0,a=o.length;r<a;r++)o[r].split(".").reduce(function(e,n){return i.removeEventListener(n,t,!1),n},"");if(e.w)for(var d=e.d.split(" "),u=0,c=d.length;u<c;u++)d[u].split(".").reduce(function(e,n){return s.default.removeEventListener(n,t,!1),n},"")}},{key:"buildposter",value:function(e,t,n,i){var o=this,r=l.default.createElement("div");r.className=o.options.classPrefix+"poster "+o.options.classPrefix+"layer",n.appendChild(r);var a=e.media.getAttribute("poster");""!==e.options.poster&&(a=e.options.poster),a?o.setPoster(a):r.style.display="none",i.addEventListener("play",function(){r.style.display="none"}),i.addEventListener("playing",function(){r.style.display="none"}),e.options.showPosterWhenEnded&&e.options.autoRewind&&i.addEventListener("ended",function(){r.style.display=""}),i.addEventListener("error",function(){r.style.display="none"}),e.options.showPosterWhenPaused&&i.addEventListener("pause",function(){i.ended||(r.style.display="")})}},{key:"buildoverlays",value:function(e,t,n,i){if(e.isVideo){var o=this,r=l.default.createElement("div"),a=l.default.createElement("div"),s=l.default.createElement("div"),d=t.querySelector("."+o.options.classPrefix+"time-buffering");r.style.display="none",r.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",r.innerHTML='<div class="'+o.options.classPrefix+'overlay-loading"><span class="'+o.options.classPrefix+'overlay-loading-bg-img"></span></div>',n.appendChild(r),a.style.display="none",a.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",a.innerHTML='<div class="'+o.options.classPrefix+'overlay-error"></div>',n.appendChild(a),s.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer "+o.options.classPrefix+"overlay-play",s.innerHTML='<div class="'+o.options.classPrefix+'overlay-button" role="button" tabindex="0"aria-label="'+c.default.t("mejs.play")+'" aria-pressed="false"></div>',s.addEventListener("click",function(){if(o.options.clickToPlayPause){var e=o.container.querySelector("."+o.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");i.paused?i.play():i.pause(),e.setAttribute("aria-pressed",!!t)}}),s.addEventListener("keydown",function(e){var t=e.keyCode||e.which||0;if(13===t||f.IS_FIREFOX&&32===t){var n=(0,p.createEvent)("click",s);return s.dispatchEvent(n),!1}}),n.appendChild(s),null!==o.media.rendererName&&(/(youtube|facebook)/i.test(o.media.rendererName)&&!e.media.originalNode.getAttribute("poster")&&!e.options.poster||f.IS_STOCK_ANDROID)&&(s.style.display="none"),i.addEventListener("play",function(){s.style.display="none",r.style.display="none",d&&(d.style.display="none"),a.style.display="none"}),i.addEventListener("playing",function(){s.style.display="none",r.style.display="none",d&&(d.style.display="none"),a.style.display="none"}),i.addEventListener("seeking",function(){s.style.display="none",r.style.display="",d&&(d.style.display="")}),i.addEventListener("seeked",function(){s.style.display=i.paused&&!f.IS_STOCK_ANDROID?"":"none",r.style.display="none",d&&(d.style.display="")}),i.addEventListener("pause",function(){r.style.display="none",f.IS_STOCK_ANDROID||(s.style.display=""),d&&(d.style.display="none")}),i.addEventListener("waiting",function(){r.style.display="",d&&(d.style.display="")}),i.addEventListener("loadeddata",function(){r.style.display="",d&&(d.style.display=""),f.IS_ANDROID&&(i.canplayTimeout=setTimeout(function(){if(l.default.createEvent){var e=l.default.createEvent("HTMLEvents");return e.initEvent("canplay",!0,!0),i.dispatchEvent(e)}},300))}),i.addEventListener("canplay",function(){r.style.display="none",d&&(d.style.display="none"),clearTimeout(i.canplayTimeout)}),i.addEventListener("error",function(e){o._handleError(e),r.style.display="none",s.style.display="none",d&&(d.style.display="none"),e.message&&(a.style.display="block",a.querySelector("."+o.options.classPrefix+"overlay-error").innerHTML=e.message)}),i.addEventListener("keydown",function(t){o.onkeydown(e,i,t)})}}},{key:"buildkeyboard",value:function(e,t,n,i){var o=this;o.container.addEventListener("keydown",function(){o.keyboardAction=!0}),o.globalBind("keydown",function(t){var n=l.default.activeElement.closest("."+o.options.classPrefix+"container"),r=o.media.closest("."+o.options.classPrefix+"container");return o.hasFocus=!(!n||!r||n.id!==r.id),o.onkeydown(e,i,t)}),o.globalBind("click",function(e){o.hasFocus=!!e.target.closest("."+o.options.classPrefix+"container")})}},{key:"onkeydown",value:function(e,t,n){if(e.hasFocus&&e.options.enableKeyboard)for(var i=0,o=e.options.keyActions.length;i<o;i++)for(var r=e.options.keyActions[i],a=0,s=r.keys.length;a<s;a++)n.keyCode===r.keys[a]&&(r.action(e,t,n.keyCode,n),n.preventDefault(),n.stopPropagation());return!0}},{key:"play",value:function(){var e=this;e.media.getCurrentTime()<=0&&e.load(),e.media.play()}},{key:"pause",value:function(){try{this.media.pause()}catch(e){}}},{key:"load",value:function(){var e=this;e.isLoaded||e.media.load(),e.isLoaded=!0}},{key:"setMuted",value:function(e){this.media.setMuted(e)}},{key:"setCurrentTime",value:function(e){this.media.setCurrentTime(e)}},{key:"getCurrentTime",value:function(){return this.media.currentTime}},{key:"getDuration",value:function(){return this.media.duration}},{key:"setVolume",value:function(e){this.media.setVolume(e)}},{key:"getVolume",value:function(){return this.media.volume}},{key:"setSrc",value:function(e){var t=this,n=l.default.getElementById(t.media.id+"-iframe-overlay");n&&n.remove(),t.media.setSrc(e),t.createIframeLayer()}},{key:"remove",value:function(){var e=this,t=e.media.rendererName;e.media.paused||e.media.pause();var n=e.media.getSrc();e.media.setSrc("");for(var i in e.options.features){var o=e.options.features[i];if(e["clean"+o])try{e["clean"+o](e)}catch(e){console.error("error cleaning "+o,e)}}var a=e.node.getAttribute("width"),s=e.node.getAttribute("height");a?-1===a.indexOf("%")&&(a+="px"):a="auto",s?-1===s.indexOf("%")&&(s+="px"):s="auto",e.node.style.width=a,e.node.style.height=s,e.isDynamic?e.container.parentNode.insertBefore(e.node,e.container):function(){e.node.setAttribute("controls",!0),e.node.setAttribute("id",e.node.getAttribute("id").replace("_"+t,"").replace("_from_mejs","")),delete e.node.autoplay,""!==e.media.canPlayType((0,h.getTypeFromFile)(n))&&e.node.setAttribute("src",n),~t.indexOf("iframe")&&l.default.getElementById(e.media.id+"-iframe-overlay").remove();var i=e.node.cloneNode();if(i.style.display="",e.container.parentNode.insertBefore(i,e.container),e.node.remove(),e.mediaFiles)for(var o=0,r=e.mediaFiles.length;o<r;o++){var a=l.default.createElement("source");a.setAttribute("src",e.mediaFiles[o].src),a.setAttribute("type",e.mediaFiles[o].type),i.appendChild(a)}if(e.trackFiles)for(var s=0,d=e.trackFiles.length;s<d;s++)!function(t,n){var o=e.trackFiles[t],r=l.default.createElement("track");r.kind=o.kind,r.label=o.label,r.srclang=o.srclang,r.src=o.src,i.appendChild(r),r.addEventListener("load",function(){this.mode="showing",i.textTracks[t].mode="showing"})}(s);delete e.node,delete e.mediaFiles,delete e.trackFiles}(),"function"==typeof e.media.renderer.destroy&&e.media.renderer.destroy(),delete d.default.players[e.id],"object"===r(e.container)&&(e.container.parentNode.querySelector("."+e.options.classPrefix+"offscreen").remove(),e.container.remove()),e.globalUnbind(),delete e.media.player}}]),e}();s.default.MediaElementPlayer=g,n.default=g,function(e){void 0!==e&&(e.fn.mediaelementplayer=function(t){return!1===t?this.each(function(){var t=e(this).data("mediaelementplayer");t&&t.remove(),e(this).removeData("mediaelementplayer")}):this.each(function(){e(this).data("mediaelementplayer",new g(this,t))}),this},e(l.default).ready(function(){e("."+y.classPrefix+"player").mediaelementplayer()}))}(d.default.$)},{2:2,24:24,25:25,26:26,27:27,29:29,3:3,5:5,6:6,7:7}],18:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=i(e(3)),a=i(e(7)),s=e(8),l=e(26),d=e(27),u=e(24),c=e(25),f={promise:null,load:function(e){return"undefined"!=typeof dashjs?f.promise=new Promise(function(e){e()}).then(function(){f._createPlayer(e)}):f.promise||(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",f.promise=f.promise||(0,c.loadScript)(e.options.path),f.promise.then(function(){f._createPlayer(e)})),f.promise},_createPlayer:function(e){var t=dashjs.MediaPlayer().create();r.default["__ready__"+e.id](t)}},p={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(e){return u.HAS_MSE&&["application/dash+xml"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,d=e.id+"_"+t.prefix,u=i.autoplay,c=null,p=null;c=i.cloneNode(!0),t=Object.assign(t,e.options);for(var m=a.default.html5media.properties,h=0,v=m.length;h<v;h++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);c["get"+n]=function(){return null!==p?c[e]:null},c["set"+n]=function(n){-1===a.default.html5media.readOnlyProperties.indexOf(e)&&("src"===e?"string"==typeof n?(c[e]=n,null!==p&&(p.attachSource(n),u&&p.play())):n&&"object"===(void 0===n?"undefined":o(n))&&n.src&&(c[e]=n.src,null!==p&&(n&&"object"===(void 0===n?"undefined":o(n))&&"object"===o(n.drm)&&(p.setProtectionData(n.drm),(0,l.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(n.src),u&&p.play())):c[e]=n)}}(m[h]);if(r.default["__ready__"+d]=function(n){e.dashPlayer=p=n;for(var i=a.default.html5media.events.concat(["click","mouseover","mouseout"]),r=dashjs.MediaPlayer.events,s=0,d=i.length;s<d;s++)!function(n){"loadedmetadata"===n&&(p.getDebug().setLogToBrowserConsole(t.dash.debug),p.initialize(),p.setScheduleWhilePaused(!1),p.setFastSwitchEnabled(!0),p.attachView(c),p.setAutoPlay(!1),"object"!==o(t.dash.drm)||a.default.Utils.isObjectEmpty(t.dash.drm)||(p.setProtectionData(t.dash.drm),(0,l.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(c.getSrc())),c.addEventListener(n,function(t){var n=(0,l.createEvent)(t.type,e);e.dispatchEvent(n)})}(i[s]);var u=function(t){var n=(0,l.createEvent)(t.type,c);n.data=t,e.dispatchEvent(n),"error"===t.type.toLowerCase()&&console.error(t)};for(var f in r)r.hasOwnProperty(f)&&p.on(r[f],u)},n&&n.length>0)for(var y=0,g=n.length;y<g;y++)if(s.renderer.renderers[t.prefix].canPlayType(n[y].type)){c.setAttribute("src",n[y].src),void 0!==n[y].drm&&(t.dash.drm=n[y].drm);break}c.setAttribute("id",d),i.parentNode.insertBefore(c,i),i.autoplay=!1,i.style.display="none",c.setSize=function(e,t){return c.style.width=e+"px",c.style.height=t+"px",c},c.hide=function(){return c.pause(),c.style.display="none",c},c.show=function(){return c.style.display="",c},c.destroy=function(){null!==p&&p.reset()};var b=(0,l.createEvent)("rendererready",c);return e.dispatchEvent(b),e.promises.push(f.load({options:t.dash,id:d})),c}};d.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".mpd")?"application/dash+xml":null}),s.renderer.add(p)},{24:24,25:25,26:26,27:27,3:3,7:7,8:8}],19:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.PluginDetector=void 0;var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=i(e(3)),a=i(e(2)),s=i(e(7)),l=i(e(5)),d=e(8),u=e(26),c=e(24),f=e(27),p=n.PluginDetector={plugins:[],hasPluginVersion:function(e,t){var n=p.plugins[e];return t[1]=t[1]||0,t[2]=t[2]||0,n[0]>t[0]||n[0]===t[0]&&n[1]>t[1]||n[0]===t[0]&&n[1]===t[1]&&n[2]>=t[2]},addPlugin:function(e,t,n,i,o){p.plugins[e]=p.detectPlugin(t,n,i,o)},detectPlugin:function(e,t,n,i){var a=[0,0,0],s=void 0,l=void 0;if(null!==c.NAV.plugins&&void 0!==c.NAV.plugins&&"object"===o(c.NAV.plugins[e])){if((s=c.NAV.plugins[e].description)&&(void 0===c.NAV.mimeTypes||!c.NAV.mimeTypes[t]||c.NAV.mimeTypes[t].enabledPlugin))for(var d=0,u=(a=s.replace(e,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".")).length;d<u;d++)a[d]=parseInt(a[d].match(/\d+/),10)}else if(void 0!==r.default.ActiveXObject)try{(l=new ActiveXObject(n))&&(a=i(l))}catch(e){}return a}};p.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",function(e){var t=[],n=e.GetVariable("$version");return n&&(n=n.split(" ")[1].split(","),t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]),t});var m={create:function(e,t,n){var i={};i.options=t,i.id=e.id+"_"+i.options.prefix,i.mediaElement=e,i.flashState={},i.flashApi=null,i.flashApiStack=[];for(var o=s.default.html5media.properties,p=0,m=o.length;p<m;p++)!function(e){i.flashState[e]=null;var t=""+e.substring(0,1).toUpperCase()+e.substring(1);i["get"+t]=function(){if(null!==i.flashApi){if("function"==typeof i.flashApi["get_"+e]){var t=i.flashApi["get_"+e]();return"buffered"===e?{start:function(){return 0},end:function(){return t},length:1}:t}return null}return null},i["set"+t]=function(t){if("src"===e&&(t=(0,f.absolutizeUrl)(t)),null!==i.flashApi&&void 0!==i.flashApi["set_"+e])try{i.flashApi["set_"+e](t)}catch(e){}else i.flashApiStack.push({type:"set",propName:e,value:t})}}(o[p]);var h=s.default.html5media.methods;h.push("stop");for(var v=0,y=h.length;v<y;v++)!function(e){i[e]=function(){if(null!==i.flashApi){if(i.flashApi["fire_"+e])try{i.flashApi["fire_"+e]()}catch(e){}}else i.flashApiStack.push({type:"call",methodName:e})}}(h[v]);for(var g=["rendererready"],b=0,E=g.length;b<E;b++){var S=(0,u.createEvent)(g[b],i);e.dispatchEvent(S)}r.default["__ready__"+i.id]=function(){if(i.flashReady=!0,i.flashApi=a.default.getElementById("__"+i.id),i.flashApiStack.length)for(var e=0,t=i.flashApiStack.length;e<t;e++){var n=i.flashApiStack[e];if("set"===n.type){var o=n.propName,r=""+o.substring(0,1).toUpperCase()+o.substring(1);i["set"+r](n.value)}else"call"===n.type&&i[n.methodName]()}},r.default["__event__"+i.id]=function(e,t){var n=(0,u.createEvent)(e,i);n.message=t||"",i.mediaElement.dispatchEvent(n)},i.flashWrapper=a.default.createElement("div"),-1===["always","sameDomain"].indexOf(i.options.shimScriptAccess)&&(i.options.shimScriptAccess="sameDomain");var w=e.originalNode.autoplay,x=["uid="+i.id,"autoplay="+w,"allowScriptAccess="+i.options.shimScriptAccess],P=null!==e.originalNode&&"video"===e.originalNode.tagName.toLowerCase(),T=P?e.originalNode.height:1,C=P?e.originalNode.width:1;e.originalNode.getAttribute("src")&&x.push("src="+e.originalNode.getAttribute("src")),!0===i.options.enablePseudoStreaming&&(x.push("pseudostreamstart="+i.options.pseudoStreamingStartQueryParam),x.push("pseudostreamtype="+i.options.pseudoStreamingType)),e.appendChild(i.flashWrapper),null!==e.originalNode&&(e.originalNode.style.display="none");var _=[];if(c.IS_IE){var k=a.default.createElement("div");i.flashWrapper.appendChild(k),_=['classid="clsid:D27CDB6E-AE6D-11cf-96B8-************"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+i.id+'"','width="'+C+'"','height="'+T+'"'],P||_.push('style="clip: rect(0 0 0 0); position: absolute;"'),k.outerHTML="<object "+_.join(" ")+'><param name="movie" value="'+i.options.pluginPath+i.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+x.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+i.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+l.default.t("mejs.install-flash")+"</div></object>"}else _=['id="__'+i.id+'"','name="__'+i.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+i.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+i.options.pluginPath+i.options.filename+'"','flashvars="'+x.join("&")+'"','width="'+C+'"','height="'+T+'"'],P||_.push('style="clip: rect(0 0 0 0); position: absolute;"'),i.flashWrapper.innerHTML="<embed "+_.join(" ")+">";if(i.flashNode=i.flashWrapper.lastChild,i.hide=function(){P&&(i.flashNode.style.display="none")},i.show=function(){P&&(i.flashNode.style.display="")},i.setSize=function(e,t){i.flashNode.style.width=e+"px",i.flashNode.style.height=t+"px",null!==i.flashApi&&"function"==typeof i.flashApi.fire_setSize&&i.flashApi.fire_setSize(e,t)},i.destroy=function(){i.flashNode.remove()},n&&n.length>0)for(var N=0,A=n.length;N<A;N++)if(d.renderer.renderers[t.prefix].canPlayType(n[N].type)){i.setSrc(n[N].src);break}return i}};if(p.hasPluginVersion("flash",[10,0,0])){f.typeChecks.push(function(e){return e=e.toLowerCase(),e.startsWith("rtmp")?~e.indexOf(".mp3")?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(e)?"audio/ogg":~e.indexOf(".m3u8")?"application/x-mpegURL":~e.indexOf(".mpd")?"application/dash+xml":~e.indexOf(".flv")?"video/flv":null});var h={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte"},canPlayType:function(e){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(e.toLowerCase())},create:m.create};d.renderer.add(h);var v={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(e){return~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:m.create};d.renderer.add(v);var y={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(e){return~["application/dash+xml"].indexOf(e.toLowerCase())},create:m.create};d.renderer.add(y);var g={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(e){return~["audio/mp3"].indexOf(e.toLowerCase())},create:m.create};d.renderer.add(g);var b={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(e){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(e.toLowerCase())},create:m.create};d.renderer.add(b)}},{2:2,24:24,26:26,27:27,3:3,5:5,7:7,8:8}],20:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(7)),a=e(8),s=e(26),l=e(24),d=e(27),u=e(25),c={promise:null,load:function(e){return"undefined"!=typeof flvjs?c.promise=new Promise(function(e){e()}).then(function(){c._createPlayer(e)}):c.promise||(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdnjs.cloudflare.com/ajax/libs/flv.js/1.3.0/flv.min.js",c.promise=c.promise||(0,u.loadScript)(e.options.path),c.promise.then(function(){c._createPlayer(e)})),c.promise},_createPlayer:function(e){flvjs.LoggingControl.enableDebug=e.options.debug,flvjs.LoggingControl.enableVerbose=e.options.debug;var t=flvjs.createPlayer(e.options);return o.default["__ready__"+e.id](t),t}},f={name:"native_flv",options:{prefix:"native_flv",flv:{path:"https://cdnjs.cloudflare.com/ajax/libs/flv.js/1.3.0/flv.min.js",cors:!0,debug:!1}},canPlayType:function(e){return l.HAS_MSE&&["video/x-flv","video/flv"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,l=e.id+"_"+t.prefix,d=null,u=null;d=i.cloneNode(!0),t=Object.assign(t,e.options);for(var f=r.default.html5media.properties,p=0,m=f.length;p<m;p++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);d["get"+n]=function(){return null!==u?d[e]:null},d["set"+n]=function(n){if(-1===r.default.html5media.readOnlyProperties.indexOf(e)&&(d[e]=n,null!==u&&"src"===e)){var i={};i.type="flv",i.url=n,i.cors=t.flv.cors,i.debug=t.flv.debug,i.path=t.flv.path,u.destroy(),(u=c._createPlayer({options:i,id:l})).attachMediaElement(d),u.load()}}}(f[p]);if(o.default["__ready__"+l]=function(t){e.flvPlayer=u=t;for(var n=r.default.html5media.events.concat(["click","mouseover","mouseout"]),i=flvjs.Events,o=0,a=n.length;o<a;o++)!function(t){"loadedmetadata"===t&&(u.unload(),u.detachMediaElement(),u.attachMediaElement(d),u.load()),d.addEventListener(t,function(t){var n=(0,s.createEvent)(t.type,e);e.dispatchEvent(n)})}(n[o]);var l=function(t,n){var i=(0,s.createEvent)(t,d);i.data=n,e.dispatchEvent(i)};for(var c in i)!function(e){i.hasOwnProperty(e)&&u.on(i[e],function(t){l(i[e],t)})}(c)},n&&n.length>0)for(var h=0,v=n.length;h<v;h++)if(a.renderer.renderers[t.prefix].canPlayType(n[h].type)){d.setAttribute("src",n[h].src);break}d.setAttribute("id",l),i.parentNode.insertBefore(d,i),i.autoplay=!1,i.style.display="none";var y={};y.type="flv",y.url=d.src,y.cors=t.flv.cors,y.debug=t.flv.debug,y.path=t.flv.path,d.setSize=function(e,t){return d.style.width=e+"px",d.style.height=t+"px",d},d.hide=function(){return null!==u&&u.pause(),d.style.display="none",d},d.show=function(){return d.style.display="",d},d.destroy=function(){null!==u&&u.destroy()};var g=(0,s.createEvent)("rendererready",d);return e.dispatchEvent(g),e.promises.push(c.load({options:y,id:l})),d}};d.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".flv")?"video/flv":null}),a.renderer.add(f)},{24:24,25:25,26:26,27:27,3:3,7:7,8:8}],21:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(7)),a=e(8),s=e(26),l=e(24),d=e(27),u=e(25),c={promise:null,load:function(e){return"undefined"!=typeof Hls?c.promise=new Promise(function(e){e()}).then(function(){c._createPlayer(e)}):c.promise||(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdnjs.cloudflare.com/ajax/libs/hls.js/0.7.9/hls.min.js",c.promise=c.promise||(0,u.loadScript)(e.options.path),c.promise.then(function(){c._createPlayer(e)})),c.promise},_createPlayer:function(e){var t=new Hls(e.options);return o.default["__ready__"+e.id](t),t}},f={name:"native_hls",options:{prefix:"native_hls",hls:{path:"https://cdnjs.cloudflare.com/ajax/libs/hls.js/0.7.9/hls.min.js",autoStartLoad:!1,debug:!1}},canPlayType:function(e){return l.HAS_MSE&&["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,l=e.id+"_"+t.prefix,d=i.getAttribute("preload"),u=i.autoplay,f=null,p=null;p=i.cloneNode(!0),(t=Object.assign(t,e.options)).hls.autoStartLoad=d&&"none"!==d||u;for(var m=r.default.html5media.properties,h=0,v=m.length;h<v;h++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);p["get"+n]=function(){return null!==f?p[e]:null},p["set"+n]=function(n){-1===r.default.html5media.readOnlyProperties.indexOf(e)&&(p[e]=n,null!==f&&"src"===e&&(f.destroy(),(f=c._createPlayer({options:t.hls,id:l})).loadSource(n),f.attachMedia(p)))}}(m[h]);if(o.default["__ready__"+l]=function(t){e.hlsPlayer=f=t;for(var n=r.default.html5media.events.concat(["click","mouseover","mouseout"]),i=Hls.Events,o=0,a=n.length;o<a;o++)!function(t){if("loadedmetadata"===t){var n=e.originalNode.src;f.detachMedia(),f.loadSource(n),f.attachMedia(p)}p.addEventListener(t,function(t){var n=(0,s.createEvent)(t.type,e);e.dispatchEvent(n)})}(n[o]);var l=void 0,d=void 0,u=function(t,n){var i=(0,s.createEvent)(t,p);if(i.data=n,e.dispatchEvent(i),"hlsError"===t&&(console.warn(t,n),n.fatal))switch(n.type){case"mediaError":var o=(new Date).getTime();!l||o-l>3e3?(l=(new Date).getTime(),f.recoverMediaError()):!d||o-d>3e3?(d=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),f.swapAudioCodec(),f.recoverMediaError()):console.error("Cannot recover, last media error recovery failed");break;case"networkError":console.error("Network error");break;default:f.destroy()}};for(var c in i)i.hasOwnProperty(c)&&f.on(i[c],u)},n&&n.length>0)for(var y=0,g=n.length;y<g;y++)if(a.renderer.renderers[t.prefix].canPlayType(n[y].type)){p.setAttribute("src",n[y].src);break}"auto"===d||u||(p.addEventListener("play",function(){null!==f&&f.startLoad()}),p.addEventListener("pause",function(){null!==f&&f.stopLoad()})),p.setAttribute("id",l),i.parentNode.insertBefore(p,i),i.autoplay=!1,i.style.display="none",p.setSize=function(e,t){return p.style.width=e+"px",p.style.height=t+"px",p},p.hide=function(){return p.pause(),p.style.display="none",p},p.show=function(){return p.style.display="",p},p.destroy=function(){null!==f&&(f.stopLoad(),f.destroy())};var b=(0,s.createEvent)("rendererready",p);return e.dispatchEvent(b),e.promises.push(c.load({options:t.hls,id:l})),p}};d.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null}),a.renderer.add(f)},{24:24,25:25,26:26,27:27,3:3,7:7,8:8}],22:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(2)),a=i(e(7)),s=e(8),l=e(26),d=e(24),u={name:"html5",options:{prefix:"html5"},canPlayType:function(e){var t=r.default.createElement("video");return d.IS_ANDROID&&/\/mp(3|4)$/i.test(e)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())&&d.SUPPORTS_NATIVE_HLS?"yes":t.canPlayType?t.canPlayType(e.toLowerCase()).replace(/no/,""):""},create:function(e,t,n){var i=e.id+"_"+t.prefix,o=null;void 0===e.originalNode||null===e.originalNode?(o=r.default.createElement("audio"),e.appendChild(o)):o=e.originalNode,o.setAttribute("id",i);for(var d=a.default.html5media.properties,u=0,c=d.length;u<c;u++)!function(e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1);o["get"+t]=function(){return o[e]},o["set"+t]=function(t){-1===a.default.html5media.readOnlyProperties.indexOf(e)&&(o[e]=t)}}(d[u]);for(var f=a.default.html5media.events.concat(["click","mouseover","mouseout"]),p=0,m=f.length;p<m;p++)!function(t){o.addEventListener(t,function(t){var n=(0,l.createEvent)(t.type,e);e.dispatchEvent(n)})}(f[p]);if(o.setSize=function(e,t){return o.style.width=e+"px",o.style.height=t+"px",o},o.hide=function(){return o.style.display="none",o},o.show=function(){return o.style.display="",o},n&&n.length>0)for(var h=0,v=n.length;h<v;h++)if(s.renderer.renderers[t.prefix].canPlayType(n[h].type)){o.setAttribute("src",n[h].src);break}var y=(0,l.createEvent)("rendererready",o);return e.dispatchEvent(y),o}};o.default.HtmlMediaElement=a.default.HtmlMediaElement=u,s.renderer.add(u)},{2:2,24:24,26:26,3:3,7:7,8:8}],23:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(3)),r=i(e(2)),a=i(e(7)),s=e(8),l=e(26),d=e(27),u=e(25),c={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(e){c.isLoaded="undefined"!=typeof YT&&YT.loaded,c.isLoaded?c.createIframe(e):(c.loadIframeApi(),c.iframeQueue.push(e))},loadIframeApi:function(){c.isIframeStarted||((0,u.loadScript)("https://www.youtube.com/player_api"),c.isIframeStarted=!0)},iFrameReady:function(){for(c.isLoaded=!0,c.isIframeLoaded=!0;c.iframeQueue.length>0;){var e=c.iframeQueue.pop();c.createIframe(e)}},createIframe:function(e){return new YT.Player(e.containerId,e)},getYouTubeId:function(e){var t="";return e.indexOf("?")>0?""===(t=c.getYouTubeIdFromParam(e))&&(t=c.getYouTubeIdFromUrl(e)):t=c.getYouTubeIdFromUrl(e),t},getYouTubeIdFromParam:function(e){if(void 0===e||null===e||!e.trim().length)return null;for(var t=e.split("?")[1].split("&"),n="",i=0,o=t.length;i<o;i++){var r=t[i].split("=");if("v"===r[0]){n=r[1];break}}return n},getYouTubeIdFromUrl:function(e){return void 0!==e&&null!==e&&e.trim().length?(e=e.split("?")[0]).substring(e.lastIndexOf("/")+1):null},getYouTubeNoCookieUrl:function(e){if(void 0===e||null===e||!e.trim().length||-1===e.indexOf("//www.youtube"))return e;var t=e.split("/");return t[2]=t[2].replace(".com","-nocookie.com"),t.join("/")}},f={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1}},canPlayType:function(e){return~["video/youtube","video/x-youtube"].indexOf(e.toLowerCase())},create:function(e,t,n){var i={},s=[],d=null,u=!0,f=!1,p=null,m=1;i.options=t,i.id=e.id+"_"+t.prefix,i.mediaElement=e;for(var h=a.default.html5media.properties,v=0,y=h.length;v<y;v++)!function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);i["get"+n]=function(){if(null!==d){switch(t){case"currentTime":return d.getCurrentTime();case"duration":return d.getDuration();case"volume":return m=d.getVolume()/100;case"paused":return u;case"ended":return f;case"muted":return d.isMuted();case"buffered":var e=d.getVideoLoadedFraction(),n=d.getDuration();return{start:function(){return 0},end:function(){return e*n},length:1};case"src":return d.getVideoUrl();case"readyState":return 4}return null}return null},i["set"+n]=function(n){if(null!==d)switch(t){case"src":var o="string"==typeof n?n:n[0].src,r=c.getYouTubeId(o);e.originalNode.autoplay?d.loadVideoById(r):d.cueVideoById(r);break;case"currentTime":d.seekTo(n);break;case"muted":n?d.mute():d.unMute(),setTimeout(function(){var t=(0,l.createEvent)("volumechange",i);e.dispatchEvent(t)},50);break;case"volume":m=n,d.setVolume(100*n),setTimeout(function(){var t=(0,l.createEvent)("volumechange",i);e.dispatchEvent(t)},50);break;case"readyState":var a=(0,l.createEvent)("canplay",i);e.dispatchEvent(a)}else s.push({type:"set",propName:t,value:n})}}(h[v]);for(var g=a.default.html5media.methods,b=0,E=g.length;b<E;b++)!function(e){i[e]=function(){if(null!==d)switch(e){case"play":return u=!1,d.playVideo();case"pause":return u=!0,d.pauseVideo();case"load":return null}else s.push({type:"call",methodName:e})}}(g[b]);var S=r.default.createElement("div");S.id=i.id,i.options.youtube.nocookie&&e.originalNode.setAttribute("src",c.getYouTubeNoCookieUrl(n[0].src)),e.originalNode.parentNode.insertBefore(S,e.originalNode),e.originalNode.style.display="none";var w="audio"===e.originalNode.tagName.toLowerCase(),x=w?"1":e.originalNode.height,P=w?"1":e.originalNode.width,T=c.getYouTubeId(n[0].src),C={id:i.id,containerId:S.id,videoId:T,height:x,width:P,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,playsinline:0,start:0,end:0,iv_load_policy:3},i.options.youtube),origin:o.default.location.host,events:{onReady:function(t){if(e.youTubeApi=d=t.target,e.youTubeState={paused:!0,ended:!1},s.length)for(var n=0,o=s.length;n<o;n++){var r=s[n];if("set"===r.type){var a=r.propName,u=""+a.substring(0,1).toUpperCase()+a.substring(1);i["set"+u](r.value)}else"call"===r.type&&i[r.methodName]()}p=d.getIframe(),e.originalNode.getAttribute("muted")&&d.mute();for(var c=["mouseover","mouseout"],f=function(t){var n=(0,l.createEvent)(t.type,i);e.dispatchEvent(n)},m=0,h=c.length;m<h;m++)p.addEventListener(c[m],f,!1);for(var v=["rendererready","loadedmetadata","loadeddata","canplay"],y=0,g=v.length;y<g;y++){var b=(0,l.createEvent)(v[y],i);e.dispatchEvent(b)}},onStateChange:function(t){var n=[];switch(t.data){case-1:n=["loadedmetadata"],u=!0,f=!1;break;case 0:n=["ended"],u=!1,f=!i.options.youtube.loop,i.options.youtube.loop||i.stopInterval();break;case 1:n=["play","playing"],u=!1,f=!1,i.startInterval();break;case 2:n=["pause"],u=!0,f=!1,i.stopInterval();break;case 3:n=["progress"],f=!1;break;case 5:n=["loadeddata","loadedmetadata","canplay"],u=!0,f=!1}for(var o=0,r=n.length;o<r;o++){var a=(0,l.createEvent)(n[o],i);e.dispatchEvent(a)}},onError:function(t){var n=(0,l.createEvent)("error",i);n.data=t.data,e.dispatchEvent(n)}}};return w&&(C.playerVars.playsinline=1),e.originalNode.autoplay&&(C.playerVars.autoplay=1),e.originalNode.loop&&(C.playerVars.loop=1),c.enqueueIframe(C),i.onEvent=function(t,n,i){null!==i&&void 0!==i&&(e.youTubeState=i)},i.setSize=function(e,t){null!==d&&d.setSize(e,t)},i.hide=function(){i.stopInterval(),i.pause(),p&&(p.style.display="none")},i.show=function(){p&&(p.style.display="")},i.destroy=function(){d.destroy()},i.interval=null,i.startInterval=function(){i.interval=setInterval(function(){var t=(0,l.createEvent)("timeupdate",i);e.dispatchEvent(t)},250)},i.stopInterval=function(){i.interval&&clearInterval(i.interval)},i}};o.default.onYouTubePlayerAPIReady=function(){c.iFrameReady()},d.typeChecks.push(function(e){return/\/\/(www\.youtube|youtu\.?be)/i.test(e)?"video/x-youtube":null}),s.renderer.add(f)},{2:2,25:25,26:26,27:27,3:3,7:7,8:8}],24:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.cancelFullScreen=n.requestFullScreen=n.isFullScreen=n.FULLSCREEN_EVENT_NAME=n.HAS_NATIVE_FULLSCREEN_ENABLED=n.HAS_TRUE_NATIVE_FULLSCREEN=n.HAS_IOS_FULLSCREEN=n.HAS_MS_NATIVE_FULLSCREEN=n.HAS_MOZ_NATIVE_FULLSCREEN=n.HAS_WEBKIT_NATIVE_FULLSCREEN=n.HAS_NATIVE_FULLSCREEN=n.SUPPORTS_NATIVE_HLS=n.SUPPORT_POINTER_EVENTS=n.HAS_MSE=n.IS_STOCK_ANDROID=n.IS_SAFARI=n.IS_FIREFOX=n.IS_CHROME=n.IS_EDGE=n.IS_IE=n.IS_ANDROID=n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=n.UA=n.NAV=void 0;for(var o=i(e(3)),r=i(e(2)),a=i(e(7)),s=n.NAV=o.default.navigator,l=n.UA=s.userAgent.toLowerCase(),d=n.IS_IPAD=/ipad/i.test(l)&&!o.default.MSStream,u=n.IS_IPHONE=/iphone/i.test(l)&&!o.default.MSStream,c=n.IS_IPOD=/ipod/i.test(l)&&!o.default.MSStream,f=(n.IS_IOS=/ipad|iphone|ipod/i.test(l)&&!o.default.MSStream,n.IS_ANDROID=/android/i.test(l)),p=n.IS_IE=/(trident|microsoft)/i.test(s.appName),m=(n.IS_EDGE="msLaunchUri"in s&&!("documentMode"in r.default)),h=n.IS_CHROME=/chrome/i.test(l),v=n.IS_FIREFOX=/firefox/i.test(l),y=n.IS_SAFARI=/safari/i.test(l)&&!h,g=n.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(l),b=(n.HAS_MSE="MediaSource"in o.default),E=(n.SUPPORT_POINTER_EVENTS=function(){var e=r.default.createElement("x"),t=r.default.documentElement,n=o.default.getComputedStyle;if(!("pointerEvents"in e.style))return!1;e.style.pointerEvents="auto",e.style.pointerEvents="x",t.appendChild(e);var i=n&&"auto"===n(e,"").pointerEvents;return e.remove(),!!i}()),S=["source","track","audio","video"],w=void 0,x=0,P=S.length;x<P;x++)w=r.default.createElement(S[x]);var T=n.SUPPORTS_NATIVE_HLS=y||f&&(h||g)||p&&/edge/i.test(l),C=void 0!==w.webkitEnterFullscreen,_=void 0!==w.requestFullscreen;C&&/mac os x 10_5/i.test(l)&&(_=!1,C=!1);var k=void 0!==w.webkitRequestFullScreen,N=void 0!==w.mozRequestFullScreen,A=void 0!==w.msRequestFullscreen,L=k||N||A,F=L,j="",I=void 0,M=void 0,O=void 0;N?F=r.default.mozFullScreenEnabled:A&&(F=r.default.msFullscreenEnabled),h&&(C=!1),L&&(k?j="webkitfullscreenchange":N?j="mozfullscreenchange":A&&(j="MSFullscreenChange"),n.isFullScreen=I=function(){return N?r.default.mozFullScreen:k?r.default.webkitIsFullScreen:A?null!==r.default.msFullscreenElement:void 0},n.requestFullScreen=M=function(e){k?e.webkitRequestFullScreen():N?e.mozRequestFullScreen():A&&e.msRequestFullscreen()},n.cancelFullScreen=O=function(){k?r.default.webkitCancelFullScreen():N?r.default.mozCancelFullScreen():A&&r.default.msExitFullscreen()});var H=n.HAS_NATIVE_FULLSCREEN=_,D=n.HAS_WEBKIT_NATIVE_FULLSCREEN=k,R=n.HAS_MOZ_NATIVE_FULLSCREEN=N,q=n.HAS_MS_NATIVE_FULLSCREEN=A,U=n.HAS_IOS_FULLSCREEN=C,V=n.HAS_TRUE_NATIVE_FULLSCREEN=L,B=n.HAS_NATIVE_FULLSCREEN_ENABLED=F,z=n.FULLSCREEN_EVENT_NAME=j;n.isFullScreen=I,n.requestFullScreen=M,n.cancelFullScreen=O,a.default.Features=a.default.Features||{},a.default.Features.isiPad=d,a.default.Features.isiPod=c,a.default.Features.isiPhone=u,a.default.Features.isiOS=a.default.Features.isiPhone||a.default.Features.isiPad,a.default.Features.isAndroid=f,a.default.Features.isIE=p,a.default.Features.isEdge=m,a.default.Features.isChrome=h,a.default.Features.isFirefox=v,a.default.Features.isSafari=y,a.default.Features.isStockAndroid=g,a.default.Features.hasMSE=b,a.default.Features.supportsNativeHLS=T,a.default.Features.supportsPointerEvents=E,a.default.Features.hasiOSFullScreen=U,a.default.Features.hasNativeFullscreen=H,a.default.Features.hasWebkitNativeFullScreen=D,a.default.Features.hasMozNativeFullScreen=R,a.default.Features.hasMsNativeFullScreen=q,a.default.Features.hasTrueNativeFullScreen=V,a.default.Features.nativeFullScreenEnabled=B,a.default.Features.fullScreenEventName=z,a.default.Features.isFullScreen=I,a.default.Features.requestFullScreen=M,a.default.Features.cancelFullScreen=O},{2:2,3:3,7:7}],25:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function o(e){return new Promise(function(t,n){var i=p.default.createElement("script");i.src=e,i.async=!0,i.onload=function(){i.remove(),t()},i.onerror=function(){i.remove(),n()},p.default.head.appendChild(i)})}function r(e){var t=e.getBoundingClientRect(),n=f.default.pageXOffset||p.default.documentElement.scrollLeft,i=f.default.pageYOffset||p.default.documentElement.scrollTop;return{top:t.top+i,left:t.left+n}}function a(e,t){g(e,t)?E(e,t):b(e,t)}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=1);var i=null;f.default.requestAnimationFrame(function o(r){var a=r-(i=i||r),s=parseFloat(1-a/t,2);e.style.opacity=s<0?0:s,a>t?n&&"function"==typeof n&&n():f.default.requestAnimationFrame(o)})}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=0);var i=null;f.default.requestAnimationFrame(function o(r){var a=r-(i=i||r),s=parseFloat(a/t,2);e.style.opacity=s>1?1:s,a>t?n&&"function"==typeof n&&n():f.default.requestAnimationFrame(o)})}function d(e,t){var n=[];e=e.parentNode.firstChild;do{t&&!t(e)||n.push(e)}while(e=e.nextSibling);return n}function u(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}function c(e,t,n,i){var o=f.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),r="application/x-www-form-urlencoded; charset=UTF-8",a=!1,s="*/".concat("*");switch(t){case"text":r="text/plain";break;case"json":r="application/json, text/javascript";break;case"html":r="text/html";break;case"xml":r="application/xml, text/xml"}"application/x-www-form-urlencoded"!==r&&(s=r+", */*; q=0.01"),o&&(o.open("GET",e,!0),o.setRequestHeader("Accept",s),o.onreadystatechange=function(){if(!a&&4===o.readyState)if(200===o.status){a=!0;var e=void 0;switch(t){case"json":e=JSON.parse(o.responseText);break;case"xml":e=o.responseXML;break;default:e=o.responseText}n(e)}else"function"==typeof i&&i(o.status)},o.send())}Object.defineProperty(n,"__esModule",{value:!0}),n.removeClass=n.addClass=n.hasClass=void 0,n.loadScript=o,n.offset=r,n.toggleClass=a,n.fadeOut=s,n.fadeIn=l,n.siblings=d,n.visible=u,n.ajax=c;var f=i(e(3)),p=i(e(2)),m=i(e(7)),h=void 0,v=void 0,y=void 0;"classList"in p.default.documentElement?(h=function(e,t){return void 0!==e.classList&&e.classList.contains(t)},v=function(e,t){return e.classList.add(t)},y=function(e,t){return e.classList.remove(t)}):(h=function(e,t){return new RegExp("\\b"+t+"\\b").test(e.className)},v=function(e,t){g(e,t)||(e.className+=" "+t)},y=function(e,t){e.className=e.className.replace(new RegExp("\\b"+t+"\\b","g"),"")});var g=n.hasClass=h,b=n.addClass=v,E=n.removeClass=y;m.default.Utils=m.default.Utils||{},m.default.Utils.offset=r,m.default.Utils.hasClass=g,m.default.Utils.addClass=b,m.default.Utils.removeClass=E,m.default.Utils.toggleClass=a,m.default.Utils.fadeIn=l,m.default.Utils.fadeOut=s,m.default.Utils.siblings=d,m.default.Utils.visible=u,m.default.Utils.ajax=c,m.default.Utils.loadScript=o},{2:2,3:3,7:7}],26:[function(e,t,n){"use strict";function i(e){if("string"!=typeof e)throw new Error("Argument passed must be a string");var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,function(e){return t[e]})}function o(e,t){var n=this,i=arguments,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"!=typeof e)throw new Error("First argument must be a function");if("number"!=typeof t)throw new Error("Second argument must be a numeric value");var r=void 0;return function(){var a=n,s=i,l=function(){r=null,o||e.apply(a,s)},d=o&&!r;clearTimeout(r),r=setTimeout(l,t),d&&e.apply(a,s)}}function r(e){return Object.getOwnPropertyNames(e).length<=0}function a(e,t){var n=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,i={d:[],w:[]};return(e||"").split(" ").forEach(function(e){var o=e+(t?"."+t:"");o.startsWith(".")?(i.d.push(o),i.w.push(o)):i[n.test(e)?"w":"d"].push(o)}),i.d=i.d.join(" "),i.w=i.w.join(" "),i}function s(e,t){if("string"!=typeof e)throw new Error("Event name must be a string");var n=e.match(/([a-z]+\.([a-z]+))/i),i={target:t};return null!==n&&(e=n[1],i.namespace=n[2]),new window.CustomEvent(e,{detail:i})}function l(e,t){return!!(e&&t&&2&e.compareDocumentPosition(t))}function d(e){return"string"==typeof e}Object.defineProperty(n,"__esModule",{value:!0}),n.escapeHTML=i,n.debounce=o,n.isObjectEmpty=r,n.splitEvents=a,n.createEvent=s,n.isNodeAfter=l,n.isString=d;var u=function(e){return e&&e.__esModule?e:{default:e}}(e(7));u.default.Utils=u.default.Utils||{},u.default.Utils.escapeHTML=i,u.default.Utils.debounce=o,u.default.Utils.isObjectEmpty=r,u.default.Utils.splitEvents=a,u.default.Utils.createEvent=s,u.default.Utils.isNodeAfter=l,u.default.Utils.isString=d},{7:7}],27:[function(e,t,n){"use strict";function i(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=document.createElement("div");return t.innerHTML='<a href="'+(0,u.escapeHTML)(e)+'">x</a>',t.firstChild.href}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&!t?a(e):r(t)}function r(e){if("string"!=typeof e)throw new Error("`type` argument must be a string");return e&&e.indexOf(";")>-1?e.substr(0,e.indexOf(";")):e}function a(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");for(var t=0,n=c.length;t<n;t++){var i=c[t](e);if(i)return i}var o=l(s(e)),r="video/mp4";return o&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg","mov"].indexOf(o)?r="video/"+o:~["mp3","oga","wav","mid","midi"].indexOf(o)&&(r="audio/"+o)),r}function s(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=e.split("?")[0].split("\\").pop().split("/").pop();return~t.indexOf(".")?t.substring(t.lastIndexOf(".")+1):""}function l(e){if("string"!=typeof e)throw new Error("`extension` argument must be a string");switch(e){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return e}}Object.defineProperty(n,"__esModule",{value:!0}),n.typeChecks=void 0,n.absolutizeUrl=i,n.formatType=o,n.getMimeFromType=r,n.getTypeFromFile=a,n.getExtension=s,n.normalizeExtension=l;var d=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),u=e(26),c=n.typeChecks=[];d.default.Utils=d.default.Utils||{},d.default.Utils.typeChecks=c,d.default.Utils.absolutizeUrl=i,d.default.Utils.formatType=o,d.default.Utils.getMimeFromType=r,d.default.Utils.getTypeFromFile=a,d.default.Utils.getExtension=s,d.default.Utils.normalizeExtension=l},{26:26,7:7}],28:[function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var o=i(e(2)),r=i(e(4));!function(e){e.forEach(function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})})}([Element.prototype,CharacterData.prototype,DocumentType.prototype]),function(){function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=o.default.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}if("function"==typeof window.CustomEvent)return!1;e.prototype=window.Event.prototype,window.CustomEvent=e}(),"function"!=typeof Object.assign&&(Object.assign=function(e){if(null===e||void 0===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,i=arguments.length;n<i;n++){var o=arguments[n];if(null!==o)for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(t[r]=o[r])}return t}),String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length-1;--n>=0&&t.item(n)!==this;);return n>-1}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=void 0,i=this;do{for(n=t.length;--n>=0&&t.item(n)!==i;);}while(n<0&&(i=i.parentElement));return i}),function(){for(var e=0,t=["ms","moz","webkit","o"],n=0;n<t.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[t[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[n]+"CancelAnimationFrame"]||window[t[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t){var n=(new Date).getTime(),i=Math.max(0,16-(n-e)),o=window.setTimeout(function(){t(n+i)},i);return e=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),/firefox/i.test(navigator.userAgent)&&(window.mediaElementJsOldGetComputedStyle=window.getComputedStyle,window.getComputedStyle=function(e,t){var n=window.mediaElementJsOldGetComputedStyle(e,t);return null===n?{getPropertyValue:function(){}}:n}),window.Promise||(window.Promise=r.default),function(e){e&&e.prototype&&null===e.prototype.children&&Object.defineProperty(e.prototype,"children",{get:function(){for(var e=0,t=void 0,n=this.childNodes,i=[];t=n[e++];)1===t.nodeType&&i.push(t);return i}})}(window.Node||window.Element)},{2:2,4:4}],29:[function(e,t,n){"use strict";function i(){return!((arguments.length>0&&void 0!==arguments[0]?arguments[0]:25)%1==0)}function o(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:25,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;e=!e||"number"!=typeof e||e<0?0:e;var a=Math.round(.066666*o),s=Math.round(o),l=24*Math.round(3600*o),d=Math.round(600*o),u=i(o)?";":":",c=void 0,f=void 0,p=void 0,m=void 0,h=Math.round(e*o);if(i(o)){h<0&&(h=l+h);var v=(h%=l)%d;h+=9*a*Math.floor(h/d),v>a&&(h+=a*Math.floor((v-a)/Math.round(60*s-a)));var y=Math.floor(h/s);c=Math.floor(Math.floor(y/60)/60),f=Math.floor(y/60)%60,p=n?y%60:(h/s%60).toFixed(r)}else c=Math.floor(e/3600)%24,f=Math.floor(e/60)%60,p=n?Math.floor(e%60):(e%60).toFixed(r);c=c<=0?0:c,f=f<=0?0:f,p=p<=0?0:p;var g=t||c>0?(c<10?"0"+c:c)+":":"";return g+=(f<10?"0"+f:f)+":",g+=""+(p<10?"0"+p:p),n&&(g+=(m=(m=(h%s).toFixed(0))<=0?0:m)<10?u+"0"+m:""+u+m),g}function r(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25;if("string"!=typeof e)throw new TypeError("Time must be a string");if(e.indexOf(";")>0&&(e=e.replace(";",":")),!/\d{2}(\:\d{2}){0,3}/i.test(e))throw new TypeError("Time code must have the format `00:00:00`");var n=e.split(":"),o=void 0,r=0,a=0,s=0,l=0,d=0,u=Math.round(.066666*t),c=Math.round(t),f=3600*c,p=60*c;switch(n.length){default:case 1:s=parseInt(n[0],10);break;case 2:a=parseInt(n[0],10),s=parseInt(n[1],10);break;case 3:r=parseInt(n[0],10),a=parseInt(n[1],10),s=parseInt(n[2],10);break;case 4:r=parseInt(n[0],10),a=parseInt(n[1],10),s=parseInt(n[2],10),l=parseInt(n[3],10)}return o=i(t)?f*r+p*a+c*s+l-u*((d=60*r+a)-Math.floor(d/10)):(f*r+p*a+t*s+l)/t,parseFloat(o.toFixed(3))}function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:25;e=!e||"number"!=typeof e||e<0?0:e;for(var i=Math.floor(e/3600)%24,o=Math.floor(e/60)%60,r=Math.floor(e%60),a=[[Math.floor((e%1*n).toFixed(3)),"f"],[r,"s"],[o,"m"],[i,"h"]],s=t.timeFormat,l=s[1]===s[0],d=l?2:1,u=s.length<d?s[d]:":",c=s[0],f=!1,p=0,m=a.length;p<m;p++)if(~s.indexOf(a[p][1]))f=!0;else if(f){for(var h=!1,v=p;v<m;v++)if(a[v][0]>0){h=!0;break}if(!h)break;l||(s=c+s),s=a[p][1]+u+s,l&&(s=a[p][1]+s),c=a[p][1]}t.currentTimeFormat=s}function s(e){if("string"!=typeof e)throw new TypeError("Argument must be a string value");for(var t=~(e=e.replace(",",".")).indexOf(".")?e.split(".")[1].length:0,n=0,i=1,o=0,r=(e=e.split(":").reverse()).length;o<r;o++)i=1,o>0&&(i=Math.pow(60,o)),n+=Number(e[o])*i;return Number(n.toFixed(t))}Object.defineProperty(n,"__esModule",{value:!0}),n.isDropFrame=i,n.secondsToTimeCode=o,n.timeCodeToSeconds=r,n.calculateTimeFormat=a,n.convertSMPTEtoSeconds=s;var l=function(e){return e&&e.__esModule?e:{default:e}}(e(7));l.default.Utils=l.default.Utils||{},l.default.Utils.secondsToTimeCode=o,l.default.Utils.timeCodeToSeconds=r,l.default.Utils.calculateTimeFormat=a,l.default.Utils.convertSMPTEtoSeconds=s},{7:7}]},{},[28,6,5,15,22,19,18,20,21,23,16,17,9,10,11,12,13,14]);