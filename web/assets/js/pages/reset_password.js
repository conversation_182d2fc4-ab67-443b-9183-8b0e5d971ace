"use strict";

// Class Definition
var KTLoginGeneral = function() {
    var _login;

    var _showForm = function(form) {
        var cls = 'login-' + form + '-on';
        var form = 'kt_login_' + form + '_form';

		_login.removeClass('login-forgot-on');

        _login.addClass(cls);

        KTUtil.animateClass(KTUtil.getById(form), 'animate__animated animate__backInUp');
    }

	var _handleForgotForm = function(e) {
		var validation;

		validation = FormValidation.formValidation(
			KTUtil.getById('kt_login_forgot_form'),
			{
				fields: {
					email: {
						validators: {
							notEmpty: {
								message: __('Veuillez entrer votre adresse email')
							},
							emailAddress: {
								message: __('Veuillez entrer une adresse email valide')
							}
						}
					}
				},
				plugins: {
					trigger: new FormValidation.plugins.Trigger(),
					bootstrap: new FormValidation.plugins.Bootstrap()
				}
			}
		);

		// Handle submit button
		$('#kt_login_forgot_submit').on('click', function (e) {
			e.preventDefault();
			$('#kt_login_forgot_submit').attr('disabled', true).addClass('spinner spinner-left spinner-white');

			validation.validate().then(function(status) {
				if (status == 'Valid') {
					let formData = $('#kt_login_forgot_form').serializeArray();
					$.post(
						baseDir + '/ajax/user/reset/password/',
						formData,
						function (result) {
							if (result.status) {
								window.location.href = result.redirect;
							} else {
								$('#kt_login_forgot_submit').attr('disabled', false).removeClass('spinner spinner-left spinner-white');
								swal.fire({
									text: result.message,
									icon: "error",
									buttonsStyling: false,
									confirmButtonText: __('Fermer'),
									confirmButtonClass: "btn font-weight-bold btn-light"
								}).then(function () {
									KTUtil.scrollTop();
								});
							}
						},
						'json'
					);
				} else {
					$('#kt_login_forgot_submit').attr('disabled', false).removeClass('spinner spinner-left spinner-white');
					swal.fire({
						text: __('Une erreur est survenue, vérifiez que vous avez bien indiqué une adresse email valide'),
						icon: "error",
						buttonsStyling: false,
						confirmButtonText: __('Fermer'),
						confirmButtonClass: "btn font-weight-bold btn-light"
					}).then(function() {
						KTUtil.scrollTop();
					});
				}
			});
		});
	}

    // Public Functions
    return {
        // public functions
        init: function() {
            _login = $('#kt_login');
			_handleForgotForm();
        }
    };
}();

// Class Initialization
jQuery(document).ready(function() {
    KTLoginGeneral.init();
});
