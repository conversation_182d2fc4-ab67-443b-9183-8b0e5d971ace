let textEditor;
let inlineEditor;
let aceEditor;

$(document).ready(function () {
    initiateElements();
    setColors();

    $('#container a, #container button').not('.force').attr('onclick', '');
    $('#container a, #container button').not('.force').on('click', function (e) {
        e.preventDefault();
    });
    $('#container form').on('submit', function (e) {
        e.preventDefault();
    });
});

function setColors() {
    if ($('.rounded-colors').length > 0) {
        $('.rounded-colors').each(function() {
            let target = $(this).data('target');
            let id = $(this).data('id');
            let type = $(this).data('type');
            $(this).find('.input-color').each(function () {
                $(this).minicolors({
                    opacity: true,
                    theme: 'bootstrap',
                    format: ((type == 'button' || type == 'button_link') ? 'hex' : 'rgb'),
                    change: function(color) {
                        updateColors(type, id, target);
                    }
                });
            });

            if ($(this).find('.angle').length > 0) {
                var slider = document.getElementById(id + '_slider');
                var angle = $('#' + id + '_angle').val();
                noUiSlider.create(slider, {
                    start: [ angle ],
                    step: 1,
                    range: {
                        'min': [ 0 ],
                        'max': [ 360 ]
                    },
                    format: wNumb({
                        decimals: 0
                    })
                });

                // init slider input
                var sliderInput = $(this).find('.angle');

                slider.noUiSlider.on('slide', function( values, handle ) {
                    sliderInput.val(values[handle]);
                    updateColors(type, id, target);
                });
            }
        });
    }
}

function updateColors(type, id, target) {
    setEditorUpdated(true);
    if (type == 'button' || type == 'button_link') {
        let color = $('#' + id + '_color').val();
        $(target).css('color', color);
        $('.editor-sidebar #' + id + '_color').val(color);
        $('#button_style_' + id).html(target + ':before { color:' + color + ' !important; }');
    } else {
        let color1 = $('#' + id + '_color1').val();
        let color2 = $('#' + id + '_color2').val();
        let angle = $('#' + id + '_angle').val();

        if (color1 && color2) {
            $('.line_overlay').css('background', 'linear-gradient(' + angle + 'deg, ' + color1 + ' 0%, ' + color2 + ' 100%)');
        } else if (color1) {
            $('.line_overlay').css('background', color1);
        } else {
            $('.line_overlay').css('background', 'transparent');
        }
    }
}

function initiateElements()
{
    $('.page-content .bloc').each(function () {
        let id = $(this).data('id');
        if (id) {
            let target = $(this).data('target');
            let type = $(this).data('type');

            $('.bloc-' + id).addClass('bloc-border');
            $('#sidebar_element_' + id).on({
                mouseenter: function () {
                    $('.bloc-' + id).addClass('border-primary');
                },
                mouseleave: function () {
                    $('.bloc-' + id).removeClass('border-primary');
                }
            });

            $('.bloc-' + id).on('click', function(event) {
                event.stopPropagation();

                let sidebarElement = $('#sidebar_element_' + id);
                if (sidebarElement.data('parent')) {
                    let parent = sidebarElement.data('parent');
                    $('#collapseElements .element[data-parent="' + parent + '"]').removeClass('d-none');

                    let parentElement = $('#sidebar_element_' + parent);
                    if (parentElement && parentElement.data('parent')) {
                        let parentParent = parentElement.data('parent');
                        $('#collapseElements .element[data-parent="' + parentParent + '"]').removeClass('d-none');

                        let parentElement2 = $('#sidebar_element_' + parentParent);
                        if (parentElement2 && parentElement2.data('parent')) {
                            let parentParent2 = parentElement2.data('parent');
                            $('#collapseElements .element[data-parent="' + parentParent2 + '"]').removeClass('d-none');
                        }
                    }
                }

                setTimeout(function() {
                    sidebarElement.addClass('highlight');
                    setTimeout(function() {
                        sidebarElement.addClass('highlight-fade');
                    }, 100);
                    setTimeout(function() {
                        sidebarElement.removeClass('highlight highlight-fade');
                    }, 1000);
                }, 1);

                if (sidebarElement.length > 0) {
                    $('#formEditor').animate({
                        scrollTop: sidebarElement.position().top - 100
                    }, 400);
                }
            });

            $('.bloc-' + id).on('dblclick', function(event) {
                event.stopPropagation();

                let sidebarElement = $('#sidebar_element_' + id);
                if ($(this).data('action') && $(this).data('type') != 'button' && $(this).data('type') != 'button_link' && sidebarElement.find('a.edit-element').length > 0) {
                    if ($(event.target).closest('.custom-video').length > 0) {
                        //no actions on these elements
                    } else {
                        sidebarElement.find('a.edit-element').trigger('click');
                    }
                }
            });

            if (type == 'text' && $(this).data('action')) {
                var editorName = 'editor' + id;
                InlineEditor
                    .create(document.querySelector(target), {
                        toolbar: ['heading', '|', 'fontColor', 'fontSize', 'fontBackgroundColor', '|', 'Alignment', '|', 'Bold', 'Italic', 'Underline', 'Strikethrough', 'Subscript', 'Superscript', '|', 'link', 'bulletedList', 'numberedList', 'blockQuote', '|', 'mediaEmbed', 'ckfinder'],
                        alignment: {
                            options: ['left', 'center', 'right', 'justify']
                        },
                        fontColor: {
                            colors: getCkEditorColorMap()
                        },
                        fontBackgroundColor: {
                            colors: getCkEditorColorMap()
                        },
                        ckfinder: {
                            options: {
                                resourceType: 'Medias'
                            }
                        },
                        mediaEmbed: {
                            previewsInData: true
                        }
                    })
                    .then(editor => {
                        window[editorName] = editor;
                        window[editorName].model.document.on('change:data', () => {
                            setEditorUpdated(true);
                            $('.editor-sidebar #' + id).html(window[editorName].getData());
                        });
                    })
                    .catch(error => {
                        console.error(error);
                    });
            }

            if (type == 'string' && $(this).data('action')) {
                var config = {
                    selector: target,
                    menubar: false,
                    inline: true,
                    toolbar: 'forecolor backcolor | bold italic underline strikethrough',
                    plugins : "paste",
                    paste_use_dialog : false,
                    paste_auto_cleanup_on_paste : true,
                    paste_convert_headers_to_strong : false,
                    paste_strip_class_attributes : "all",
                    paste_remove_spans : true,
                    paste_remove_styles : true,
                    paste_retain_style_properties : "",
                    paste_text_sticky: true,
                    paste_text_sticky_default: true,
                    paste_as_text: true,
                    color_map: getTinyMceColorMap(),
                    forced_root_block : false,
                    setup: function(editor) {
                        editor.on('change', function(e) {
                            setEditorUpdated(true);
                            let content = editor.getContent();
                            $('.editor-sidebar #' + id).html(content);
                        });
                    }
                };
                tinymce.init(config);
            }

            if ((type == 'button' || type == 'button_link') && $(this).data('action')) {
                $(target).removeAttr('href');
                var config = {
                    selector: target + ' span',
                    menubar: false,
                    inline: true,
                    toolbar: 'forecolor | bold italic underline strikethrough | fontsizeselect',
                    fontsize_formats: "Normal=26px Petit=18px",
                    color_map: getTinyMceColorMap(),
                    forced_root_block : false,
                    setup: function(editor) {
                        editor.on('change', function(e) {
                            setEditorUpdated(true);
                            let content = '<span>' + editor.getContent() + '</span>';
                            $('.editor-sidebar #' + id).html(content);
                        });
                    }
                };
                tinymce.init(config);
            }
        }
    });

    $('#collapseElements .card-body input[type="checkbox"]').on('change', function () {
        setEditorUpdated(true);
        let target = $(this).data('target');
        let targetContent = $(this).data('content-target');
        let visible = $(this).is(':checked');

        if (visible) {
            $(this).closest('.form-group').removeClass('off').addClass('on');
            $('.bloc-' + target).removeClass('d-none');
            $('#element_' + target).val('on');

            if ($('#sidebar_element_' + target).hasClass('element-image-bg')) {
                $('.bloc-with-bg-image').removeClass('d-none');
            }

            if ($('.bloc-' + target + ' ' + targetContent).is(':empty')) {
                let defaultContent = $('.bloc-' + target).data('default-content');
                if (defaultContent) {
                    $('.bloc-' + target + ' ' + targetContent).html(defaultContent);
                }
            }
        } else {
            $(this).closest('.form-group').removeClass('on').addClass('off');
            $('.bloc-' + target).addClass('d-none');
            $('#element_' + target).val('off');

            if ($('#sidebar_element_' + target).hasClass('element-image-bg')) {
                $('.bloc-with-bg-image').addClass('d-none');
            }

            if ($(this).closest('.form-group').hasClass('element-bloc')) {
                let id = $(this).closest('.form-group').prop('id').replace('sidebar_element_', '');
                $('#collapseElements .element[data-parent="' + id + '"]').each(function() {
                    $(this).addClass('d-none');
                    if ($(this).hasClass('element-bloc')) {
                        var elementId = $(this).prop('id').replace('sidebar_element_', '');
                        $('#collapseElements .element[data-parent="' + elementId + '"]').addClass('d-none');
                    }
                });
            }
        }

        updateCardHeader();
    });

    $('#collapseElements .element-bloc').each(function() {
        let id = $(this).prop('id').replace('sidebar_element_', '');
        $(this).find('.element-bloc-label').append('<span class="label label-outline-primary label-rounded">' + $('#collapseElements .element[data-parent="' + id + '"]').length + '</span>');
    });

    $('#collapseElements .element label.control-label').on('click', function() {
        let id = $(this).data('bloc-target');

        if ($(this).closest('.form-group').hasClass('element-bloc')) {
            $('#collapseElements .element[data-parent="' + id + '"]').toggleClass('d-none');
            if ($('#collapseElements .element[data-parent="' + id + '"]').hasClass('d-none')) {
                $('#collapseElements .element[data-parent="' + id + '"]').each(function () {
                    if ($(this).hasClass('element-bloc')) {
                        var subId = $(this).attr('id').replace('sidebar_element_', '');
                        $('#collapseElements .element[data-parent="' + subId + '"]').addClass('d-none');

                        $('#collapseElements .element[data-parent="' + subId + '"]').each(function () {
                            if ($(this).hasClass('element-bloc')) {
                                var subId2 = $(this).attr('id').replace('sidebar_element_', '');
                                $('#collapseElements .element[data-parent="' + subId2 + '"]').addClass('d-none');
                            }
                        });
                    }
                });
            }
        } else if ($(this).closest('.form-group').hasClass('element-bg-color')) {
            $('#sidebar_element_colors_' + id).toggleClass('d-none');
        } else {
            $('body, html').animate({
                scrollTop: $('.bloc-' + id).offset().top - 100
            }, 400);
        }
    });

    if ($('.editor-sidebar #logo').length > 0) {
        $('.logo-container, .logo-container img').on('dblclick', function () {
            return selectFileWithCKFinder('logo');
        });
    }
}

function EditElement(container)
{
    let target = $(container).data('target');
    let id = $(container).data('id');
    let type = $(container).data('type');

    let title = $(container).closest('.switch').find('label').html();
    let value = $('.editor-sidebar #' + id).html();

    if (!value) {
        let defaultContent = $('.page-container .bloc-' + id).data('default-content');
        if (defaultContent) {
            value = defaultContent;
        }
    }

    if (type == 'image' || type == 'image-bg') {
        return selectImageWithCKFinder(id, target, type);
    }
    if (type == 'bg-color') {
        $('#sidebar_element_colors_' + id).toggleClass('d-none');
        return;
    }

    if (type == 'button_link') {
        let link = $('.editor-sidebar #' + id + '_link').val();
        $('#Modal' + type + ' input[name="link"]').val(link);
    }
    if (type == 'html') {
        $('#Modal' + type + ' .modal-body').html('<div id="ace-editor">' + value + '</div>');
        aceEditor = ace.edit('ace-editor');
        aceEditor.setTheme('ace/theme/dracula');
        aceEditor.setOptions({
            fontSize: "12pt"
        });
        aceEditor.session.setMode('ace/mode/html');
        aceEditor.getSession().setUseWorker(false);
    }

    if (type == 'icon') {
        $.post(
            baseDir + '/ajax/page_editor/icons/get/',
            { CSRFGuard_token:CSRFGuard_token, async: false },
            function (data) {
                $('#Modal' + type + ' #icons').html(data.icons);
                $('#Modal' + type + ' #search').hideseek();
            },
            'json'
        );
    }

    $('#Modal' + type + ' input[name="' + type + '"]').val(value);
    $('#Modal' + type + ' input#target').val(target);
    $('#Modal' + type + ' input#id').val(id);
    $('#Modal' + type + ' .modal-title').html(title);
    $('#Modal' + type).modal('show');
}

function UpdateElement(type)
{
    setEditorUpdated(true);

    let target = $('#Modal' + type + ' input#target').val();
    let id = $('#Modal' + type + ' input#id').val();

    if (type == 'image') {
        var value = $('#Modal' + type + ' input#modal_image').val();
    } else if (type == 'video') {
        var value = $('#Modal' + type + ' input#video_url').val();
    } else if (type == 'html') {
        var aceEditorValue = aceEditor.getValue();
        var value = $('<div/>').text(aceEditorValue).html();
    } else if (type == 'icon') {
        var value = $('#Modal' + type + ' input#icon').val();
    } else {
        var value = $('#Modal' + type + ' input#' + type).val();
    }

    $('.editor-sidebar #' + id).html(value);

    if (type == 'image') {
        $('.page-content ' + target).attr('src', value);
    } else if (type == 'image-bg') {
        $('.page-content ' + target).css('background-image', 'url(' + value + ')');
    } else if (type == 'video') {
        $.post(
            baseDir + '/ajax/page_editor/video/',
            { video: value, CSRFGuard_token:CSRFGuard_token, async: false },
            function (data) {
                $('.page-content ' + target).html(data.html);
                if (data.html.includes('custom-video')) {
                    new Plyr('.page-content ' + target + ' video');
                }
            },
            'json'
        );
    } else if (type == 'button_link') {
        let link = $('#Modal' + type + ' input#link').val();
        $('.page-content ' + target).prop('href', link);
        $('.editor-sidebar #' + id + '_link').val(link);
    } else if (type == 'html') {
        $('.page-content ' + target).html(aceEditorValue);
    } else if (type == 'icon') {
        $('.page-content ' + target).removeClass().addClass(value);
        $('#Modal' + type + ' #icons').html('<div class="d-flex justify-content-center py-10 px-10"><div class="spinner spinner-primary"></div></div>');
    } else {
        $('.page-content ' + target).html(value);
    }

    $('#Modal' + type).modal('hide');

    updateCardHeader();
}

function updateCardHeader()
{
    $('.page-container .card').each(function() {
        var title = $(this).find('.card-title').hasClass('d-none');
        var toolbar = true;
        if ($(this).find('.card-toolbar .bloc').length > 0) {
            toolbar = $(this).find('.card-toolbar .bloc').hasClass('d-none');
        }

        if (title && toolbar) {
            $(this).find('.card-header').addClass('d-none');
        } else {
            $(this).find('.card-header').removeClass('d-none');
        }
    });
}

function selectFileWithCKFinder(elementId) {
    var startupPath = '';
    CKFinder.modal({
        chooseFiles: true,
        width: 800,
        height: 600,
        skin: 'neko',
        startupPath: startupPath,
        plugins: [
            baseDir + '/lib/ckfinder/plugins/CustomDialog/CustomDialog.js'
        ],
        onInit: function (finder) {
            finder.on('files:choose', function (evt) {
                setEditorUpdated(true);
                var file = evt.data.files.first();
                var output = document.getElementById(elementId);
                var fileUrl = file.getUrl();
                $('#' + elementId).val(fileUrl);
                $('#' + elementId).trigger('input');
            });
        }
    });
}

function selectImageWithCKFinder(elementId, target, type) {
    var startupPath = '';
    CKFinder.modal({
        chooseFiles: true,
        width: 800,
        height: 600,
        skin: 'neko',
        startupPath: startupPath,
        plugins: [
            baseDir + '/lib/ckfinder/plugins/CustomDialog/CustomDialog.js'
        ],
        onInit: function (finder) {
            finder.on('files:choose', function (evt) {
                setEditorUpdated(true);

                let file = evt.data.files.first();
                var imageUrl = file.getUrl();

                if (type == 'image-bg') {
                    $('.editor-sidebar #' + elementId).html(imageUrl);
                    $('.page-content ' + target).css('background-image', 'url("' + imageUrl + '")');

                    //update opacity if needed
                    let nextElement = $('#sidebar_element_' + elementId).next();
                    if (nextElement.length > 0 && nextElement.hasClass('element-bg-color')) {
                        nextElement.find('.input-color').each(function() {
                            let inputColorId = $(this).attr('id');
                            let inputColorValue = $('#' + inputColorId).minicolors('rgbObject');
                            let inputColorOpacity = $('#' + inputColorId).minicolors('opacity');
                            if ($(this).val() && (inputColorOpacity == 1 || inputColorOpacity == '1.00')) {
                                $('#' + inputColorId).minicolors('value', 'rgba(' + inputColorValue.r + ',' + inputColorValue.g + ',' + inputColorValue.b + ',.7)');
                            }
                        });
                    }
                } else {
                    $('.editor-sidebar #' + elementId).html(imageUrl);
                    updateImage(elementId, target, imageUrl);
                }
            });
        }
    });
}

function updateImage(elementId, target, imageUrl) {
    if ($('.page-content .bloc-' + elementId).data('in_svg')) {
        $('.page-content ' + target).attr('xlink:href', imageUrl);
    } else {
        $('.page-content ' + target).attr('src', imageUrl);
    }

    $('.editor-sidebar #' + elementId).html(imageUrl);
}

function getTinyMceColorMap()
{
    var colors = [];
    colors['#5e98e0'] =__('Bleu clair');
    colors['#4b50dd'] = __('Bleu');
    colors['#8f54de'] = __('Violet');

    colors['#000000'] = __('Noir');
    colors['#4d4d4d'] = __('Gris pâle');
    colors['#999999'] = __('Gris');
    colors['#e6e6e6'] = __('Gris clair');
    colors['#ffffff'] = __('Blanc');

    colors['#d65752'] = __('Rouge');
    colors['#dc9b5a'] = __('Orange');
    colors['#e6e567'] = __('Jaune');
    colors['#a9e364'] = __('Vert clair');
    colors['#7ae263'] = __('Vert');

    colors['#79e29f'] = __('Bleu vert');
    colors['#79e3e4'] = __('Turquoise');

    return colors;
}

function getCkEditorColorMap()
{
    var colors = [];
    colors.push({ color: '#5e98e0', label: __('Bleu clair') });
    colors.push({ color: '#4b50dd', label: __('Bleu') });
    colors.push({ color: '#8f54de', label: __('Violet') });

    colors.push({ color: '#000000', label: __('Noir') });
    colors.push({ color: '#4d4d4d', label: __('Gris pâle') });
    colors.push({ color: '#999999', label: __('Gris') });
    colors.push({ color: '#e6e6e6', label: __('Gris clair') });
    colors.push({ color: '#ffffff', label: __('Blanc') });

    colors.push({ color: '#d65752', label: __('Rouge') });
    colors.push({ color: '#dc9b5a', label: __('Orange') });
    colors.push({ color: '#e6e567', label: __('Jaune') });
    colors.push({ color: '#a9e364', label: __('Vert clair') });
    colors.push({ color: '#7ae263', label: __('Vert') });

    colors.push({ color: '#79e29f', label: __('Bleu vert') });
    colors.push({ color: '#79e3e4', label: __('Turquoise') });

    return colors;
}
