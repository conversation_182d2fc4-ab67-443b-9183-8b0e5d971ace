function ExportInvoices() {
    var date_start = $('input[name="date_start"]').val();
    var date_end = $('input[name="date_end"]').val();

    $('#loader').html('<div class="progress" style="width:400px; margin: 0 auto"><div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width:0px"></div></div>');
    $.post(
        baseDir + '/ajax/invoices/export/',
        { date_start:date_start, date_end:date_end, CSRFGuard_token:CSRFGuard_token, async: false },
        function(data) {
            if (data.status) {
                var nb_loops = data.nb_loops;
                var uploadId = data.uploadId;

                $('.blank-state h1').html(data.strNbInvoices);
                $('.blank-state p').html(data.timeSpent);

                //first call
                exportInvoices(date_start, date_end, nb_loops, 1, uploadId);
            } else {
                ShowToast('error', data.message);
            }
        },
    'json');
}

function exportInvoices(date_start, date_end, nb_loops, nb_loop, uploadId) {
	$.post(
        baseDir + '/ajax/invoices/export_loop/',
        { date_start: date_start, date_end: date_end, nb_loops: nb_loops, nb_loop: nb_loop, uploadId: uploadId, CSRFGuard_token:CSRFGuard_token, async: true },
        function(data2) {
	        if (data2.status) {
			    $('.progress-bar').css('width', parseInt(data2.percentage*4)+'px');

			    nb_loop++;
			    if (nb_loop < nb_loops) {
                    exportInvoices(date_start, date_end, nb_loops, nb_loop, uploadId);
			    } else if (nb_loop == nb_loops) {
				    $('#infos').html(__('Création du fichier PDF... Encore quelques secondes...'));
				    $('.progress-bar').css('width', parseInt(99*4)+'px');
                    exportInvoices(date_start, date_end, nb_loops, nb_loop, uploadId);
			    } else {
	                $('#infos').html('');
	                $('#loader').html('<a class="btn btn-primary btn-lg font-weight-bolder mt-4" href="'+data2.file_url+'" target="_blank">' + __('Télécharger le fichier') + '</a>');
			    }
            } else {
                ShowToast('error', data2.message);
            }
        },
    'json');
}
