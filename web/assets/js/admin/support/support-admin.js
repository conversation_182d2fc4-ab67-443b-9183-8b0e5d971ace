let quillEditor;
let ticketId;

$(document).ready(function() {
	if ($('#kt_inbox_reply_editor').length > 0) {
		initEditor();
	}

	$('#kt_inbox_list').on('keyup', '#search_support', debounce(function (e) {
		searchTickets(e.target.value);
	}, 700));

	$('#kt_inbox_view').on('click', '.reply-header', function() {
		if ($(this).parent().hasClass('toggle-on')) {
			$(this).parent().addClass('toggle-off').removeClass('toggle-on');
		} else {
			$(this).parent().removeClass('toggle-off').addClass('toggle-on');
		}
	});
});

function debounce(callback, delay) {
	var timer;
	return function () {
		var args = arguments;
		var context = this;
		clearTimeout(timer);
		timer = setTimeout(function () {
			callback.apply(context, args);
		}, delay)
	}
}

/* ---------- Support ---------- */
function searchTickets() {
	var search = $('#search_support').val();
	if (!search) {
		ReloadTickets();
		return;
	}

	$('#search-group .input-group-append').addClass('spinner spinner-sm spinner-primary');
	KTApp.block('#kt_inbox_list', {});
	$.post(
		baseDir + '/ajax/support/search/',
		{ search: search, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			$('#search-group .input-group-append').removeClass('spinner spinner-sm spinner-primary');
			KTApp.unblock('#kt_inbox_list', {});
			if (data.status) {
				$('#kt_inbox_list #tickets .list').html(data.content);
				$('#inbox-toolbar #pagination').html('');
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
	return false;
}

function ReloadTickets() {
	var id_department = $('.navi-link.active').parent().prop('id');
	if (id_department == -3 && $('#departments #-3').data('user')) {
		displayUserTickets($('#departments #-3').data('user'));
	} else {
		var page = $('#pagination #page').val();
		DisplayDepartment(id_department, page);
	}
}

function DisplayPage(type) {
	var id_department = $('.navi-link.active').parent().prop('id');
	var page = parseInt($('#pagination #page').val());
	if (type == 'previous') {
		page = page - 1;
	} else {
		page = page + 1;
	}
	DisplayDepartment(id_department, page);
}

function DisplayHome() {
	$('#kt_inbox_list').removeClass('d-none').addClass('d-block');
	$('#kt_inbox_view').removeClass('d-block').addClass('d-none');
	$('#kt_inbox_view #ticket').html('');
	$('#kt_inbox_view #ticket-toolbar').html('');
}

function DisplayDepartment(id_department, page) {

	$('.navi-link.active').removeClass('active');
	$('#departments #'+id_department + ' .navi-link').addClass('active');

	KTApp.block('#kt_inbox_list', {});
	KTApp.block('#kt_inbox_view', {});

	$.post(
		baseDir + '/ajax/support/department/display/',
		{ id_department: id_department, page: page, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#kt_inbox_list', {});
			KTApp.unblock('#kt_inbox_view', {});
			if (data.status) {
				$('#kt_inbox_list').removeClass('d-none').addClass('d-block');
				$('#kt_inbox_view').removeClass('d-block').addClass('d-none');
    			$('#kt_inbox_list #tickets .list').html(data.content);
				$('#kt_inbox_list #inbox-toolbar').html(data.toolbar);
				$('#kt_inbox_view #ticket').html('');
				$('#kt_inbox_view #ticket-toolbar').html('');
				$('#inbox-toolbar').html(data.toolbar);
				$('#kt_inbox_list #toolbar [data-rel="tooltip"]').tooltip();
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function displayUserTickets(userId) {
	$('.navi-link.active').removeClass('active');
	$('#departments #-3 .navi-link').addClass('active');
	$('#departments #-3').data('user', userId);

	KTApp.block('#kt_inbox_list', {});
	KTApp.block('#kt_inbox_view', {});

	$.post(
		baseDir + '/ajax/support/user/tickets/',
		{ userId: userId, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#kt_inbox_list', {});
			KTApp.unblock('#kt_inbox_view', {});
			if (data.status) {
				$('#kt_inbox_list').removeClass('d-none').addClass('d-block');
				$('#kt_inbox_view').removeClass('d-block').addClass('d-none');
				$('#kt_inbox_list #tickets .list').html(data.content);
				$('#kt_inbox_list #inbox-toolbar').html(data.toolbar);
				$('#kt_inbox_view #ticket').html('');
				$('#kt_inbox_view #ticket-toolbar').html('');
				$('#inbox-toolbar').html(data.toolbar);
				$('#kt_inbox_list #toolbar [data-rel="tooltip"]').tooltip();
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function DisplayTicket(ticketid) {
	KTApp.block('#kt_inbox_list', {});
	ticketId = ticketid;

	$.post(
		baseDir + '/ajax/support/ticket/display/',
		{ ticketid: ticketid, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#kt_inbox_list', {});
			if (data.status) {
				$('#kt_inbox_view').removeClass('d-none').addClass('d-block');
				$('#kt_inbox_list').removeClass('d-block').addClass('d-none');
				$('#kt_inbox_view #ticket').html(data.content);
				$('#kt_inbox_view #ticket-toolbar').html(data.toolbar);
				$('#kt_inbox_view #ticket-toolbar [data-rel="tooltip"]').tooltip();
				$('#kt_inbox_view #ticket [data-rel="tooltip"]').tooltip();

				initEditor();
				initDropzone();

				if ($('#kt_inbox_view #ticket #saved_reply').length > 0) {
					displaySaveReplies();
				}
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function displaySaveReplies() {
	$('#saved_reply').on('change', function() {
		var idSavedReply = $(this).val();
		if (idSavedReply != 0) {
			$.post(
				baseDir + '/ajax/support/saved_reply/',
				{ id_saved_reply: idSavedReply, async: false, CSRFGuard_token:CSRFGuard_token },
				function(data) {
					if (data.status) {
						if (data.content) {
							quillEditor.root.innerHTML = data.content;
						}
					} else {
						ShowToast('error', data.message);
					}
				},
				'json'
			);
		}
	});
}

function saveReply(ticketid) {
	KTApp.block('#kt_inbox_view', {});

	var data = $('#kt_inbox_reply_form').serialize();
	var message = quillEditor.root.innerHTML;
	$.post(
		baseDir + '/ajax/support/ticket/reply/',
		{ ticketid: ticketid, data:data, message:message, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#kt_inbox_view', {});
			if (data.status) {
				$('#kt_inbox_view #ticket').html(data.content);
				$('#kt_inbox_view #ticket-toolbar').html(data.toolbar);
				$('#kt_inbox_view #ticket-toolbar [data-rel="tooltip"]').tooltip();
				initEditor();
				initDropzone();
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function initEditor() {
	// init editor
	var id = 'kt_inbox_reply_editor';
	var replyEl = KTUtil.getById('kt_inbox_reply');

	var options = {
		modules: {
			toolbar: {}
		},
		placeholder: __('Tapez votre réponse ici...'),
		theme: 'snow'
	};

	// Init editor
	quillEditor = new Quill('#' + id, options);

	// Customize editor
	var toolbar = KTUtil.find(replyEl, '.ql-toolbar');
	var editorContainer = KTUtil.find(replyEl, '.ql-editor');

	if (toolbar) {
		KTUtil.addClass(toolbar, 'px-5 border-top-0 border-left-0 border-right-0');
	}
	if (editorContainer) {
		KTUtil.addClass(editorContainer, 'px-8');
	}
}

function SetTicket(ticketid, state) {
	$(".tooltip").tooltip("hide");

	$.post(
		baseDir + '/ajax/support/ticket/update/state/',
		{ ticketid: ticketid, state: state, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			if (data.status) {
				$('#kt_inbox_view #ticket-toolbar').html(data.toolbar);
				ShowToast('success', data.message);
				$('#kt_inbox_view #ticket-toolbar [data-rel="tooltip"]').tooltip();
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function displayModalSetTicketUser(ticketid) {
	ticketId = ticketid;
	$('#ModalSetTicketUser').modal('show');
}

function setTicketUser(userId) {
	KTApp.block('#ModalSetTicketUser .modal-content', {});
	$.post(
		baseDir + '/ajax/support/ticket/update/user/',
		{ ticketid: ticketId, user_id: userId, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#ModalSetTicketUser .modal-content');
			if (data.status) {
				ShowToast('success', data.message);
				$('#ModalSetTicketUser').modal('hide');
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function displayModalSetTicketDepartment(ticketid) {
	ticketId = ticketid;
	$('#ModalSetTicketDepartment').modal('show');
}

function setTicketDepartment(id_department) {
	KTApp.block('#ModalSetTicketDepartment .modal-content', {});
	$.post(
		baseDir + '/ajax/support/ticket/update/department/',
		{ ticketid: ticketId, id_department: id_department, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#ModalSetTicketDepartment .modal-content');
			if (data.status) {
				ShowToast('success', data.message);
				$('#ModalSetTicketDepartment').modal('hide');
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function CreateConfirmationDeleteTicket(ticketid) {
	Swal.fire({
		title: __('Confirmation'),
		text: __('Etes-vous sûr de vouloir supprimer ce ticket ?'),
		icon: "warning",
		showCancelButton: true,
		confirmButtonText: __('Supprimer'),
		cancelButtonText: __('Annuler'),
		reverseButtons: true,
		confirmButtonColor: '#F64E60',
		iconColor: '#F64E60',
	}).then(function(result) {
		if (result.value) {
			DeleteTicket(ticketid);
		}
	});
}

function DeleteTicket(ticketid) {
	KTApp.block('#kt_inbox_view', {});
	ticketId = '';
	$.post(
		baseDir + '/ajax/support/ticket/delete/',
		{ ticketid: ticketid, action: 'delete', async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			if (data.status) {
				ShowToast('success', __('Ticket supprimé avec succès'));
				ReloadTickets();
			} else {
				KTApp.unblock('#kt_inbox_view', {});
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function saveNote(ticketid) {
	KTApp.block('#kt_inbox_view', {});
	var content = quillEditor.root.innerHTML;
	$.post(
		baseDir + '/ajax/support/note/save/',
		{ ticketid:ticketid, content:content, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#kt_inbox_view', {});
			if (data.status) {
				$('#kt_inbox_view #ticket').html(data.content);
				$('#kt_inbox_view #ticket-toolbar').html(data.toolbar);
				$('#kt_inbox_view #ticket-toolbar [data-rel="tooltip"]').tooltip();
				initEditor();
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function CreateConfirmationDeleteNote(ticketid, idNote) {
	Swal.fire({
		title: __('Confirmation'),
		text: __('Etes-vous sûr de vouloir supprimer cette note ?'),
		icon: "warning",
		showCancelButton: true,
		confirmButtonText: __('Supprimer'),
		cancelButtonText: __('Annuler'),
		reverseButtons: true,
		confirmButtonColor: '#F64E60',
		iconColor: '#F64E60',
	}).then(function(result) {
		if (result.value) {
			DeleteNote(ticketid, idNote);
		}
	});
}

function UseNote(idNote) {
	var content = $('#note' + idNote + ' .note-content').html();
	quillEditor.root.innerHTML = content;
}

function DeleteNote(ticketid, idNote) {
	$.post(
        baseDir + '/ajax/support/note/delete/',
		{ ticketid:ticketid, id_note: idNote, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
		    if (data.status) {
				$('#note'+idNote).fadeOut(300, function() {
					$(this).remove();
				});
			} else {
                ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function initDropzone() {
	var id = "#kt_inbox_reply_attachments";
	var previewNode = $(id + " .dropzone-item");
	previewNode.id = "";
	var previewTemplate = previewNode.parent('.dropzone-items').html();
	previewNode.remove();

	var myDropzone = new Dropzone(id, {
		url: baseDir + '/lib/upload/',
		parallelUploads: 1,
		maxFilesize: 10,
		previewTemplate: previewTemplate,
		previewsContainer: id + " .dropzone-items",
		clickable: id + "_select"
	});

	myDropzone.on("addedfile", function(file) {
		// Hookup the start button
		$(document).find(id + ' .dropzone-item').css('display', '');
	});

	// Update the total progress bar
	myDropzone.on("totaluploadprogress", function(progress) {
		document.querySelector(id + " .progress-bar").style.width = progress + "%";
	});

	myDropzone.on("sending", function(file) {
		// Show the total progress bar when upload starts
		document.querySelector(id + " .progress-bar").style.opacity = "1";
	});

	// Hide the total progress bar when nothing's uploading anymore
	myDropzone.on("complete", function(file) {
		let response = JSON.parse(file.xhr.response);
		if (file.status == 'success') {
			$(file.previewElement).append('<input type="hidden" name="attachments[]" value="' + response.files[0].name + '">');
		}

		var thisProgressBar = id + " .dz-complete";
		setTimeout(function() {
			$(thisProgressBar + " .progress-bar, " + thisProgressBar + " .progress").css('opacity', '0');
		}, 300)
	});
}
