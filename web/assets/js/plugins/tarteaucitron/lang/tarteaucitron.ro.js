/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Buna! Acest site este transparent și vă permite să alegeți serviciile terță parte pe care doriți să le permiteți.",
    "adblock_call": "Dezactivați-vă adblocker-ul pentru a începe personalizarea.",
    "reload": "Reincarca Pagina",
    
    "alertBigScroll": "Continuând să defilați,",
    "alertBigClick": "Dacă continuați să răsfoiți acest site,",
    "alertBig": "permiteți tuturor serviciilor terță parte",
    
    "alertBigPrivacy": "Acest site utilizează cookie-uri și vă oferă control asupra a ceea ce doriți să activați",
    "alertSmall": "Gestionați serviciile",
    "personalize": "Personalizați",
    "acceptAll": "OK, acceptați-le pe toate",
    "close": "Închide",

    "privacyUrl": "Politica de confidentialitate",
    
    "all": "Preferință pentru toate serviciile",

    "info": "Protejați-vă confidențialitatea",
    "disclaimer": "Permițând acestor servicii terțe părți să acceptați cookie-urile și utilizarea tehnologiilor de urmărire necesare pentru buna funcționare a acestora.",
    "allow": "Permite",
    "deny": "Refuza",
    "noCookie": "Acest serviciu nu utilizează modul cookie.",
    "useCookie": "Acest serviciu se poate instala",
    "useCookieCurrent": "Acest serviciu a fost instalat",
    "useNoCookie": "Acest serviciu nu a instalat niciun cookie.",
    "more": "Citeste mai mult",
    "source": "Vizualizați site-ul oficial",
    "credit": "Cookie manager de către tarteaucitron.js",

    "toggleInfoBox": "Afișați / ascundeți informații despre stocarea modulelor cookie",
    "title": "Panoul de gestionare a panourilor cookie",
    "cookieDetail": "Detaliile cookie pentru",
    "ourSite": "pe site-ul nostru",
    "newWindow": "(fereastră nouă)",
    "allowAll": "Permiteți toate cookie-urile",
    "denyAll": "Respinge toate cookie-urile",
    
    "fallback": "este dezactivat.",

    "ads": {
        "title": "Rețea de publicitate",
        "details": "Rețelele publicitare pot genera venituri prin vânzarea de spațiu publicitar pe site."
    },
    "analytic": {
        "title": "Măsurarea audienței",
        "details": "Serviciile de măsurare a audienței utilizate pentru a genera participarea la statistici utile pentru îmbunătățirea site-ului."
    },
    "social": {
        "title": "Retele sociale",
        "details": "Rețelele sociale pot îmbunătăți gradul de utilizare a site-ului și pot ajuta să îl promoveze prin intermediul acțiunilor."
    },
    "video": {
        "title": "Videoclipuri",
        "details": "Serviciile de partajare video ajută la adăugarea de materiale media pe site și la creșterea vizibilității acestora."
    },
    "comment": {
        "title": "Comentarii",
        "details": "Managerii de comentarii facilitează depunerea de comentarii și lupta împotriva spamului."
    },
    "support": {
        "title": "Susţinere",
        "details": "Serviciile de asistență vă permit să contactați echipa site-ului și să vă ajutați să îl îmbunătățiți."
    },
    "api": {
        "title": "APIs",
        "details": "API-urile sunt folosite pentru a încărca scripturi: geolocație, motoare de căutare, traduceri, ..."
    },
    "other": {
        "title": "Alte",
        "details": "Servicii pentru afișarea conținutului web."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
