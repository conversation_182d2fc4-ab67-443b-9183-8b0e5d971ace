/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Γεια σας! Ο ιστότοπος αυτός σας επιτρέπει να επιλέξετε τις υπηρεσίες που παρέχονται από τρίτους που θα θέλατε να επιτρέψετε.",
    "adblock_call": "Παρακαλώ απενεργοποιήστε τα προγράμματα απόρριψης διαφημίσεων για να ξεκινήσετε τις τροποποιήσεις σας.",
    "reload": "Ανανέωση της σελίδας",
    
    "alertBigScroll": "Συνεχίζοντας την ανάγνωση (κύλιση) της σελίδας,",
    "alertBigClick": "Αν συνεχίσετε την περιήγηση σας στον ιστότοπο,",
    "alertBig": "επιτρέπετε όλες τις υπηρεσίες που παρέχονται από τρίτους",
    
    "alertBigPrivacy": "Ο ιστότοπος αυτός χρησιμοποιεί &quot;μπισκότα&quot; (cookies) και σας επιτρέπει να ελέγξετε τι θέλετε να ενεργοποιήσετε",
    "alertSmall": "Διαχείριση υπηρεσιών",
    "personalize": "Εξατομίκευση",
    "acceptAll": "OK, αποδοχή όλων",
    "close": "Κλείσιμο",

    "privacyUrl": "Πολιτική απορρήτου",
    
    "all": "Προτίμηση για όλες τις υπηρεσίες",

    "info": "Προστασία των προσωπικών σας δεδομένων",
    "disclaimer": "Επιτρέποντας αυτές τις υπηρεσίες που παρέχονται από τρίτους, αποδέχεστε τα &quot;μπισκότα&quot; (cookies) τους καθώς και τη χρήση τεχνολογιών παρακολούθησης που είναι απαραίτητες για τη λειτουργία τους.",
    "allow": "Επέτρεψε",
    "deny": "Απόρριψε",
    "noCookie": "Η υπηρεσία αυτή δε χρησιμοποιεί &quot;μπισκότα&quot; (cookies).",
    "useCookie": "Η υπηρεσία αυτή μπορεί να αποθηκεύσει ",
    "useCookieCurrent": "Η υπηρεσία αυτή έχει αποθηκεύσει ",
    "useNoCookie": "Η υπηρεσία αυτή δεν έχει αποθηκεύσει κανένα &quot;μπισκότο&quot; (cookie).",
    "more": "Διαβάστε περισσότερα",
    "source": "Δείτε τον επίσημο ιστότοπο",
    "credit": "Cookies manager by tarteaucitron.js",

    "toggleInfoBox": "Προβολή/Απόκρυψη πληροφοριών για την αποθήκευση &quot;μπισκότων&quot; (cookies)",
    "title": "Πίνακας διαχείρισης &quot;Μπισκότων&quot; (Cookies)",
    "cookieDetail": "Λεπτομέρειες &quot;μπισκότων&quot; (cookies) για",
    "ourSite": "στον ιστότοπο μας",
    "newWindow": "(νέο παράθυρο)",
    "allowAll": "Επέτρεψε όλα τα &quot;μπισκότα&quot; (cookies)",
    "denyAll": "Απόρριψε όλα τα &quot;μπισκότα&quot; (cookies)",
    
    "fallback": "είναι απενεργοποιημένο.",

    "ads": {
        "title": "Διαφημιστικό Δίκτυο",
        "details": "Τα διαφημιστικά δίκτυα μπορούν να αποφέρουν εισόδημα πουλώντας διαφημιστικό χώρο στη σελίδα."
    },
    "analytic": {
        "title": "Μετρήσεις κοινού",
        "details": "Οι υπηρεσίες μέτρησης κοινού χρησιμοποιούνται για τον υπολογισμό χρήσιμων στατιστικών επισκεψιμότητας του ιστοτόπου για την βελτίωση του."
    },
    "social": {
        "title": "Κοινωνικά δίκτυα",
        "details": "Τα κοινωνικά δίκτυα μπορούν να βελτιώσουν την χρηστικότητα του ιστοτόπου και να τον προωθήσουν μέσω κοινοποιήσεων."
    },
    "video": {
        "title": "Βίντεο",
        "details": "Υπηρεσίες διαμοιρασμού βίντεο που βοηθούν να παρουσιαστεί πλούσιο περιεχόμενο στον ιστότοπο και να αυξήσουν την αναγνωρισιμότητα του."
    },
    "comment": {
        "title": "Σχόλια",
        "details": "Οι διαχειριστές σχολίων βοηθούν την καταχώρηση σχολίων και προστατεύουν από κακόβουλες ενέργειες."
    },
    "support": {
        "title": "Υποστήριξη",
        "details": "Οι υποστηρικτικές υπηρεσίες σας επιτρέπουν να επικονωνείτε με την ομάδα υποστήριξης του ιστοτόπου και να βοηθήσετε στην βελτίωση του."
    },
    "api": {
        "title": "APIs",
        "details": "Τα API χρησιμοποιούνται για την φόρτωση προγραμμάτων: αναγνώρισης τοποθεσίας, μηχανών αναζήτησης, μεταφράσεων, ..."
    },
    "other": {
        "title": "Λοιπές υπηρεσίες",
        "details": "Υπηρεσίες που παρουσιάζουν άλλο περιεχόμενο."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
