/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Hello! This site is transparent and lets you chose the 3rd party services you want to allow.",
    "adblock_call": "Please disable your adblocker to start customizing.",
    "reload": "Refresh the page",
    
    "alertBigScroll": "By continuing to scroll,",
    "alertBigClick": "If you continue to browse this website,",
    "alertBig": "you are allowing all third-party services",
    
    "alertBigPrivacy": "当サイトはクッキーを利用しております。お客様自身でクッキー利用の設定および管理ができます。",
    "alertSmall": "Manage services",
    "personalize": "カスタマイズする",
    "acceptAll": "全てに同意する",
    "close": "Close",

    "privacyUrl": "プライバシーポリシー",
    
    "all": "Preference for all services",

    "info": "Protecting your privacy",
    "disclaimer": "これらの第三者によるサービスを許可することで、サイトの動作に必要なクッキーや他のトラッキング・テクノロジーの使用に同意するものとみなします。",
    "allow": "許可",
    "deny": "拒否",
    "noCookie": "This service does not use cookie.",
    "useCookie": "This service can install",
    "useCookieCurrent": "このサービスは２つのクッキーを利用します",
    "useNoCookie": "This service has not installed any cookie.",
    "more": "もっと読む",
    "source": "公式サイトで閲覧する",
    "credit": "Cookies manager by tarteaucitron.js",
    "noServices": "This website does not use any cookie requiring your consent.",

    "toggleInfoBox": "Show/hide informations about cookie storage",
    "title": "クッキー利用の管理について",
    "cookieDetail": "Cookie detail for",
    "ourSite": "on our site",
    "newWindow": "(new window)",
    "allowAll": "すべてのクッキーを許可する",
    "denyAll": "すべてのクッキーを拒否する",
    
    "fallback": "is disabled.",

    "ads": {
        "title": "Advertising network",
        "details": "Ad networks can generate revenue by selling advertising space on the site."
    },
    "analytic": {
        "title": "Audience measurement",
        "details": "The audience measurement services used to generate useful statistics attendance to improve the site."
    },
    "social": {
        "title": "Social networks",
        "details": "Social networks can improve the usability of the site and help to promote it via the shares."
    },
    "video": {
        "title": "Videos",
        "details": "Video sharing services help to add rich media on the site and increase its visibility."
    },
    "comment": {
        "title": "Comments",
        "details": "Comments managers facilitate the filing of comments and fight against spam."
    },
    "support": {
        "title": "Support",
        "details": "Support services allow you to get in touch with the site team and help to improve it."
    },
    "api": {
        "title": "APIs",
        "details": "APIs are used to load scripts: geolocation, search engines, translations, ..."
    },
    "other": {
        "title": "Other",
        "details": "Services to display web content."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
