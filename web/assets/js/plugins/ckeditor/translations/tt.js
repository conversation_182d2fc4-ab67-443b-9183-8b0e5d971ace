(function(e){const t=e["tt"]=e["tt"]||{};t.dictionary=Object.assign(t.dictionary||{},{"%0 of %1":"",Accept:"","Align cell text to the bottom":"","Align cell text to the center":"","Align cell text to the left":"","Align cell text to the middle":"","Align cell text to the right":"","Align cell text to the top":"","Align table to the left":"","Align table to the right":"",Alignment:"",Aquamarine:"Аквамарин",Background:"",Black:"Кара",Blue:"Зәңгәр",Bold:"Калын",Border:"","Break text":"","Bulleted List":"","Bulleted list styles toolbar":"",Cancel:"Баш тарт","Cannot upload file:":"","Caption for image: %0":"","Caption for the image":"","Cell properties":"","Center table":"","Centered image":"","Change image text alternative":"",Circle:"Түгәрәк",Clear:"","Click to edit block":"",Code:"Код",Color:"Төс","Color picker":"",Column:"",Dashed:"",Decimal:"","Decimal with leading zero":"","Delete column":"","Delete row":"","Dim grey":"",Dimensions:"",Disc:"Диск",Dotted:"",Double:"",Downloadable:"","Drag to move":"","Dropdown toolbar":"","Edit block":"","Edit link":"","Editor block content toolbar":"","Editor contextual toolbar":"","Editor editing area: %0":"","Editor toolbar":"","Enter image caption":"","Enter table caption":"","Full size image":"",Green:"Яшел",Grey:"Соры",Groove:"","Header column":"","Header row":"",Height:"",HEX:"","Horizontal text alignment toolbar":"","Image resize list":"","Image toolbar":"","image widget":"","In line":"",Insert:"","Insert column left":"","Insert column right":"","Insert image":"","Insert image via URL":"","Insert row above":"","Insert row below":"","Insert table":"",Inset:"",Italic:"","Justify cell text":"","Left aligned image":"","Light blue":"Ачык зәңгәр","Light green":"Ачык яшел","Light grey":"Ачык соры",Link:"Сылтама","Link image":"","Link URL":"","List properties":"","Lower-latin":"","Lower–roman":"","Merge cell down":"","Merge cell left":"","Merge cell right":"","Merge cell up":"","Merge cells":"",Next:"","No results found":"","No searchable items":"",None:"","Numbered List":"","Numbered list styles toolbar":"","Open in a new tab":"","Open link in new tab":"",Orange:"Кызгылт",Original:"",Outset:"",Padding:"",Previous:"",Purple:"Шәмәхә",Red:"Кызыл",Redo:"Кабатла","Remove color":"","Resize image":"","Resize image to %0":"","Resize image to the original size":"","Restore default":"","Reversed order":"","Rich Text Editor":"","Rich Text Editor. Editing area: %0":"",Ridge:"","Right aligned image":"",Row:"",Save:"Сакла","Select column":"","Select row":"","Show more items":"","Side image":"",Solid:"","Split cell horizontally":"","Split cell vertically":"",Square:"","Start at":"","Start index must be greater than 0.":"",Strikethrough:"",Style:"",Subscript:"",Superscript:"","Table alignment toolbar":"","Table cell text alignment":"","Table properties":"","Table toolbar":"","Text alternative":"",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':"",'The value is invalid. Try "10px" or "2em" or simply "2".':"","This link has no URL":"","To-do List":"","Toggle caption off":"","Toggle caption on":"","Toggle the circle list style":"","Toggle the decimal list style":"","Toggle the decimal with leading zero list style":"","Toggle the disc list style":"","Toggle the lower–latin list style":"","Toggle the lower–roman list style":"","Toggle the square list style":"","Toggle the upper–latin list style":"","Toggle the upper–roman list style":"",Turquoise:"Фервоз",Underline:"",Undo:"",Unlink:"",Update:"Яңарт","Update image URL":"","Upload failed":"","Upper-latin":"","Upper-roman":"","Vertical text alignment toolbar":"",White:"Ак",Width:"","Wrap text":"",Yellow:"Сары"});t.getPluralForm=function(e){return 0}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));