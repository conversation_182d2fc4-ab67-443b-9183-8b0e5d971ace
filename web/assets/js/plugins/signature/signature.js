$(function() {
    let signature = $('.jq-signature').jqSignature({
        width: 600,
        height: 300,
        autoFit: true,
        border: '1px solid #0E2072',
        lineColor: '#111111',
        lineWidth: 2,
    });
    signature.on('jq.signature.changed', function() {
        let signatureDataUrl = signature.jqSignature('getDataURL');
        $('#signature').val(signatureDataUrl);
    });
    $('#jq-signature-clear').click(function(e) {
        e.preventDefault();
        signature.jqSignature('clearCanvas');
    });
});
