/*! http://keith-wood.name/signature.html
	Signature plugin for jQuery UI v1.2.1.
	Requires excanvas.js in IE.
	Written by <PERSON> (wood.keith{at}optusnet.com.au) April 2012.
	Available under the MIT (http://keith-wood.name/licence.html) license. 
	Please attribute the author if you use it. */
!function(a){"use strict";var b={options:{distance:0,background:"#fff",color:"#000",thickness:2,guideline:!1,guidelineColor:"#a0a0a0",guidelineOffset:50,guidelineIndent:10,notAvailable:"Your browser doesn't support signing",scale:1,syncField:null,syncFormat:"JSON",svgStyles:!1,change:null},_create:function(){this.element.addClass(this.widgetFullName||this.widgetBaseClass);try{this.canvas=a('<canvas width="'+this.element.width()+'" height="'+this.element.height()+'">'+this.options.notAvailable+"</canvas>")[0],this.element.append(this.canvas)}catch(b){a(this.canvas).remove(),this.resize=!0,this.canvas=document.createElement("canvas"),this.canvas.setAttribute("width",this.element.width()),this.canvas.setAttribute("height",this.element.height()),this.canvas.innerHTML=this.options.notAvailable,this.element.append(this.canvas),G_vmlCanvasManager&&G_vmlCanvasManager.initElement(this.canvas)}this.ctx=this.canvas.getContext("2d"),this._refresh(!0),this._mouseInit()},_refresh:function(b){if(this.resize){var c=a(this.canvas);a("div",this.canvas).css({width:c.width(),height:c.height()})}this.ctx.fillStyle=this.options.background,this.ctx.strokeStyle=this.options.color,this.ctx.lineWidth=this.options.thickness,this.ctx.lineCap="round",this.ctx.lineJoin="round",this.clear(b)},clear:function(a){this.options.disabled||(this.ctx.clearRect(0,0,this.element.width(),this.element.height()),this.ctx.fillRect(0,0,this.element.width(),this.element.height()),this.options.guideline&&(this.ctx.save(),this.ctx.strokeStyle=this.options.guidelineColor,this.ctx.lineWidth=1,this.ctx.beginPath(),this.ctx.moveTo(this.options.guidelineIndent,this.element.height()-this.options.guidelineOffset),this.ctx.lineTo(this.element.width()-this.options.guidelineIndent,this.element.height()-this.options.guidelineOffset),this.ctx.stroke(),this.ctx.restore()),this.lines=[],a||this._changed())},_changed:function(b){if(this.options.syncField){var c="";switch(this.options.syncFormat){case"PNG":c=this.toDataURL();break;case"JPEG":c=this.toDataURL("image/jpeg");break;case"SVG":c=this.toSVG();break;default:c=this.toJSON()}a(this.options.syncField).val(c)}this._trigger("change",b,{})},_setOptions:function(){this._superApply?this._superApply(arguments):a.Widget.prototype._setOptions.apply(this,arguments);var b=0,c=!0;for(var d in arguments[0])arguments[0].hasOwnProperty(d)&&(b++,c=c&&"disabled"===d);(b>1||!c)&&this._refresh()},_mouseCapture:function(){return!this.options.disabled},_mouseStart:function(a){this.offset=this.element.offset(),this.offset.left-=document.documentElement.scrollLeft||document.body.scrollLeft,this.offset.top-=document.documentElement.scrollTop||document.body.scrollTop,this.lastPoint=[this._round(a.clientX-this.offset.left),this._round(a.clientY-this.offset.top)],this.curLine=[this.lastPoint],this.lines.push(this.curLine)},_mouseDrag:function(a){var b=[this._round(a.clientX-this.offset.left),this._round(a.clientY-this.offset.top)];this.curLine.push(b),this.ctx.beginPath(),this.ctx.moveTo(this.lastPoint[0],this.lastPoint[1]),this.ctx.lineTo(b[0],b[1]),this.ctx.stroke(),this.lastPoint=b},_mouseStop:function(a){1===this.curLine.length&&(a.clientY+=this.options.thickness,this._mouseDrag(a)),this.lastPoint=null,this.curLine=null,this._changed(a)},_round:function(a){return Math.round(100*a)/100},toJSON:function(){return'{"lines":['+a.map(this.lines,function(b){return"["+a.map(b,function(a){return"["+a+"]"})+"]"})+"]}"},toSVG:function(){var b=this.options.svgStyles?'style="fill: '+this.options.background+';"':'fill="'+this.options.background+'"',c=this.options.svgStyles?'style="fill: none; stroke: '+this.options.color+"; stroke-width: "+this.options.thickness+';"':'fill="none" stroke="'+this.options.color+'" stroke-width="'+this.options.thickness+'"';return'<?xml version="1.0"?>\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<svg xmlns="http://www.w3.org/2000/svg" width="15cm" height="15cm">\n\t<g '+b+'>\n\t\t<rect x="0" y="0" width="'+this.canvas.width+'" height="'+this.canvas.height+'"/>\n\t\t<g '+c+">\n"+a.map(this.lines,function(b){return'\t\t\t<polyline points="'+a.map(b,function(a){return a+""}).join(" ")+'"/>\n'}).join("")+"\t\t</g>\n\t</g>\n</svg>\n"},toDataURL:function(a,b){return this.canvas.toDataURL(a,b)},draw:function(a){this.options.disabled||(this.clear(!0),"string"==typeof a&&0===a.indexOf("data:")?this._drawDataURL(a,this.options.scale):"string"==typeof a&&a.indexOf("<svg")>-1?this._drawSVG(a,this.options.scale):this._drawJSON(a,this.options.scale),this._changed())},_drawJSON:function(b,c){"string"==typeof b&&(b=a.parseJSON(b)),this.lines=b.lines||[];var d=this.ctx;a.each(this.lines,function(){d.beginPath(),a.each(this,function(a){d[0===a?"moveTo":"lineTo"](this[0]*c,this[1]*c)}),d.stroke()})},_drawSVG:function(b,c){var d=this.lines=[];a(b).find("polyline").each(function(){var b=[];a.each(a(this).attr("points").split(" "),function(a,c){var d=c.split(",");b.push([parseFloat(d[0]),parseFloat(d[1])])}),d.push(b)});var e=this.ctx;a.each(this.lines,function(){e.beginPath(),a.each(this,function(a){e[0===a?"moveTo":"lineTo"](this[0]*c,this[1]*c)}),e.stroke()})},_drawDataURL:function(a,b){var c=new Image,d=this.ctx;c.onload=function(){d.drawImage(this,0,0,c.width*b,c.height*b)},c.src=a},isEmpty:function(){return 0===this.lines.length},_destroy:function(){this.element.removeClass(this.widgetFullName||this.widgetBaseClass),a(this.canvas).remove(),this.canvas=this.ctx=this.lines=null,this._mouseDestroy()}};a.Widget.prototype._destroy||a.extend(b,{destroy:function(){this._destroy(),a.Widget.prototype.destroy.call(this)}}),a.Widget.prototype._getCreateOptions===a.noop&&a.extend(b,{_getCreateOptions:function(){return a.metadata&&a.metadata.get(this.element[0])[this.widgetName]}}),a.widget("kbw.signature",a.ui.mouse,b),a.kbw.signature.options=a.kbw.signature.prototype.options}(jQuery);
//# sourceMappingURL=jquery.signature.min.map