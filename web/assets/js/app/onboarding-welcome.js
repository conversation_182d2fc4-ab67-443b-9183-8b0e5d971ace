let _validations = [];

$(document).ready(function() {
    var _wizardEl = KTUtil.getById('kt_wizard');

    var fieldsStep2 = {
        company: {
            validators: {
                notEmpty: {
                    message: __('Merci d\'indiquer le nom de votre société')
                }
            }
        },
    };
    _validations[2] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep2,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );
});
