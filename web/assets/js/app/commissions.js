$(document).ready(function() {
    $('#pre_tax_amount').on('input', function () {
        $('#amount_tax_incl').val(getNewAmount());
    });
    $('#vat_percent').on('input', function () {
        $('#amount_tax_incl').val(getNewAmount());
    });

    // Commissions select
    $('.commission-checkbox').on('change', function() {
        $('#pre_tax_amount').val(getCommissionsAmount());
        $('#pre_tax_amount').trigger('input');
    });
    $('.commissions-check-all').on('change', function() {
        let checked = $(this).prop('checked');
        $('.commission-checkbox').prop('checked', checked);
        if (checked === true) {
            $('.commission-checkbox').parent().addClass('checked');
        } else {
            $('.commission-checkbox').parent().removeClass('checked');
        }
        $('.commission-checkbox').trigger('change');
    });
});

function getNewAmount() {
    let amount = $('#pre_tax_amount').val();
    let vat = $('#vat_percent').val();
    let ttcAmount = parseFloat(amount);
    if (vat !== '' && vat !== 0) {
        ttcAmount = ttcAmount + parseFloat((vat * amount) / 100);
    }

    return ttcAmount.toFixed(2);
}

function getCommissionsAmount() {
    let amount = 0;
    $('.commission-checkbox:checked').each(function () {
        amount += parseFloat($(this).data('amount'));
    });
    return amount.toFixed(2);
}
