function displayVersions() {
	$('#main-navigation .navi-link').removeClass('active');
	$('#main-navigation .navi-link-versions').addClass('active');

	$('#notifications').addClass('d-none');
	$('#news').addClass('d-none');
	$('#versions').removeClass('d-none');

	$.post(
		baseDir + '/ajax/versions/setView/',
		{ async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) { },
		'json'
	);
}

function displayNews() {
	$('#main-navigation .navi-link').removeClass('active');
	$('#main-navigation .navi-link-news').addClass('active');

	$('#notifications').addClass('d-none');
	$('#news').removeClass('d-none');
	$('#versions').addClass('d-none');
	$('.navi-nb-news').remove();

	$.post(
		baseDir + '/ajax/news/setView/',
		{ async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) { },
		'json'
	);
}

function displayNotifications() {
	$('#main-navigation .navi-link').removeClass('active');
	$('#main-navigation .navi-link-notifications').addClass('active');

	$('#notifications').removeClass('d-none');
	$('#news').addClass('d-none');
	$('#versions').addClass('d-none');
}

function displayNotification(idNotification) {
	$('#notifications .notification').addClass('d-none');

	$('#notifications .notification-inbox').removeClass('active');
	$('#notifications .nav-notification-' + idNotification).addClass('active');

	if ($('#notifications #notification_' + idNotification).html()) {
		$('#notifications #notification_' + idNotification).removeClass('d-none');
		return;
	}

	if ($('#notifications .nav-notification-' + idNotification).hasClass('new')) {
		var nbNotifications = $('.nb-notifications').data('nb-notifications');
		nbNotifications--;
		$('.nb-notifications').html(nbNotifications);
		$('.nb-notifications').data('nb-notifications', nbNotifications);
	}

	KTApp.block('#notifications .nav-notification-' + idNotification, {});
	$.post(
		baseDir + '/ajax/notification/get/',
		{ idNotification: idNotification, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#notifications .nav-notification-' + idNotification);
			if (data.status) {
				$('#notifications .nav-notification-' + idNotification).removeClass('new');
				$('#notifications #notification_' + idNotification).html(data.content).removeClass('d-none');
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}

function deleteNotification(idNotification) {
	KTApp.block('#notifications .nav-notification-' + idNotification, {});
	$.post(
		baseDir + '/ajax/notification/delete/',
		{ idNotification: idNotification, async: false, CSRFGuard_token:CSRFGuard_token },
		function(data) {
			KTApp.unblock('#notifications .nav-notification-' + idNotification);
			if (data.status) {
				$('#notifications #notification_' + idNotification).remove();
				$('#notifications .nav-notification-' + idNotification).remove();

				if (!$('.notification-inbox').length) {
					$('#notifications_container').addClass('d-none');
					$('.notifications-blank-state').removeClass('d-none');
				}
			} else {
				ShowToast('error', data.message);
			}
		},
		'json'
	);
}
