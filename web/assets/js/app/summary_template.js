$(document).ready(function() {
    if ($('#btnGenerateSummary').length) {
        $('#btnGenerateSummary').on('click', function () {
            let templateId = $('select#summary_template_id').val();
            if (!templateId) {
                return;
            }

            let recordingId = $('#formGenerateSummary #recording_id').val();

            KTApp.block('#formGenerateSummary', {});
            $.post(
                baseDir + '/ajax/summary/generate/',
                {summaryTemplateId: templateId, recordingId: recordingId, CSRFGuard_token: CSRFGuard_token},
                function (data) {
                    KTApp.unblock('#formGenerateSummary');
                    if (data.status) {
                        window.location.reload();
                    } else {
                        ShowToast('error', data.message);
                    }
                },
                'json'
            );
        });
    }
});