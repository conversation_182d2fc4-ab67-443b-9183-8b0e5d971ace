$(document).ready(function() {
    initCalendar();
    initCustomCalendar();
});

var calendar; // Variable globale pour le calendrier FullCalendar
var currentDate = new Date(); // Date actuelle pour le calendrier manuel

function initCalendar() {
    var calendarEl = document.getElementById('kt_calendar');
    calendar = new FullCalendar.Calendar(calendarEl, {
        //plugins: [ 'bootstrap', 'interaction', 'dayGrid', 'timeGrid', 'list' ],

        /*themeSystem: 'bootstrap',*/
        locale: 'fr',
        firstDay: 1,
        initialView: 'timeGridDay', // Changé de 'listWeek' à 'timeGridDay'
        height: 600,
        contentHeight: 480,
        aspectRatio: 3,  // see: https://fullcalendar.io/docs/aspectRatio
        slotMinTime: '06:00:00',
        slotMaxTime: '23:59:59',

        headerToolbar: {
            start: 'title',
            end: 'prev,next today',
        },

        nowIndicator: true,
        scrollTime: moment().format("HH:mm:ss"),
        scrollTimeReset: false,

        views: {
            dayGridMonth: { buttonText: __('Mois') },
            timeGridWeek: { buttonText: __('Semaine') },
            timeGridDay: { buttonText: __('Jour') },
            listWeek: { buttonText: __('Liste') }
        },
        buttonText: {
            today: __('Aujourd\'hui'),
        },

        navLinks: true,

        dayMaxEventRows: true, // for all non-TimeGrid views

        events: {
            url: baseDir + '/ajax/calendar/events/',
            method: 'POST',
            extraParams: {
                CSRFGuard_token: CSRFGuard_token
            },
            failure: function() {
                alert('There was an error while fetching events!');
            },
            success: function(data) {
                //console.log(data);
            },
        },

        loading: function( isLoading ) {
            if (isLoading) {
                KTApp.block('.fc-view', {});
            } else {
                KTApp.unblock('.fc-view');
            }
        },

        allDayText: __('Journée'),

        eventDidMount: function(arg) {
            var event = arg.event;
            var element = $(arg.el);
            if (event.extendedProps && event.extendedProps.description) {
                if (element.hasClass('fc-daygrid-event')) {
                    element.data('content', event.extendedProps.description);
                    element.data('html', true);
                    element.data('placement', 'top');
                    KTApp.initPopover(element);
                } else if (element.hasClass('fc-timegrid-event')) {
                    //element.find('.fc-event-title').append('<div class="fc-description">' + event.extendedProps.description + '</div>');
                    element.data('content', event.extendedProps.description);
                    element.data('html', true);
                    element.data('placement', 'top');
                    KTApp.initPopover(element);
                } else if (element.find('.fc-list-event-title').length !== 0) {
                    //element.find('.fc-list-event-title').append('<div class="fc-description">' + event.extendedProps.description + '</div>');
                    element.data('content', event.extendedProps.description);
                    element.data('html', true);
                    element.data('placement', 'top');
                    KTApp.initPopover(element);
                }
            }
        }
    });

    calendar.render();
}

// Fonctions pour le calendrier manuel
function initCustomCalendar() {
    renderCustomCalendar();

    // Événements pour les boutons de navigation
    document.getElementById('prev_month').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        renderCustomCalendar();
    });

    document.getElementById('next_month').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        renderCustomCalendar();
    });
}

function renderCustomCalendar() {
    const monthNames = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    // Mettre à jour le titre du mois
    document.getElementById('current_month_year').textContent =
        monthNames[currentDate.getMonth()] + ' ' + currentDate.getFullYear();

    // Générer les jours du mois
    generateCalendarDays();

    //get events
    $.post(
        baseDir + '/ajax/app/calendar/home/<USER>/days/',
        { month: currentDate.getMonth(), year: currentDate.getFullYear(), CSRFGuard_token: CSRFGuard_token },
        function(data) {
            if (data.status) {
                for (let i = 0; i < data.days.length; i++) {
                    const dayElement = document.querySelector('.calendar-day[data-date="' + data.days[i] + '"]');
                    if (dayElement) {
                        dayElement.classList.add('has-events');
                    }
                }
            }
        },
        'json'
    );

    // Sélectionner automatiquement le jour d'aujourd'hui si on est sur le mois actuel
    const today = new Date();
    if (currentDate.getMonth() === today.getMonth() &&
        currentDate.getFullYear() === today.getFullYear()) {
        const todayElement = document.querySelector('.calendar-day.today');
        if (todayElement) {
            selectDay(todayElement);
        }
    }
}

function generateCalendarDays() {
    const daysContainer = document.getElementById('custom_calendar_days');
    daysContainer.innerHTML = '';

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Premier jour du mois
    const firstDay = new Date(year, month, 1);
    // Dernier jour du mois
    const lastDay = new Date(year, month + 1, 0);

    // Jour de la semaine du premier jour (0 = dimanche, 1 = lundi, etc.)
    let startDay = firstDay.getDay();
    // Convertir pour que lundi = 0
    startDay = startDay === 0 ? 6 : startDay - 1;

    // Créer les cellules vides pour les jours avant le premier du mois
    for (let i = 0; i < startDay; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day empty';
        daysContainer.appendChild(emptyDay);
    }

    // Créer les cellules pour tous les jours du mois
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = day;
        dayElement.setAttribute('data-date', year + '-' +
            String(month + 1).padStart(2, '0') + '-' +
            String(day).padStart(2, '0'));

        // Marquer le jour actuel
        const today = new Date();
        if (year === today.getFullYear() &&
            month === today.getMonth() &&
            day === today.getDate()) {
            dayElement.classList.add('today');
        }

        // Ajouter l'événement de clic
        dayElement.addEventListener('click', function() {
            selectDay(this);
        });

        daysContainer.appendChild(dayElement);
    }
}

function selectDay(dayElement) {
    // Retirer la sélection précédente
    document.querySelectorAll('.calendar-day.selected').forEach(el => {
        el.classList.remove('selected');
    });

    // Ajouter la sélection au jour cliqué
    dayElement.classList.add('selected');

    // Obtenir la date sélectionnée
    const selectedDate = dayElement.getAttribute('data-date');

    // Mettre à jour le calendrier FullCalendar pour afficher cette date
    if (calendar) {
        calendar.gotoDate(selectedDate);
    }
}
