let calendar;

$(document).ready(function() {
    initCalendar();
});

//main function to initiate the module
function initCalendar() {
    let calendarEl = document.getElementById('kt_calendar');
    let calendarId = $('#kt_calendar').data('calendar-id');

    calendar = new FullCalendar.Calendar(calendarEl, {
        //plugins: [ 'bootstrap', 'interaction', 'dayGrid', 'timeGrid', 'list' ],

        /*themeSystem: 'bootstrap',*/
        locale: 'fr',
        firstDay: 1,
        initialView: 'timeGridWeek',
        height: 800,
        contentHeight: 780,
        aspectRatio: 3,  // see: https://fullcalendar.io/docs/aspectRatio
        slotMinTime: '06:00:00',
        slotMaxTime: '23:59:59',

        headerToolbar: {
            start: 'prev,next today',
            center: 'title',
            end: 'timeGridDay,timeGridWeek,dayGridMonth,listWeek'
        },

        nowIndicator: true,
        editable: true,

        views: {
            dayGridMonth: { buttonText: __('Mois') },
            timeGridWeek: { buttonText: __('Semaine') },
            timeGridDay: { buttonText: __('Jour') },
            listWeek: { buttonText: __('Liste') }
        },
        buttonText: {
            today: __('Aujourd\'hui'),
        },

        navLinks: true,

        dayMaxEventRows: true, // for all non-TimeGrid views

        events: {
            url: baseDir + '/ajax/calendar/availabilities/',
            method: 'POST',
            extraParams: {
                CSRFGuard_token: CSRFGuard_token,
                calendarId: calendarId
            },
            failure: function(data) {
                ShowToast('error', data.xhr.responseText);
            },
            success: function(data) {
                //console.log(data);
            },
        },

        eventOverlap: (calendarId ? false : true),
        selectable: true,
        selectMirror: true,
        selectOverlap: (calendarId ? false : true),
        selectConstraint: {
            startTime: '00:00',
            endTime: '24:00',
        },
        eventConstraint: {
            startTime: '00:00',
            endTime: '24:00',
        },

        eventDrop: function(eventDropInfo) {
            $.post(
                baseDir + '/ajax/calendar/availability/update/' + eventDropInfo.event.id + '/',
                { CSRFGuard_token: CSRFGuard_token, start: eventDropInfo.event.start.getTime(), end: eventDropInfo.event.end.getTime() },
                function(result) {
                    if (result.status) {
                        if (result.redirect) {
                            window.location.href = result.redirect;
                        }
                    } else {
                        ShowToast('error', result.message);
                        eventDropInfo.revert();
                    }
                },
                'json'
            );
        },

        eventResize: function(eventResizeInfo) {
            $.post(
                baseDir + '/ajax/calendar/availability/update/' + eventResizeInfo.event.id + '/',
                { CSRFGuard_token: CSRFGuard_token, start: eventResizeInfo.event.start.getTime(), end: eventResizeInfo.event.end.getTime() },
                function(result) {
                    if (result.status) {
                        if (result.redirect) {
                            window.location.href = result.redirect;
                        }
                    } else {
                        ShowToast('error', result.message);
                        eventResizeInfo.revert();
                    }
                },
                'json'
            );
        },

        select: function (info) {
            if (calendarId) {
                addAvailability(info, calendarId);
            } else {
                displayModalCalendars(info);
            }
        },

        eventClick: function(info) {
            var target = $(info.jsEvent.target);
            if (target.hasClass('fc-remove') || target.hasClass('fc-remove-icon')) {
                $.post(
                    baseDir + '/ajax/calendar/availability/remove/' + info.event.id + '/',
                    { CSRFGuard_token: CSRFGuard_token },
                    function(result) {
                        if (result.status) {
                            if (result.redirect) {
                                window.location.href = result.redirect;
                            } else {
                                info.event.remove();
                            }
                        } else {
                            ShowToast('error', result.message)
                            calendar.unselect();
                        }
                    },
                    'json'
                );
            } else {
                window.location.href = baseDir + '/app/calendar/availability/edit/' + info.event.id + '/' + info.event.extendedProps.calendar_id + '/';
            }
        },

        eventDidMount: function(arg) {
            let element = $(arg.el);
            if (element.hasClass('fc-daygrid-event')) {
                element.find('.fc-event-title-container').append('<div class="fc-remove"><i class="fas fa-times fc-remove-icon"></i></div>');
            } else if (element.hasClass('fc-timegrid-event')) {
                element.find('.fc-event-time').append('<div class="fc-remove"><i class="fas fa-times fc-remove-icon"></i></div>');
            } else if (element.find('.fc-list-item-title').length !== 0) {
                element.find('.fc-list-item-title').append('<div class="fc-remove"><i class="fas fa-times fc-remove-icon"></i></div>');
            }
        },

        loading: function( isLoading ) {
            if (isLoading) {
                KTApp.block('.fc-view', {});
            } else {
                KTApp.unblock('.fc-view');
            }
        },

        allDayText: __('Journée'),
    });

    calendar.render();
}

function addAvailability(info, calendarId) {
    KTApp.block('#ModalCalendar', {});
    let recurring = $('#ModalCalendar #recurring').prop('checked');
    if (!recurring) {
        recurring = ''; //otherwise recurring = 'false'
    }
    $.post(
        baseDir + '/ajax/calendar/availability/add/' + calendarId + '/',
        {CSRFGuard_token: CSRFGuard_token, start: info.start.getTime(), end: info.end.getTime(), recurring:recurring},
        function (result) {
            KTApp.unblock('#ModalCalendar');
            if (result.status) {
                $('#ModalCalendar').modal('hide');
                $('#ModalCalendar #recurring').prop('checked', false);
                let eventData = {
                    id: result.id,
                    title: __('Disponible'),
                    start: new Date(result.start * 1000),
                    end: new Date(result.end * 1000),
                    allDay: info.allDay,
                    extendedProps: {
                        calendar_id: result.calendar_id
                    }
                };
                if (result.backgroundColor) {
                    eventData.backgroundColor = result.backgroundColor;
                }
                if (result.borderColor) {
                    eventData.borderColor = result.borderColor;
                }
                if (result.textColor) {
                    eventData.textColor = result.textColor;
                }
                calendar.addEvent(eventData, true);
                calendar.unselect();
            } else {
                ShowToast('error', result.message)
                calendar.unselect();
            }
        },
        'json'
    );
}

function displayModalCalendars(info) {
    $('#ModalCalendar').modal('hide');
    $('#ModalCalendar .btn').unbind('click');
    $('#ModalCalendar .btn').bind('click', function() {
        addAvailability(info, $(this).data('calendar-id'));
    });
    $('#ModalCalendar').modal('show');
}
