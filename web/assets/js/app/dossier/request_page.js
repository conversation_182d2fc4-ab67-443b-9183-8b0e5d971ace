$(document).ready(function() {
    /*$(document).on('click', '.btn-prev-field', function() {
        let currentFieldId = parseInt($('div[data-wizard-state="current"] .card-field.active').data('field-id'));
        if (currentFieldId <= 0) {
            return;
        }

        let fieldsIds = [];
        $('div[data-wizard-state="current"] .card-field').not('.d-none').each(function() {
            let fieldId = parseInt($(this).data('field-id'));
            if (fieldId >= currentFieldId) {
                return;
            }
            fieldsIds.push(fieldId);
        });

        //sort fieldsIds DESC
        fieldsIds.sort(function(a, b) {
            return b - a;
        });

        let prevFieldId = fieldsIds[0];

        $('div[data-wizard-state="current"] .card-field[data-field-id="' + prevFieldId + '"]').addClass('active').removeClass('done inactive hidden');
        $('div[data-wizard-state="current"] .card-field[data-field-id="' + currentFieldId + '"]').addClass('inactive').removeClass('active');

        $('div[data-wizard-state="current"] .card-field').not('.d-none').each(function() {
            let fieldId = parseInt($(this).data('field-id'));
            if (fieldId <= currentFieldId) {
                return;
            }
            $('div[data-wizard-state="current"] .card-field[data-field-id="' + fieldId + '"]').addClass('hidden').removeClass('active inactive');
        });
    });

    $(document).on('click', '.btn-next-field', function() {
        let currentFieldId = parseInt($('div[data-wizard-state="current"] .card-field.active').data('field-id'));

        let fieldsIds = [];
        $('div[data-wizard-state="current"] .card-field').not('.d-none').each(function() {
            let fieldId = parseInt($(this).data('field-id'));
            if (fieldId <= currentFieldId) {
                return;
            }
            fieldsIds.push(fieldId);
        });
        let nextFieldId = fieldsIds[0];
        if (typeof nextFieldId === 'undefined') {
            return;
        }

        $('div[data-wizard-state="current"] .card-field[data-field-id="' + nextFieldId + '"]').addClass('active').removeClass('inactive');
        $('div[data-wizard-state="current"] .card-field[data-field-id="' + currentFieldId + '"]').addClass('done').removeClass('active');

        let nextFieldId1 = fieldsIds[1];
        if (typeof nextFieldId1 !== 'undefined' && $('div[data-wizard-state="current"] .card-field[data-field-id="' + nextFieldId1 + '"]').length) {
            $('div[data-wizard-state="current"] .card-field[data-field-id="' + nextFieldId1 + '"]').addClass('inactive').removeClass('active hidden');
        }
    });*/

    $(document).on('click', '.btn-approve', function() {
        let slug = $(this).data('slug');
        let requestUserPageId = $('#request_user_page_id').val();
        KTApp.block('#card_' + slug, {});
        $.post(
            baseDir + '/ajax/request/answer/approve/',
            { slug: slug, requestUserPageId: requestUserPageId, CSRFGuard_token: CSRFGuard_token },
            function (result) {
                KTApp.unblock('#card_' + slug);
                if (result.status) {
                    $('#card_' + slug + ' .card-footer').html(result.content);
                } else {
                    ShowToast('error', result.message);
                }
            },
            'json'
        );
    });

    $(document).on('click', '.btn-unapprove', function() {
        let slug = $(this).data('slug');
        let requestUserPageId = $('#request_user_page_id').val();
        KTApp.block('#card_' + slug, {});
        $.post(
            baseDir + '/ajax/request/answer/unapprove/',
            { slug: slug, requestUserPageId: requestUserPageId, CSRFGuard_token: CSRFGuard_token },
            function (result) {
                KTApp.unblock('#card_' + slug);
                if (result.status) {
                    $('#card_' + slug + ' .card-footer').html(result.content);
                } else {
                    ShowToast('error', result.message);
                }
            },
            'json'
        );
    });

    $(document).on('click', '.btn-refuse-add-comment', function() {
        let slug = $(this).data('slug');
        $('#' + slug + '_comment_container').removeClass('d-none');
    });

    $(document).on('click', '.btn-refuse-cancel-comment', function() {
        let slug = $(this).data('slug');
        $('#' + slug + '_comment_container').addClass('d-none');
    });

    $(document).on('click', '.btn-refuse', function() {
        let slug = $(this).data('slug');
        let comment = $('#' + slug + '_comment').val();
        let requestUserPageId = $('#request_user_page_id').val();
        KTApp.block('#card_' + slug, {});
        $.post(
            baseDir + '/ajax/request/answer/refuse/',
            { slug: slug, comment: comment, requestUserPageId: requestUserPageId, CSRFGuard_token: CSRFGuard_token },
            function (result) {
                KTApp.unblock('#card_' + slug);
                if (result.status) {
                    $('#' + slug + '_comment').val('');
                    $('#card_' + slug + ' .card-footer').html(result.content);
                } else {
                    ShowToast('error', result.message);
                }
            },
            'json'
        );
    });
});

/**
 * Gestionnaire des transitions de cards avec animations fluides
 */
class CardTransitionManager {
    constructor(containerSelector = 'div[data-wizard-state="current"] .cards-container') {
        this.container = document.querySelector(containerSelector);
        this.cards = [];
        this.currentStep = 0;
        this.isAnimating = false;

        this.init();
    }

    init() {
        if (!this.container) {
            console.error('Container non trouvé');
            return;
        }

        this.cards = Array.from(this.container.querySelectorAll('.card-field'));
        this.setupInitialState();
        this.bindEvents();
    }

    setupInitialState() {
        this.cards.forEach((card, index) => {
            // Nettoyer les classes existantes
            card.classList.remove('active', 'inactive', 'done', 'inactive-to-active', 'active-to-done');

            if (index === 0) {
                card.classList.add('active');
            } else if (index === 1) {
                card.classList.add('inactive');
            } else {
                // Les autres cards sont cachées
                card.style.display = 'none';
            }
        });

        this.currentStep = 0;
    }

    bindEvents() {
        // Écouteur pour les boutons "Suivant"
        $(document).on('click', '.btn-next-field', (e) => {
            e.preventDefault();
            this.nextStep();
        });

        // Écouteur pour les boutons "Précédent" (optionnel)
        $(document).on('click', '.btn-prev-field', (e) => {
            e.preventDefault();
            this.prevStep();
        });
    }

    nextStep() {
        if (this.isAnimating || this.currentStep >= this.cards.length - 1) {
            return;
        }

        this.isAnimating = true;

        const currentCard = this.cards[this.currentStep];
        const nextCard = this.cards[this.currentStep + 1];
        const futureCard = this.cards[this.currentStep + 2];

        // Animation de la card actuelle vers "done"
        currentCard.classList.remove('active');
        currentCard.classList.add('active-to-done');

        // Animation de la card suivante vers "active"
        if (nextCard) {
            nextCard.classList.remove('inactive');
            nextCard.classList.add('inactive-to-active');
        }

        // Préparer la card future comme "inactive"
        if (futureCard) {
            futureCard.style.display = 'block';
            futureCard.classList.add('inactive');
        }

        // Attendre la fin des animations
        setTimeout(() => {
            // Finaliser les états
            currentCard.classList.remove('active-to-done');
            currentCard.classList.add('done');

            if (nextCard) {
                nextCard.classList.remove('inactive-to-active');
                nextCard.classList.add('active');
            }

            this.currentStep++;
            this.isAnimating = false;

            // Déclencher un événement personnalisé
            this.dispatchStepChangeEvent();

        }, 600); // Durée de l'animation CSS
    }

    prevStep() {
        if (this.isAnimating || this.currentStep <= 0) {
            return;
        }

        this.isAnimating = true;

        const currentCard = this.cards[this.currentStep];
        const prevCard = this.cards[this.currentStep - 1];
        const futureCard = this.cards[this.currentStep + 1];

        // Animation inverse
        currentCard.classList.remove('active');
        currentCard.classList.add('active-to-inactive');

        if (prevCard) {
            prevCard.classList.remove('done');
            prevCard.classList.add('done-to-active');
        }

        // Cacher la card future
        if (futureCard) {
            futureCard.classList.remove('inactive');
            futureCard.style.display = 'none';
        }

        setTimeout(() => {
            // Finaliser les états
            currentCard.classList.remove('active-to-inactive');
            currentCard.classList.add('inactive');

            if (prevCard) {
                prevCard.classList.remove('done-to-active');
                prevCard.classList.add('active');
            }

            this.currentStep--;
            this.isAnimating = false;
            this.dispatchStepChangeEvent();
        }, 600);
    }

    goToStep(stepIndex) {
        if (this.isAnimating || stepIndex < 0 || stepIndex >= this.cards.length) {
            return;
        }

        if (stepIndex > this.currentStep) {
            // Aller vers l'avant
            const stepsToGo = stepIndex - this.currentStep;
            let currentStepCount = 0;

            const nextStepInterval = setInterval(() => {
                this.nextStep();
                currentStepCount++;

                if (currentStepCount >= stepsToGo) {
                    clearInterval(nextStepInterval);
                }
            }, 700); // Délai entre chaque étape

        } else if (stepIndex < this.currentStep) {
            // Aller vers l'arrière
            const stepsToGo = this.currentStep - stepIndex;
            let currentStepCount = 0;

            const prevStepInterval = setInterval(() => {
                this.prevStep();
                currentStepCount++;

                if (currentStepCount >= stepsToGo) {
                    clearInterval(prevStepInterval);
                }
            }, 400);
        }
    }

    dispatchStepChangeEvent() {
        const event = new CustomEvent('stepChanged', {
            detail: {
                currentStep: this.currentStep,
                totalSteps: this.cards.length,
                currentCard: this.cards[this.currentStep]
            }
        });

        this.container.dispatchEvent(event);
    }

    // Méthodes utilitaires
    getCurrentStep() {
        return this.currentStep;
    }

    getTotalSteps() {
        return this.cards.length;
    }

    isLastStep() {
        return this.currentStep === this.cards.length - 1;
    }

    isFirstStep() {
        return this.currentStep === 0;
    }

    reset() {
        this.setupInitialState();
    }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
    // Créer une instance globale
    window.cardTransitionManager = new CardTransitionManager();

    // Écouteur pour les événements de changement d'étape
    document.addEventListener('stepChanged', function(e) {
        console.log('Étape changée:', e.detail);

        // Vous pouvez ajouter ici d'autres actions :
        // - Mise à jour d'une barre de progression
        // - Sauvegarde automatique
        // - Validation des données
        // - etc.

        updateProgressBar(e.detail.currentStep, e.detail.totalSteps);
    });
});

// Fonction utilitaire pour mettre à jour une barre de progression
function updateProgressBar(currentStep, totalSteps) {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        const percentage = ((currentStep + 1) / totalSteps) * 100;
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
    }
}

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CardTransitionManager;
}
