let sortableEnabled = false;

$(document).ready(function () {
    if ($('#notes .note').length) {
        sortNotes();
    }

    $(document).on('click', '.note-labels .label', function () {
        $('.note-labels .label').removeClass('active');
        $(this).addClass('active');
        $('.note-labels #label').val($(this).data('label'));
    });
});

$.fn.startBootstrapSuggest = function () {
    if (typeof users != 'undefined') {
        $(this).suggest('@', {
            data: users,
            map: function (user) {
                return {
                    value: user.username,
                    text: '<img src="'+user.avatar+'"><strong>'+user.username+'</strong> <small>'+user.fullname+'</small>'
                }
            }
        });
    }
}

function AddNote()
{
    $('#create_note #btn-validate').attr('disabled', true);
    KTApp.block('.tab-notes-form', {});

    let dossier_id = $('#create_note #dossier_id').val();
    let content = $('#create_note #note_content').val();
    let label = $('#create_note #label').val();

    $.post(
        baseDir + '/ajax/dossier/note/',
        {dossier_id: dossier_id, content: content, label:label, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            $('#create_note #btn-validate').attr('disabled', false);
            KTApp.unblock('.tab-notes-form');
            if (data.status) {
                if ($('#dossierNotes #noNoteInfo').length) {
                    $('#dossierNotes #noNoteInfo').remove();
                }
                $('#notes .timeline-items').prepend(data.note);
                $('#create_note')[0].reset();
                $('#create_note .label').removeClass('active');
                $('.tab-notes-form').hide();
                setTimeout(function() {
                    HideNoteForm();
                }, 2000);
                ShowToast('success', data.message);

                let nbNotes = parseInt($('.navi-link.active .navi-label span').text());
                nbNotes++;
                $('.navi-link.active .navi-label span').html(nbNotes);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function ShowAddNoteForm()
{
    let element = KTUtil.getById('noteform');
    let offcanvas = new KTOffcanvas(element);
    offcanvas.hide();

    $('#noteform form input[name="id_note"]').remove();
    $('#noteform #content').val('');
    $('#noteform .note-labels .label').removeClass('active');
    $('#noteform #label').val('');
    $('#noteform #content').startBootstrapSuggest();

    offcanvas.show();
}

function EditNote(id_note)
{
    let element = KTUtil.getById('noteform');
    let offcanvas = new KTOffcanvas(element);
    offcanvas.hide();

    $.post(
        baseDir + '/ajax/dossier/note/get/',
        {id_note: id_note, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                $('#noteform form').append('<input type="hidden" name="id" value="' + id_note + '">');
                $('#noteform #content').val(data.content);
                $('#noteform .note-labels .label').removeClass('active');
                $('#noteform #label').val('');
                if (data.label) {
                    $('#noteform .note-labels .label[data-label="'+data.label+'"]').addClass('active');
                    $('#noteform #label').val(data.label);
                }

                $('#noteform #content').startBootstrapSuggest();

                offcanvas.show();
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function UpdateNote(id_note)
{
    $('#update_note #btn-validate').attr('disabled', true);
    KTApp.block('.tab-notes-form', {});

    let content = $('#update_note #note_content').val();
    let label = $('#update_note #label').val();
    $.post(
        baseDir + '/ajax/dossier/note/update/',
        {id_note: id_note, content: content, label:label, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            $('#update_note #btn-validate').attr('disabled', false);
            KTApp.unblock('.tab-notes-form');
            if (data.status) {
                $('#note' + id_note).replaceWith(data.note);
                enableSortNotes();
                ShowToast('success', data.message);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function DeleteNote(id_note)
{
    $.post(
        baseDir + '/ajax/dossier/note/delete/',
        {id_note: id_note, async: false, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                $('#note' + id_note).remove();
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function sortNotes()
{
    if ($('#notes .note').length) {
        $('#notes').sortable({
            revert: true,
            items: '.note',
            handle: '.note-grab',
            axis: 'y',
            update: function (event, ui) {
                $.post(
                    baseDir + '/ajax/dossier/notes/positions/',
                    {notes: $(this).sortable('toArray'), CSRFGuard_token: CSRFGuard_token},
                    function (data) {
                        if (!data.status) {
                            ShowToast('error', data.message);
                        }
                    },
                    'json'
                );
            }
        });
        sortableEnabled = true;
    }
}

function disableSortNotes()
{
    if ($('#notes .note').length && sortableEnabled) {
        $('#notes').sortable('disable');
    }
}

function enableSortNotes()
{
    if ($('#notes .note').length) {
        if (!sortableEnabled) {
            sortNotes();
        } else {
            $('#notes').sortable('enable');
        }
    }
}

function displayDossierNotes(dossierId) {
    $('#modalNotes').remove();
    var modal = '<div class="modal fade" id="modalNotes" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
    modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
    modal += '<div class="modal-content">\n' +
        '            <div class="modal-header">\n' +
        '                <h5 class="modal-title" id="exampleModalLabel">' + __('Notes') + '</h5>\n' +
        '                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
        '                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
        '                </button>\n' +
        '            </div>\n' +
        '            <div class="modal-body"><div class="p-20 w-100 d-flex justify-content-center"><div class="spinner spinner-primary"></div></div></div>\n' +
        '            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
        '                <a class="btn btn-light-primary font-weight-bold" href="' + baseDir + '/app/dossier/notes/' + dossierId + '/">' + __('Ajouter une note') + '</a>\n' +
        '                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
        '            </div>\n' +
        '        </div>';

    $('body').append(modal);
    $('#modalNotes').modal('show');

    $.post(
        baseDir + '/ajax/dossier/notes/' + dossierId + '/',
        { CSRFGuard_token: CSRFGuard_token},
        function (data) {
            if (data.status) {
                $('#modalNotes .modal-body').html(data.content);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}
