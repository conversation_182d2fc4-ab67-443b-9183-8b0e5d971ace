let signature;
let signatureAdded = false;

function startJqSignature() {
    if (typeof signature !== 'undefined') {
        signature.jqSignature('clearCanvas');
        return;
    }

    signature = $('#ModalSignature .jq-signature').jqSignature({
        width: 600,
        height: 300,
        autoFit: true,
        border: '1px solid #0E2072',
        lineColor: '#111111',
        lineWidth: 2,
    });
    signature.on('jq.signature.changed', function() {
        let signatureDataUrl = signature.jqSignature('getDataURL');
        $('#ModalSignature #signature').val(signatureDataUrl);
    });
    $('#ModalSignature #jq-signature-clear').click(function(e) {
        e.preventDefault();
        signature.jqSignature('clearCanvas');
        $('#ModalSignature #signature').val('');
    });
}

function ModalSignature(dossierId, personId) {
    $('#ModalSignature #signature').val('');

    let name = $('#signature' + personId + ' h3.card-title').text();
    $('#ModalSignature .modal-title').text(name);

    $('#ModalSignature #existing-signature').html('');
    if ($('#existing-signature' + personId).length) {
        let content = '<div class="d-flex flex-row p-4 border rounded mb-8 justify-content-between">';
        content += '<div class="mr-4">';
        content += '<p>' + __('Vous pouvez utiliser la signature enregistrée pour signer ce document rapidement.') + '</p>';
        content += '<a class="btn btn-secondary" onclick="useSignature(' + dossierId + ', ' + personId + ');">' + __('Utiliser la signature') + '</a>';
        content += '</div>';
        content += '<div class="text-center">';
        content += '<img src="' + $('#existing-signature' + personId).data('url') + '" class="img-fluid max-h-100px">';
        content += '</div>';
        content += '</div>';
        $('#ModalSignature #existing-signature').html(content);
    }

    //add modal
    $('#ModalSignature').on('shown.bs.modal', function (e) {
        startJqSignature();
    });
    $('#ModalSignature').modal('show');

    $('#ModalSignature form').off('submit');
    $('#ModalSignature form').on('submit', function (e) {
        e.preventDefault();
        KTApp.block('#ModalSignature .modal-content', {});
        let signature = $('#ModalSignature #signature').val();
        let data = {
            dossier_id: dossierId,
            person_id: personId,
            signature: signature,
            CSRFGuard_token:CSRFGuard_token,
            async: false
        };
        $.post(
            baseDir + "/ajax/dossier/signature/",
            data,
            function (response) {
                KTApp.unblock('#ModalSignature .modal-content');
                if (response.status) {
                    signatureAdded = true;
                    $('#signature' + personId + ' h3.card-title').text(name + ' (signé)');
                    $('#signature' + personId + ' .card-toolbar .btn').removeClass('btn-primary').addClass('btn-light-primary');
                    $('#signature' + personId + ' .card-body').html('<img class="img-fluid" src="' + response.signature + '" alt="Signature de ' + name + '">');
                    $('#ModalSignature').modal('hide');
                } else {
                    ShowToast('error', response.message);
                }
            },
        'json');
    });
}

function useSignature(dossierId, personId) {
    $('#ModalSignature #signature').val('use-existing-signature');
    $('#ModalSignature form').submit();
}

function exitPage(dossierId, redirect) {
    if (!signatureAdded) {
        window.location.href = redirect;
    } else {
        $('#ModalExit').modal({backdrop: 'static', keyboard: false});
        generateDocument(dossierId, 'contradictory', redirect, JSON.stringify(params));
    }
}
