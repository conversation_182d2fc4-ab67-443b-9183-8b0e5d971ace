function toggleUserType(dossierId, userType) {
    KTApp.block('.user_type_select_content', {});
    $('.user_type_select_content').find('.btn').removeClass('active');
    $('.user_type_select_content').find('.btn.btn-' + userType).addClass('active');
    $.post(
        baseDir + '/ajax/dossier/invoice/user_type/',
        { dossierId: dossierId, userType: userType, CSRFGuard_token: CSRFGuard_token},
        function (data) {
            KTApp.unblock('.user_type_select_content');
            if (data.status) {
                $('#first_name').val(data.firstName);
                $('#last_name').val(data.lastName);
                $('#email').val(data.email);
                $('#telephone').val(data.telephone);
                $('#address').val(data.address);
                $('#address2').val(data.address2);
                $('#city').val(data.city);
                $('#zip').val(data.zip);
                $('#tva_intracom').val(data.vatNumber);
                $('#company').val(data.company);
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}
