let _validations = [];

$(document).ready(function() {
    var _wizardEl = KTUtil.getById('kt_wizard');

    var fieldsStep1 = {
        title: {
            validators: {
                notEmpty: {
                    message: __('<PERSON>rc<PERSON> d\'indiquer le nom du calendrier')
                }
            }
        },
    };
    _validations[1] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep1,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep2 = {
        'consulting_reasons_ids[]': {
            validators: {
                notEmpty: {
                    message: __('Merci de choisir un motif de consultation')
                },
            }
        },
    };
    _validations[2] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep2,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep3 = {
        type: {
            validators: {
                notEmpty: {
                    message: __('<PERSON>rc<PERSON> de sélectionner un type de calendrier')
                }
            }
        },
    };
    _validations[3] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep3,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );


    $('#consulting_reason_price_tax_excl').on('input', function() {
        CalcPrice('excl');
    });
    $('#consulting_reason_price_tax_incl').on('input', function() {
        CalcPrice('incl');
    });
    $('#consulting_reason_vat_rule_id').on('change', function() {
        CalcPrice('excl');
    });
});

function CalcPrice(type) {
    var vat = parseInt($('#consulting_reason_vat_rule_id').find(':selected').data('default-vat'));
    var priceTaxExcl = $('#consulting_reason_price_tax_excl').val();
    var priceTaxIncl = $('#consulting_reason_price_tax_incl').val();

    if (type == 'excl') {
        if (vat == 0) {
            priceTaxIncl = priceTaxExcl;
        } else {
            priceTaxIncl = priceTaxExcl*(1+vat/100);
        }
        priceTaxIncl  = parseFloat(priceTaxIncl);
        priceTaxIncl = roundUpto(priceTaxIncl, 3);
        $('#consulting_reason_price_tax_incl').val(priceTaxIncl);
    } else {
        if (vat == 0) {
            priceTaxExcl = priceTaxIncl;
        } else {
            priceTaxExcl = priceTaxIncl/(1+vat/100);
        }
        priceTaxExcl  = parseFloat(priceTaxExcl);
        priceTaxExcl = roundUpto(priceTaxExcl, 3);
        $('#consulting_reason_price_tax_excl').val(priceTaxExcl);
    }
}

function roundUpto(number, upto) {
    return Number(number.toFixed(upto));
}