let somethingChanged = false;
let currentHoveredIcon;
let hideMenuTimeout;

window.onbeforeunload = function() {
	if (somethingChanged) {
		return false;
	}
	return null;
}

$(document).ready(function () {
	$('#kt_aside').hover(function() {
		$('#kt_aside').addClass('open');
	}, function() {
		$('#kt_aside').removeClass('open');
	});

	$(document).on('mouseenter', '#kt_aside ul.menu-nav li a.has-children', function () {
		currentHoveredIcon = $(this);
		positionSidebarMenu(currentHoveredIcon);
	});

	$(document).on('mouseleave', '#kt_aside ul.menu-nav li a.has-children', function () {
		let menu = $(this).parent('li').find('div.menu-submenu');
		hideMenuTimeout = setTimeout(() => {
			menu.css('display', 'none');
		}, 100);
	});

	$(document).on('mouseenter', '#kt_aside ul.menu-nav li div.menu-submenu', function () {
		clearTimeout(hideMenuTimeout);
	});

	$(document).on('mouseleave', '#kt_aside ul.menu-nav li div.menu-submenu', function () {
		$(this).css('display', 'none');
	});

	$('.aside-nav').on('scroll', function () {
		if (currentHoveredIcon) {
			positionSidebarMenu(currentHoveredIcon);
		}
	});

	$('form').on('submit', function() {
		window.onbeforeunload = null;
		$(this).find('button[type="submit"]').addClass('spinner spinner-left').prop('disabled', true);
	});

	if (!$('#kt_wizard').length) {
		$('input[type="text"]').bind('change', function() {
			let id = $(this).attr('id');
			let filter = $(this).closest('.filters');
			if (id != 'kt_quick_search_input' && !filter.length) {
				somethingChanged = true;
				$('input[type="text"]').unbind('change');
			}
		});
	}
	$('.btn-cancel').on('click', function() {
		somethingChanged = false;
	});

	if ($('.btn-display').length) {
		$('.btn-display').on('click', function() {
			let name = $(this).data('name');
			let value = $(this).data('value');
			$('#' + name).val(value);
			$(this).closest('form').submit();
		});
	}
	if ($('select[name="sort"]').length) {
		$('select[name="sort"]').on('change', function() {
			$(this).closest('form').submit();
		});
	}

	/* ---------- ckeditor ---------- */
	if ($('.ckeditorsmall').length > 0) {
		$('.ckeditorsmall').each(function() {
			var ckeditorId = $(this).attr('id');
			ClassicEditor
				.create( document.querySelector('#' + ckeditorId), {
					toolbar: ['bold', 'italic', 'underline', 'link', 'bulletedList', '|', 'mediaEmbed'],
					mediaEmbed: {
						previewsInData: true
					}
				})
				.then( newEditor => {
					smallEditor = newEditor;
				} )
				.catch( error => {
					console.error( error );
				} );
		});
	}
	if ($(".ckeditor").length > 0) {
		$('.ckeditor').each(function() {
			var ckeditorId = $(this).attr('id');
			ClassicEditor
				.create( document.querySelector('#' + ckeditorId), {
					toolbar: ['heading', 'fontColor', 'fontSize', '|', 'alignment', '|', 'bold', 'italic', 'underline', 'strikethrough', 'link', 'bulletedList', 'numberedList', '|', 'indent', 'outdent', 'blockQuote', '|', 'mediaEmbed', 'ckfinder'],
					alignment: {
						options: ['left', 'center', 'right', 'justify']
					},
					ckfinder: {
						options: {
							resourceType: 'Medias'
						}
					},
					mediaEmbed: {
						previewsInData: true
					}
				})
				.then( newEditor => {
					editor = newEditor;
				} )
				.catch( error => {
					console.error( error );
				} );
		});
	}

	initDataTables();

	$('.date-time-picker').datetimepicker({
		format: 'DD/MM/YYYY HH:mm:ss',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en')
	});
	$('.date-picker').datetimepicker({
		format: 'DD/MM/YYYY',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en')
	});

	$('.date-mask-picker').each(function() {
		let options = {
			format: 'DD/MM/YYYY',
			locale: (appLanguage == 'fr_FR' ? 'fr' : 'en'),
			autoclose: true,
			useCurrent: false,
		};
		if ($(this).data('min-date')) {
			options.minDate = $(this).data('min-date');
		}
		$(this).datetimepicker(options);
	});
	$('.date-month-mask-picker').datetimepicker({
		format: 'MM/YYYY',
		locale: (appLanguage == 'fr_FR' ? 'fr' : 'en'),
		autoclose: true,
		useCurrent: false
	});

	$(".date-mask").each(function() {
		$(this).inputmask({
			"alias" : "datetime",
			"inputFormat": "dd/mm/yyyy",
			//autoUnmask: true
		});
	});
	$(".date-month-mask").each(function() {
		$(this).inputmask({
			"alias" : "datetime",
			"inputFormat": "mm/yyyy",
			//autoUnmask: true
		});
	});

	if ($('.date-picker-linked').length > 0) {
		var datePicker1 = $($('.date-picker-linked .date-picker')[0]).attr('id');
		var datePicker2 = $($('.date-picker-linked .date-picker')[1]).attr('id');
		$('#' + datePicker1).on('change.datetimepicker', function (e) {
			$('#' + datePicker2).datetimepicker('minDate', e.date);
		});
		$('#' + datePicker2).on('change.datetimepicker', function (e) {
			$('#' + datePicker1).datetimepicker('maxDate', e.date);
		});

		if ($('#' + datePicker1 + ' input').val()) {
			var value = $('#' + datePicker1 + ' input').val();
			$('#' + datePicker2).datetimepicker('minDate', moment().format(value));
		}
		if ($('#' + datePicker2 + ' input').val()) {
			var value = $('#' + datePicker2 + ' input').val();
			$('#' + datePicker1).datetimepicker('maxDate', moment().format(value));
		}
	}
	if ($('.date-time-range-picker').length) {
		let options = {
			buttonClasses: ' btn',
			applyClass: 'btn-primary',
			cancelClass: 'btn-secondary',
			timePicker: true,
			timePickerIncrement: 5,
			timePicker24Hour: true,
			locale: {
				format: 'DD/MM/YYYY HH:mm',
				applyLabel: __('Appliquer'),
				cancelLabel: __('Annuler'),
				monthNames: [
					"Janvier",
					"Février",
					"Mars",
					"Avril",
					"Mai",
					"Juin",
					"Juillet",
					"Août",
					"Septembre",
					"Octobre",
					"Novembre",
					"Décembre"
				],
			}
		};
		if ($('.date-time-range-picker .input').val()) {
			let value = $('.date-time-range-picker .input').val();
			value = value.split(' - ');
			options.startDate = value[0];
			options.endDate = value[1];
		}
		$('.date-time-range-picker').daterangepicker(options, function(start, end, label) {
			$('.date-time-range-picker .form-control').val(start.format('DD/MM/YYYY HH:mm') + ' - ' + end.format('DD/MM/YYYY HH:mm'));
		});
	}

	$('[data-rel="tooltip"]').tooltip()

	$('[data-rel="select2"],[rel="select2"]').each(function() {
		let options = {
			language: (appLanguage == 'fr_FR' ? "fr" : "en"),
		};
		if ($(this).data('placeholder')) {
			options.placeholder = $(this).data('placeholder');
		}
		$(this).select2(options);
	});

	function select2ChosenImg(img) {
		if (typeof img.element != 'undefined') {
			return $('<span><img src="'+ img.element.dataset.imgSrc + '"> '+ img.text +'</span>');
		}
	}

	$('[data-rel="select2-img"],[rel="select2-img"]').select2({
		templateSelection: select2ChosenImg,
		templateResult: select2ChosenImg,
		allowHtml: true
	}).on('select2:open', function (e) {
		$('.select2-search input').attr('placeholder', __('Rechercher'))
	});

	if ($('.image-input').length > 0) {
		$('.image-input').each(function() {
			var id = $(this).attr('id');
			new KTImageInput(id);
		});
	}

	if ($('.btn-create-modal-content').length > 0) {
		$('.btn-create-modal-content').on('click', function() {
			let image = $(this).data('image');
			let content = $(this).data('content');
			var customClass = 'swal2-lg';
			if ($(this).data('size')) {
				customClass = $(this).data('size');
			}
			Swal.fire({
				title: '',
				text: content,
				imageUrl: image,
				imageWidth: 600,
				animation: false,
				customClass: customClass
			});
		});
	}

	if ($('.form-panel-container').length > 0) {
		$('.form-panel-container').each(function() {
			let id = $(this).attr('id');
			let element = KTUtil.getById(id);
			new KTOffcanvas(element, {
				overlay: true,
				baseClass: 'offcanvas',
				placement: 'right',
				closeBy: id + '_close',
				toggleBy: id + '_toggle'
			});
		});
	}

	$('#kt_quick_search_dropdown_menu').on('shown.bs.dropdown', function() {
		$('#kt_quick_search_input').focus();
	});
});

function initDataTables() {
	let datatableLanguage = {
		searchPlaceholder: "Search",
		url: cdnDir + "assets/js/plugins/datatables/jquery.dataTables.english.json",
	}
	if (appLanguage == 'fr_FR') {
		datatableLanguage = {
			url: cdnDir + "assets/js/plugins/datatables/jquery.dataTables.french.json",
			searchPlaceholder: "Rechercher",
			select: {
				rows: {
					0: "",
					_: "%d éléments sélectionnés",
					1: "1 élément sélectionné"
				}
			}
		};
	}

	$('.datatable').each(function() {
		let sortColumn = 0;
		let sortOrder = 'desc';
		if ($(this).data('sort-column')) {
			sortColumn = $(this).data('sort-column');
		}
		if ($(this).data('sort-order')) {
			sortOrder = $(this).data('sort-order');
		}
		let options = {
			responsive: true,
			stateSave: true,
			dom: `<'row'<'col-sm-6'f><'col-sm-6'l>><'row'<'col-sm-12'tr>>
			<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 dataTables_pager'p>>`,
			aaSorting: [[ sortColumn, sortOrder ]],
			columnDefs: [
				{ orderable: false, targets: -1 }
			],
			language: datatableLanguage
		};
		$(this).DataTable(options);
	});
}

function CreateAlertSuccess(content, title) {
	if (!title) {
		title = __('Félicitations !');
	}
	Swal.fire(title, content, "success");
}

function CreateAlertError(content, title) {
	if (!title) {
		title = __('Erreur');
	}
	Swal.fire(title, content, "error");
}

function CreateConfirmationDeleteAlert(objectName, idObject, content, button, title, redirect) {
	if (!button) {
		button = __('Supprimer');
	}
	if (!title) {
		title = __('Confirmation');
	}
	Swal.fire({
		title: title,
		text: content,
		icon: "warning",
		showCancelButton: true,
		confirmButtonText: button,
		cancelButtonText: __('Annuler'),
		reverseButtons: true,
		confirmButtonColor: '#F64E60',
		iconColor: '#F64E60',
	}).then(function(result) {
		if (result.value) {
			if (redirect) {
				window.location.href = redirect;
			} else {
				$('#suppr_' + objectName + ' #' + objectName).val(idObject);
				$('#suppr_' + objectName).submit();
			}
		}
	});
}

function ShowToast(type, message) {
	toastr.options = {
		"closeButton": true,
		"debug": false,
		"newestOnTop": false,
		"progressBar": true,
		"positionClass": "toast-top-right",
		"preventDuplicates": false,
		"onclick": null,
		"showDuration": "300",
		"hideDuration": "1000",
		"timeOut": (type == 'error' ? "10000" : "5000"),
		"extendedTimeOut": "1000",
		"showEasing": "swing",
		"hideEasing": "linear",
		"showMethod": "fadeIn",
		"hideMethod": "fadeOut"
	};

	if (type == 'error') {
		toastr.error(message);
	} else if (type == 'success') {
		toastr.success(message);
	} else if (type == 'notice') {
		toastr.info(message);
	}
}

function uniqid(container) {
	var nom = $(this).val();

	var accent = [
		/[\300-\306]/g, /[\340-\346]/g, // A, a
		/[\310-\313]/g, /[\350-\353]/g, // E, e
		/[\314-\317]/g, /[\354-\357]/g, // I, i
		/[\322-\330]/g, /[\362-\370]/g, // O, o
		/[\331-\334]/g, /[\371-\374]/g, // U, u
		/[\321]/g, /[\361]/g, // N, n
		/[\307]/g, /[\347]/g, // C, c
	];
	var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

	for (var i = 0; i < accent.length; i++) {
		nom = nom.replace(accent[i], noaccent[i]);
	}

	uniqid = nom.replace(/\s/g, '-').replace(/[^a-zA-Z0-9\-]/g, '').replace(RegExp('-{2,}', 'g'), '-');

	var target = container.currentTarget.id + '_uniqid';
	if ($(container.currentTarget).data('target')) {
		target = $(container.currentTarget).data('target');
	}
	$('#' + target).val(uniqid.toLowerCase());
}

function standalone_uniqid(container) {
	var nom = $(this).val();
	var uniqid = '';

	var accent = [
		/[\300-\306]/g, /[\340-\346]/g, // A, a
		/[\310-\313]/g, /[\350-\353]/g, // E, e
		/[\314-\317]/g, /[\354-\357]/g, // I, i
		/[\322-\330]/g, /[\362-\370]/g, // O, o
		/[\331-\334]/g, /[\371-\374]/g, // U, u
		/[\321]/g, /[\361]/g, // N, n
		/[\307]/g, /[\347]/g, // C, c
	];
	var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

	for (var i = 0; i < accent.length; i++) {
		nom = nom.replace(accent[i], noaccent[i]);
	}

	uniqid = nom.replace(/\s/g, '-').replace(/[^a-zA-Z0-9\-]/g, '').replace(RegExp('-{2,}', 'g'), '-');

	$('#' + container.currentTarget.id).val(uniqid.toLowerCase());
}

function selectFileWithCKFinder(elementId) {
	CKFinder.popup({
		chooseFiles: true,
		width: 800,
		height: 600,
		skin: 'neko',
		plugins: [
			baseDir + '/lib/ckfinder/plugins/CustomDialog/CustomDialog.js'
		],
		onInit: function (finder) {
			finder.on('files:choose', function (evt) {
				var file = evt.data.files.first();
				var fileUrl = file.getUrl();
				$('#' + elementId).val(fileUrl);
				$('#' + elementId).trigger('input');
				if ($('#actual-' + elementId).length) {
					$('#actual-' + elementId).val(fileUrl);
				}
				if ($('#' + elementId).data('target')) {
					$('#' + $('#' + elementId).data('target') + ' img').attr('src', fileUrl);
					$('#' + $('#' + elementId).data('target')).removeClass('d-none');
				}
				if ($('#actual-' + elementId).length && $('#actual-' + elementId).data('target')) {
					$('#' + $('#actual-' + elementId).data('target') + ' img').attr('src', fileUrl);
				}
			});

			finder.on('file:choose:resizedImage', function (evt) {
				var output = document.getElementById(elementId);
				output.value = evt.data.resizedUrl;
			});
		}
	});
}

function displayVideo(videoUrl) {
	let content = '<div class="embed-responsive embed-responsive-16by9">';
	content += '<iframe class="embed-responsive-item rounded border" src="' + videoUrl + '" width="854" height="480" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>';
	content += '</div>';
	$('#ModalVideo .modal-body').html(content);
	$('#ModalVideo').on('hidden.bs.modal', function() {
		$('#ModalVideo .modal-body').html('');
	});
	$('#ModalVideo').modal('show');
}

function ToggleFilters() {
	if ($('.filters').hasClass('d-none')) {
		$('.filters').removeClass('d-none');
		$('#btnFilters').removeClass('btn-light-primary').addClass('btn-primary');
	} else {
		$('.filters').addClass('d-none');
		$('#btnFilters').removeClass('btn-primary').addClass('btn-light-primary');
	}
}

function insertText(id, text) {
	if ($('#' + id).hasClass('ckeditor') || $('#' + id).hasClass('ckeditorsmall')) {
		if (typeof editors[id] !== 'undefined') {
			let editor = editors[id];
			let value = editor.getData();
			value += '<p>' + text + '</p>';
			editor.setData(value);
		}
	} else {
		let content = $('#' + id).val();
		$('#' + id).val((content ? content + "\n" : '') + text.replace('<br>', "\n"));

		if ($('#' + id).hasClass('autosize')) {
			let textarea = $('#' + id);
			autosize.update(textarea);
		}
	}
}

function createModalImg(imgUrl, title, downloadUrl) {
	$('#modalPreview').remove();
	if (!downloadUrl) {
		downloadUrl = imgUrl;
	}
	if (imgUrl.indexOf('?') !== -1) {
		downloadUrl = downloadUrl + '&download';
	} else {
		downloadUrl = downloadUrl + '?download';
	}

	var modal = '<div class="modal fade" id="modalPreview" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
	modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
	modal += '<div class="modal-content">\n' +
		'            <div class="modal-header">\n' +
		'                <h5 class="modal-title" id="exampleModalLabel">' + title + '</h5>\n' +
		'                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
		'                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
		'                </button>\n' +
		'            </div>\n' +
		'            <div class="modal-body text-center">\n' +
		'                <img class="img-fluid" src=\'' + imgUrl + '\'>\n' +
		'            </div>\n' +
		'            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
		'                <a class="btn btn-primary font-weight-bold mr-4" href="' + downloadUrl + '" target="_blank">' + __('Télécharger') + '</a>\n' +
		'                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
		'            </div>\n' +
		'        </div>';

	$('body').append(modal);
	$('#modalPreview').modal('show');
}

function createModalDocument(url, title, downloadUrl) {
	$('#modalPreview').remove();
	if (!downloadUrl) {
		downloadUrl = url;
	}
	if (url.indexOf('?') !== -1) {
		downloadUrl = downloadUrl + '&download';
	} else {
		downloadUrl = downloadUrl + '?download';
	}

	let preview = '<iframe class="border-0 w-100 h-1000px" src=\'' + url + '\'></iframe>';
	if (url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png') || url.includes('.gif')) {
		preview = '<img class="img-fluid" src=\'' + url + '\'>';
	}

	var modal = '<div class="modal fade" id="modalPreview" tabindex="-1" role="dialog" aria-labelledby="exampleModalSizeSm" aria-hidden="true">';
	modal += '<div class="modal-dialog modal-dialog-centered modal-xl" role="document">';
	modal += '<div class="modal-content">\n' +
		'            <div class="modal-header">\n' +
		'                <h5 class="modal-title" id="exampleModalLabel">' + title + '</h5>\n' +
		'                <button type="button" class="close" data-dismiss="modal" aria-label="Close">\n' +
		'                    <i aria-hidden="true" class="ki ki-close"></i>\n' +
		'                </button>\n' +
		'            </div>\n' +
		'            <div class="modal-body text-center">\n' +
		'                ' + preview + '\n' +
		'            </div>\n' +
		'            <div class="modal-footer d-flex flex-row justify-content-between">\n' +
		'                <a class="btn btn-primary font-weight-bold mr-4" href="' + downloadUrl + '" target="_blank">' + __('Télécharger') + '</a>\n' +
		'                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">' + __('Fermer') + '</button>\n' +
		'            </div>\n' +
		'        </div>';

	$('body').append(modal);
	$('#modalPreview').modal('show');
}

function toggleLeftMenu() {
	if ($('#kt_aside_menu').hasClass('d-lg-none')) {
		$('#kt_aside_menu').removeClass('d-lg-none');
		$('#kt_aside_content').addClass('ml-lg-8');
	} else {
		$('#kt_aside_menu').addClass('d-lg-none');
		$('#kt_aside_content').removeClass('ml-lg-8');
	}
}

function scrollToElement(id, offset) {
	let position = $(id).offset().top;
	if (offset) {
		position = position + offset;
	}
	$('html, body').animate({
		scrollTop: position
	}, 1000);
}

function positionSidebarMenu($icon) {
	const rect = $icon[0].getBoundingClientRect();
	const translateY = rect.top;

	let menu = $icon.parent('li').find('div.menu-submenu');
	if (!menu.length) {
		return;
	}

	menu.css({
		'transform': `translate(0px, ${translateY}px)`,
		'display': 'block'
	});
}
