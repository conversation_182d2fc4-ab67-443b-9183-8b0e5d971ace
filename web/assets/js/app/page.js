$(document).ready(function() {
    let currentCardIndex = 1;
    const totalCards = 10;

    // Fonction pour mettre à jour l'état des cartes
    function updateCardsState() {
        $('.card-field').each(function(index) {
            const cardNumber = index + 1;
            const $card = $(this);

            // Retirer toutes les classes d'état
            $card.removeClass('active inactive hidden done');

            if (cardNumber < currentCardIndex) {
                // Cartes précédentes - état done (cachées vers le haut)
                $card.addClass('done');
            } else if (cardNumber === currentCardIndex) {
                // Carte actuelle - état active
                $card.addClass('active');
            } else if (cardNumber === currentCardIndex + 1) {
                // Carte suivante - état inactive
                $card.addClass('inactive');
            } else {
                // Autres cartes - état hidden
                $card.addClass('hidden');
            }
        });
    }

    // Gestion du bouton Next
    $('.btn-next-field').on('click', function(e) {
        e.preventDefault();

        if (currentCardIndex < totalCards) {
            currentCardIndex++;
            updateCardsState();
        }

        // Désactiver le bouton si on est à la dernière carte
        if (currentCardIndex >= totalCards) {
            $(this).addClass('disabled').css('opacity', '0.5');
        }

        // Réactiver le bouton Previous si nécessaire
        $('.btn-prev-field').removeClass('disabled').css('opacity', '1');
    });

    // Gestion du bouton Previous
    $('.btn-prev-field').on('click', function(e) {
        e.preventDefault();

        if (currentCardIndex > 1) {
            currentCardIndex--;
            updateCardsState();
        }

        // Désactiver le bouton si on est à la première carte
        if (currentCardIndex <= 1) {
            $(this).addClass('disabled').css('opacity', '0.5');
        }

        // Réactiver le bouton Next si nécessaire
        $('.btn-next-field').removeClass('disabled').css('opacity', '1');
    });

    // Initialisation
    updateCardsState();

    // Désactiver le bouton Previous au début
    $('.btn-prev-field').addClass('disabled').css('opacity', '0.5');
});