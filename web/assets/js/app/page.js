$(document).ready(function() {
    let currentCardIndex = 1;
    const totalCards = 10;

    // Fonction pour calculer et ajuster la position de la carte inactive
    function adjustInactiveCardPosition() {
        const $activeCard = $('.card-field.active');
        const $inactiveCard = $('.card-field.inactive');

        if ($activeCard.length && $inactiveCard.length) {
            // Attendre que les transitions CSS soient terminées
            setTimeout(function() {
                const activeCardHeight = $activeCard.outerHeight(true);
                const spacing = 20; // Espacement entre les cartes
                const newTop = activeCardHeight + spacing;

                // Appliquer la nouvelle position
                $inactiveCard.css('top', newTop + 'px');
            }, 50); // Petit délai pour s'assurer que le DOM est mis à jour
        }
    }

    // Fonction pour mettre à jour l'état des cartes
    function updateCardsState() {
        $('.card-field').each(function(index) {
            const cardNumber = index + 1;
            const $card = $(this);

            // Retirer toutes les classes d'état
            $card.removeClass('active inactive hidden done');

            if (cardNumber < currentCardIndex) {
                // Cartes précédentes - état done (cachées vers le haut)
                $card.addClass('done');
            } else if (cardNumber === currentCardIndex) {
                // Carte actuelle - état active
                $card.addClass('active');
            } else if (cardNumber === currentCardIndex + 1) {
                // Carte suivante - état inactive
                $card.addClass('inactive');
            } else {
                // Autres cartes - état hidden
                $card.addClass('hidden');
            }
        });

        // Ajuster la position de la carte inactive après avoir appliqué les classes
        adjustInactiveCardPosition();
    }

    // Gestion du bouton Next
    $('.btn-next-field').on('click', function(e) {
        e.preventDefault();

        if (currentCardIndex < totalCards) {
            currentCardIndex++;
            updateCardsState();
        }

        // Désactiver le bouton si on est à la dernière carte
        if (currentCardIndex >= totalCards) {
            $(this).addClass('disabled').css('opacity', '0.5');
        }

        // Réactiver le bouton Previous si nécessaire
        $('.btn-prev-field').removeClass('disabled').css('opacity', '1');
    });

    // Gestion du bouton Previous
    $('.btn-prev-field').on('click', function(e) {
        e.preventDefault();

        if (currentCardIndex > 1) {
            currentCardIndex--;
            updateCardsState();
        }

        // Désactiver le bouton si on est à la première carte
        if (currentCardIndex <= 1) {
            $(this).addClass('disabled').css('opacity', '0.5');
        }

        // Réactiver le bouton Next si nécessaire
        $('.btn-next-field').removeClass('disabled').css('opacity', '1');
    });

    // Observer pour recalculer la position si le contenu change
    function setupResizeObserver() {
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(function(entries) {
                // Recalculer la position de la carte inactive quand la carte active change de taille
                adjustInactiveCardPosition();
            });

            // Observer toutes les cartes actives
            $('.card-field').each(function() {
                resizeObserver.observe(this);
            });
        } else {
            // Fallback pour les navigateurs qui ne supportent pas ResizeObserver
            $(window).on('resize', function() {
                adjustInactiveCardPosition();
            });
        }
    }

    // Initialisation
    updateCardsState();
    setupResizeObserver();

    // Désactiver le bouton Previous au début
    $('.btn-prev-field').addClass('disabled').css('opacity', '0.5');

    // Recalculer la position après le chargement complet de la page
    $(window).on('load', function() {
        setTimeout(adjustInactiveCardPosition, 100);
    });
});