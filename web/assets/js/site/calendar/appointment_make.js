let _validations = [];
let calendar;
let _wizardObj2;

const strongPassword = function () {
    return {
        validate: function (input) {
            const value = input.value;
            if (value === '') {
                return {
                    valid: true,
                };
            }

            // Check the password strength
            if (value.length < 12) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any uppercase character
            if (value === value.toLowerCase()) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any uppercase character
            if (value === value.toUpperCase()) {
                return {
                    valid: false,
                };
            }

            // The password does not contain any digit
            if (value.search(/[0-9]/) < 0) {
                return {
                    valid: false,
                };
            }

            return {
                valid: true,
            };
        },
    };
};

$(document).ready(function() {
    updateBlockCart();

    $('#consulting_reason_id').on('change', function() {
        $('#calendar_id').val('');
        updateBlockCart();
        loadCalendars();
        displayPayment();
    });

    $('#calendar_id').on('change', function() {
        updateBlockCart();
        initCalendar();
    });

    $('#date_start').on('change', function() {
        updateBlockCart();
    });

    $('.btn-display-step-user-hidden').on('click', function() {
        $('.wizard-step-user-login').remove();
        $('#kt_login_signin_form').removeClass('d-none');
    });

    if ($('.wizard-step-content-user-logged').length > 0 && !$('.wizard-step-content-user-logged').hasClass('d-none')) {
        $('#password').closest('.form-group').addClass('d-none');
        $('#password2').closest('.form-group').addClass('d-none');
    }

    $('.consulting-reason-description').on('click', function() {
        $(this).toggleClass('truncated');
    });

    setTutorialValidator();
});

function updateBlockCart() {
    let content = '';
    let price = 0;

    content = '<div class="d-flex flex-column">';

    if ($('#consulting_reason_id').val()) {
        let consultingReasonId = $('#consulting_reason_id').val();
        let consultingReasonName = $('#consulting_reasons').find('.consulting-reason-' + consultingReasonId + ' .consulting-reason-title').text();
        price = $('#consulting_reasons').find('.consulting-reason-' + consultingReasonId).data('price');
        content += '<p class="mb-0"><strong>' + __('Motif de consultation') + ' : </strong>' + consultingReasonName + '</p>';
    }
    if ($('#calendar_id').val()) {
        let calendarId = $('#calendar_id').val();
        let calendarName = $('#calendars').find('.calendar-' + calendarId + ' .calendar-title').text();
        content += '<p class="mb-0"><strong>' + __('Calendrier') + ' : </strong>' + calendarName + '</p>';
    }
    if ($('#date_start').val()) {
        let dateStart = moment.unix($('#date_start').val() / 1000);
        content += '<p class="mb-0"><strong>' + __('Date du rendez-vous') + ' : </strong>' + dateStart.format('DD/MM/YYYY HH:mm') + '</p>';
    }
    content += '</div>';

    let card = '<div class="card card-custom">' +
        '<div class="card-header">' +
        '<h3 class="card-title font-weight-bold">' + __('Récapitulatif du rendez-vous') + '</h3>' +
        '</div>' +
        '<div class="card-body text-left">' + content + '</div>' +
        '<div class="card-footer">' +
        '<span class="font-weight-bold text-dark-50">' + __('Prix') + ' : </span>' +
        '<span class="font-weight-bolder font-size-h5 text-dark-75">' + price + ' €</span>' +
        '</div>' +
        '</div>';
    $('#appointment_cart').html(card);
}

function selectConsultingReasonAndCalendar(consultingReasonId, calendarId) {
    $('#consulting_reason_id').val(consultingReasonId);
    $('.consulting-reason').removeClass('active');
    $('.consulting-reason-' + consultingReasonId).addClass('active');
    $('#calendar_id').val('');

    $('#wizard-step1').addClass('d-none');
    $('#wizard-step2').addClass('d-none');
    $('#wizard-step3 .wizard-number').html(1);
    $('#wizard-step4 .wizard-number').html(2);
    $('#wizard-step5 .wizard-number').html(3);
    $('#wizard-step6 .wizard-number').html(4);
    loadCalendars(calendarId);
}

function selectConsultingReason(consultingReasonId, calendarId) {
    $('#consulting_reason_id').val(consultingReasonId).trigger('change');
    $('.consulting-reason').removeClass('active');
    $('.consulting-reason-' + consultingReasonId).addClass('active');
    goToStep('calendar');
}

function loadCalendars(calendarId) {
    KTApp.block('#calendar_choice_content', {});
    $('#consulting_reason_message').addClass('d-none');
    let consultingReasonId = $('#consulting_reason_id').val();
    $.post(
        baseDir + '/ajax/calendars/list/' + consultingReasonId + '/',
        { async: false, CSRFGuard_token:CSRFGuard_token },
        function(data) {
            KTApp.unblock('#calendar_choice_content');
            if (data.status) {
                $('#calendar_choice_content').html(data.content);
                if (calendarId) {
                    selectCalendar(calendarId);
                }

                if (data.consulting_reason_message) {
                    $('#consulting_reason_message').html(data.consulting_reason_message).removeClass('d-none');
                }
            } else {
                $('#calendar_choice_content').html('<div class="alert alert-custom alert-light-danger">' + data.message+ '</div>');
            }
        },
        'json'
    );
}

function displayPayment() {
    let consultingReasonId = $('#consulting_reason_id').val();
    let paymentType = $('#consulting_reasons').find('.consulting-reason-' + consultingReasonId).data('payment-type');
    let price = parseFloat($('#consulting_reasons').find('.consulting-reason-' + consultingReasonId).data('price'));

    if (paymentType == 'online' && price > 0) {
        $('#free_order').removeClass('d-flex').addClass('d-none');
        $('#paid_order').removeClass('d-none');
        loadPayments();
    } else {
        $('#free_order').removeClass('d-none').addClass('d-flex');
        $('#paid_order').addClass('d-none');
    }
}

function loadPayments() {
    KTApp.block('#paid_order', {});
    let consultingReasonId = $('#consulting_reason_id').val();
    $.post(
        baseDir + '/ajax/consulting_reason/payments/' + consultingReasonId + '/',
        { async: false, CSRFGuard_token:CSRFGuard_token },
        function(data) {
            KTApp.unblock('#paid_order');
            if (data.status) {
                $('#paid_order #payments').html(data.content);
                setInstallments();
                setPaymentTypes();
            } else {
                $('#order_errors_payment').html('<div class="alert alert-custom alert-light-danger">' + data.message+ '</div>');
            }
        },
        'json'
    );
}

function setInstallments()
{
    $('input[name="installment"]').on('change', function() {
        $('.installment').hide();
        $('#installment' + $(this).data('installment')).show();

        var firstPaymentType = $('#installment' + $(this).data('installment')).find('input[name="id_payment"]').first();
        $('#' + firstPaymentType.prop('id')).trigger('change');
    });
}

function setPaymentTypes()
{
    $('input[name="id_payment"]').on('change', function() {
        $('.payment').hide();
        $(this).prop('checked', true);
        $('.payment.' + $(this).data('type')).show();
    });
}

function selectCalendar(calendarId) {
    $('#calendar_id').val(calendarId).trigger('change');
    $('.calendar').removeClass('active');
    $('.calendar-' + calendarId).addClass('active');
    goToStep('date');
}

//main function to initiate the module
function initCalendar() {
    let calendarEl = document.getElementById('kt_calendar');
    let calendarId = $('#calendar_id').val();
    let consultingReasonId = $('#consulting_reason_id').val();

    calendar = new FullCalendar.Calendar(calendarEl, {
        //plugins: [ 'bootstrap', 'interaction', 'dayGrid', 'timeGrid', 'list' ],

        /*themeSystem: 'bootstrap',*/
        locale: 'fr',
        //timeZone: 'Europe/Paris',
        firstDay: moment().day(),
        initialView: 'timeGridFourDay',
        height: 600,
        contentHeight: 580,
        eventMinHeight:40,
        eventOverlap: false,
        slotEventOverlap: false,

        aspectRatio: 3,  // see: https://fullcalendar.io/docs/aspectRatio

        headerToolbar: {
            start: '',
            center: 'title',
            end: 'prev,next today'
        },
        buttonText: {
            today: __('Aujourd\'hui'),
        },
        views: {
            timeGridFourDay: {
                type: 'timeGrid',
                duration: { days: 5 }
            }
        },

        nowIndicator: true,
        editable: false,
        navLinks: false,
        dayMaxEventRows: true, // for all non-TimeGrid views
        allDaySlot: false,
        scrollTime: '09:00:00',
        validRange: {
            start: moment().format('YYYY-MM-DD'),
        },

        events: {
            url: baseDir + '/ajax/site/calendar/availabilities/',
            method: 'POST',
            extraParams: {
                CSRFGuard_token: CSRFGuard_token,
                calendarId: calendarId,
                consultingReasonId: consultingReasonId
            },
            failure: function(data) {
                ShowToast('error', data.xhr.responseText);
            },
            success: function(data) {
                if (!data.length) {
                    calendar.setOption('slotMinTime', '09:00:00');
                    calendar.setOption('slotMaxTime', '19:00:00');
                    $('#kt_calendar_no_events').removeClass('d-none');
                    return;
                }

                $('#kt_calendar_no_events').addClass('d-none');

                var minTime = moment().set({'hour': 23, 'minutes': 59, 'seconds' : 59}).format("HH:mm:ss");
                var maxTime = moment().set({'hour': 0, 'minutes': 0, 'seconds' : 0}).format("HH:mm:ss");
                for(var i in data) {
                    var start = moment(data[i].start)
                    var end = moment(data[i].end)
                    minTime = timeDiff(minTime, start.format("HH:mm:ss"), true);
                    maxTime = timeDiff(maxTime, end.format("HH:mm:ss"), false);

                    data[i].title = start.format("HH:mm");
                }

                calendar.setOption('slotMinTime', minTime);
                calendar.setOption('slotMaxTime', maxTime);
            },
        },

        eventClick: function(info) {
            $('#date_start').val(info.event.start.getTime()).trigger('change');

            $('.fc-event').removeClass('selected');
            let element = $(info.el);
            element.addClass('selected');
        },

        loading: function( isLoading ) {
            if (isLoading) {
                KTApp.block('.fc-view', {});
            } else {
                KTApp.unblock('.fc-view');
            }
        },

        allDayText: __('Journée'),
    });

    calendar.render();
}

function timeDiff(time1, time2, getMin) {
    var d1 = new Date('2014-01-01 ' + time1),
        d2 = new Date('2014-01-01 ' + time2);

    if (getMin) {
        return d1.getTime(d1) - d2.getTime(d2) < 0 ? time1 : time2;
    } else {
        return d1.getTime(d1) - d2.getTime(d2) > 0 ? time1 : time2;
    }
}

function login() {
    KTApp.block('#kt_login_signin_form', {});
    $('#kt_login_signin_submit').attr('disabled', true).addClass('spinner spinner-left spinner-white');
    let formData = {
        email: $('#kt_login_signin_form #login_email').val(),
        password: $('#kt_login_signin_form #login_password').val(),
        CSRFGuard_token: CSRFGuard_token,
    };
    $.post(
        baseDir + '/ajax/user/signin/',
        formData,
        function (result) {
            if (result.status) {
                $.post(
                    baseDir + '/ajax/user/infos/',
                    { CSRFGuard_token: CSRFGuard_token },
                    function (result) {
                        if (result.status) {
                            $('#kt_login_signin_form').remove();
                            $('.wizard-step-user-hidden').addClass('d-none');
                            $('.wizard-step-user-hidden').before('<div class="wizard-step-content-user-logged gutter-b">' + result.userContent + '</div>');
                            $('#first_name').val(result.user.first_name);
                            $('#last_name').val(result.user.last_name);
                            $('#email').val(result.user.email);
                            $('#address').val(result.address.address);
                            $('#address2').val(result.address.address2);
                            $('#city').val(result.address.city);
                            $('#zip').val(result.address.zip);
                            $('#state').val(result.address.state);
                            $('#telephone').val(result.address.telephone);
                            $('#password').closest('.form-group').addClass('d-none');
                            $('#password2').closest('.form-group').addClass('d-none');
                            setTutorialValidator();
                        } else {
                            ShowToast('error', result.message);
                        }
                    },
                    'json'
                );
            } else {
                KTApp.unblock('#kt_login_signin_form');
                $('#kt_login_signin_submit').attr('disabled', false).removeClass('spinner spinner-left spinner-white');
                swal.fire({
                    text: result.message,
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: __('Fermer'),
                    confirmButtonClass: "btn font-weight-bold btn-light"
                });
            }
        },
        'json'
    );
}

function goToStep(stepTitle) {
    var step = stepTitle;
    if ($('#step_' + stepTitle).length > 0) {
        step = $('#step_' + stepTitle).data('step');
    }
    _wizardObj2.goTo(step);
}

function setTutorialValidator() {
    var _wizardEl = KTUtil.getById('kt_wizard');

    FormValidation.validators.checkPassword = strongPassword;

    var fieldsStep1 = {
        consulting_reason_id: {
            validators: {
                notEmpty: {
                    message: __('Merci de choisir un motif de consultation')
                }
            }
        },
    };
    _validations[1] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep1,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep2 = {
        calendar_id: {
            validators: {
                notEmpty: {
                    message: __('Merci de choisir un calendrier')
                }
            }
        },
    };
    _validations[2] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep2,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep3 = {
        date_start: {
            validators: {
                notEmpty: {
                    message: __('Merci de choisir une date de rendez-vous')
                }
            }
        },
    };
    _validations[3] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep3,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    var fieldsStep4 = {
        first_name: {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre prénom')
                }
            }
        },
        last_name: {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre nom')
                }
            }
        },
        email: {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre adresse email')
                },
                emailAddress: {
                    message: __('Veuillez entrer une adresse email valide')
                }
            }
        },
    };
    if ($('#kt_form #address').length > 0 && $('#kt_form #address').is(':required')) {
        fieldsStep4.address = {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre adresse')
                }
            }
        };
    }
    if ($('#kt_form #zip').length > 0 && $('#kt_form #zip').is(':required')) {
        fieldsStep4.zip = {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre code postal')
                }
            }
        };
    }
    if ($('#kt_form #city').length > 0 && $('#kt_form #city').is(':required')) {
        fieldsStep4.city = {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre ville')
                }
            }
        };
    }
    if ($('#kt_form #telephone').length > 0 && $('#kt_form #telephone').is(':required')) {
        fieldsStep4.telephone = {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer votre numéro de téléphone')
                }
            }
        };
    }

    if ($('#kt_form #password').length > 0 && !$('#kt_form #password').closest('.form-group').hasClass('d-none')) {
        fieldsStep4.password = {
            validators: {
                notEmpty: {
                    message: __('Veuillez entrer un mot de passe')
                },
                checkPassword: {
                    message:  __('Veuillez entrer un mot de passe de minimum 8 caractères avec au moins une lettre majuscule et un chiffre')
                },
            }
        };
        fieldsStep4.password2 = {
            validators: {
                notEmpty: {
                    message: __('Veuillez confirmer le mot de passe')
                },
                checkPassword: {
                    message:  __('Veuillez entrer un mot de passe de minimum 8 caractères avec au moins une lettre majuscule et un chiffre')
                },
                identical: {
                    compare: function () {
                        return document.getElementById('password').value;
                    },
                    message: __('Les 2 mots de passe sont différents'),
                },
            }
        };
    }
    _validations[4] = FormValidation.formValidation(
        _wizardEl,
        {
            fields: fieldsStep4,
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap({
                    eleValidClass: '',
                })
            }
        }
    );

    _wizardObj2 = new KTWizard(_wizardEl, {
        startStep: 1,
        clickableSteps: false
    });
    _wizardObj2.on('change', function (wizard) {
        if (wizard.getStep() > wizard.getNewStep()) {
            return; // Skip if stepped back
        }

        var validator = _validations[wizard.getStep()];
        if (validator) {
            validator.validate().then(function (status) {
                if (status == 'Valid') {
                    wizard.goTo(wizard.getNewStep());
                } else {
                    if (wizard.getStep() == 4 && $('.wizard-step-user-hidden').length > 0 && $('.wizard-step-user-hidden').hasClass('d-none')) {
                        displayCustomerFields();
                    }
                }
                var wizardId = KTUtil.getById('kt_wizard');
                KTUtil.scrollTo(wizardId);
            });
        } else {
            return true;
        }
        return false;
    });
    _wizardObj2.on('changed', function (wizard) {
        $('.wizard').attr('data-wizard-step', wizard.getStep());
    });
    _wizardObj2.on('submit', function (wizard) {
        $('#kt_form .errors-step').addClass('d-none');
        $('#kt_form_submit').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left');

        let formData = $('#kt_form').serializeArray();
        formData.push({name: 'CSRFGuard_token', value: CSRFGuard_token});
        $.post(
            baseDir + '/ajax/' + ajaxAction + '/',
            formData,
            function (result) {
                if (result.status) {
                    validateForm(result);
                } else {
                    if (!result.step) {
                        result.step = 'consulting_reason';
                    }
                    $('#kt_form_submit').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left').addClass('px-9');

                    let stepNumber = $('#step_' + result.step).data('step');
                    $('#kt_form #errors' + stepNumber + ' .alert-text').html(result.message);
                    $('#kt_form #errors' + stepNumber).removeClass('d-none');

                    goToStep(result.step);

                    KTUtil.scrollTop();
                }
            },
            'json'
        );
    });
}

function validateForm(result) {
    if (result.paymentType == 'on_site') {
        $('#kt_form').submit();
    } else {
        if (result.paymentSubType == 'oneclick') {
            processStripePaymentOneClick(result.custom, result.token);
        } else {
            processStripePayment(result.stripeBillingAddress, result.custom, result.token);
        }
    }
}

function displayError(errorMessage, step)
{
    if (!step) {
        step = 'payment';
    }

    setPaymentInProcess(false);

    let target = '#order_errors_' + step;
    if (!$('#order_errors_' + step).length) {
        if ($('#errors' + step).length) {
            target = '#errors' + step;
        } else if ($('#step_' + step).length) {
            target = '#errors' + $('#step_' + step).data('step');
        }
    }

    if (!$(target + ' .alert-text').length) {
        var alert = '<div class="alert alert-custom alert-light-danger fade show mb-5" role="alert">\n' +
            '    <div class="alert-icon"><i class="fas fa-exclamation-triangle"></i></div>\n' +
            '    <div class="alert-text">' + errorMessage + '</div>\n' +
            '    <div class="alert-close">\n' +
            '        <button type="button" class="close" data-dismiss="alert" aria-label="Close">\n' +
            '            <span aria-hidden="true"><i class="fas fa-times"></i></span>\n' +
            '        </button>\n' +
            '    </div>\n' +
            '</div>';
        $(target).html(alert);
    } else {
        $(target + ' .alert-text').html(errorMessage);
    }

    $(target).removeClass('d-none');
    $(target + ' .alert').removeClass('d-none');

    goToStep(step);
}

function setPaymentInProcess(paymentInProcess)
{
    if (paymentInProcess) {
        $('.alert-text').html('');
        $('.alert-text').parent().addClass('d-none');
        $('#kt_form_submit').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left disabled').html(__('Traitement'));
        $('#btnValidate').attr('disabled', true).removeClass('px-9').addClass('pl-12 spinner spinner-white spinner-left disabled').html(__('Traitement'));
    } else {
        $('#kt_form_submit').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left disabled').addClass('px-9').html(__('Valider'));
        $('#btnValidate').attr('disabled', false).removeClass('pl-12 spinner spinner-white spinner-left disabled').addClass('px-9').html(__('Valider'));
    }
}

function getFormInputs()
{
    var values = {};
    $.each($('#kt_form, #payment_form').serializeArray(), function(i, field) {
        if (field.name.endsWith('[]')) {
            var fieldName = field.name;
            fieldName = fieldName.substring(0, fieldName.length - 2);
            if (!(fieldName in values)) {
                values[fieldName] = [];
            }
            values[fieldName].push(field.value);
        } else {
            values[field.name] = field.value;
        }
    });
    $.each($('#card-products').serializeArray(), function(i, field) {
        values[field.name] = field.value;
    });
    return values;
}

function displayCustomerFields() {
    $('.wizard-step-content-user-logged').addClass('d-none');
    $('.wizard-step-user-hidden').removeClass('d-none');
    $('#password').closest('.form-group').addClass('d-none');
    $('#password2').closest('.form-group').addClass('d-none');
    setTutorialValidator();
}
