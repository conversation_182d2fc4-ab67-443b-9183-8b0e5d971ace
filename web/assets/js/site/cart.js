$(document).ready(function() {
    $('#quick_cart_toggle').on('click', function () {
        displayCartMenu();
    });
    $('#kt_quick_cart_close').on('click', function() {
        hideCartMenu();
    });
});

function displayCartMenu() {
    $('#kt_quick_cart').addClass('offcanvas-on');
    $('#kt_quick_cart').after('<div class="offcanvas-overlay"></div>');
    $('.offcanvas-overlay').on('click', function() {
        hideCartMenu();
    });
    getCartMenu();
}

function hideCartMenu() {
    $('#kt_quick_cart').removeClass('offcanvas-on');
    $('.offcanvas-overlay').remove();
}

function getCartMenu() {
    KTApp.block('.menu-cart-content', {});
    $.post(
        baseDir + '/ajax/ecommerce/cart/menu/get/',
        { CSRFGuard_token:CSRFGuard_token, async: false },
        function (data) {
            KTApp.unblock('.menu-cart-content');
            if (data.status) {
                $('#kt_quick_cart .menu-cart-products').html(data.cart);
                if (data.empty) {
                    $('#kt_quick_cart .cart-total-content').addClass('d-none');
                } else {
                    $('#kt_quick_cart .header-cart-total .cart-total-amount').html(data.cartTotal);
                }
            } else {
                $('#kt_quick_cart .menu-cart-products').html('<div class="alert alert-light-danger">' + data.message + '</div>');
            }
        },
        'json'
    );
}

function AddToCart(idEventProduct)
{
    if (!idEventProduct) {
        return;
    }
    KTApp.block('.btn-add-to-cart', {});
    let productData = $('#productAddToCart' + idEventProduct).serialize();
    $.post(
        baseDir + '/ajax/ecommerce/cart/add/',
        { productData: productData, CSRFGuard_token:CSRFGuard_token, async: false },
        function (data) {
            KTApp.unblock('.btn-add-to-cart');
            if (data.status) {
                displayCartMenu();
            } else {
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function DisplayModalAddToCart()
{
    $('#ModalAddToCart').modal('show');
}

function RemoveFromCart(identifier)
{
    if (!identifier) {
        return;
    }

    KTApp.block('.menu-cart-content', {});
    $.post(
        baseDir + '/ajax/ecommerce/cart/remove/',
        { identifier: identifier, CSRFGuard_token:CSRFGuard_token, async: false },
        function (data) {
            if (data.status) {
                $('#' + identifier).remove();
                getCartMenu();
            } else {
                KTApp.unblock('.menu-cart-content');
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function UpdateQuantity(identifier, qty)
{
    if (!identifier) {
        return;
    }

    KTApp.block('.menu-cart-content', {});
    if ($('#cart-content').length > 0) {
        KTApp.block('#cart-content', {});
    }
    $.post(
        baseDir + '/ajax/ecommerce/cart/quantity/update/',
        { identifier: identifier, qty:qty, CSRFGuard_token:CSRFGuard_token, async: false },
        function (data) {
            if (data.status) {
                if ($('#cart-content').length > 0) {
                    window.location.reload();
                } else {
                    getCartMenu();
                }
            } else {
                KTApp.unblock('.menu-cart-content');
                ShowToast('error', data.message);
            }
        },
        'json'
    );
}

function addEcommerceDiscount() {
    $('#formDiscount #discountError').html('');
    KTApp.block('#formDiscount');
    var discount = $('#formDiscount #discount').val();
    if (discount == '') {
        $('#formDiscount #discountError').html('<div class="alert alert-custom alert-light-danger">' + __('Veuillez entrer le code de votre bon de réduction') + '</div>');
        KTApp.unblock('#formDiscount');
    } else {
        $.post(
            baseDir + '/ajax/ecommerce/discount/add/',
            { discount: discount, CSRFGuard_token: CSRFGuard_token },
            function(data) {
                KTApp.unblock('#formDiscount');
                if (data.status) {
                    window.location.reload();
                } else {
                    $('#formDiscount #discountError').html('<span class="text-danger">' + data.message + '</span>');
                }
            },
            'json'
        );
    }
}

function deleteEcommerceDiscount() {
    KTApp.block('.discount');
    $.post(
        baseDir + '/ajax/ecommerce/discount/delete/',
        { CSRFGuard_token: CSRFGuard_token },
        function(data) {
            if (data.status) {
                window.location.reload();
            } else {
                KTApp.unblock('.discount');
                $('#formDiscount #discountError').html('<span class="text-danger">' + data.message + '</span>');
            }
        },
        'json'
    );
}

function login() {
    KTApp.block('#kt_login_signin_form', {});
    $('#kt_login_signin_submit').attr('disabled', true).addClass('spinner spinner-left spinner-white');
    let formData = {
        email: $('#kt_login_signin_form #login_email').val(),
        password: $('#kt_login_signin_form #login_password').val(),
        CSRFGuard_token: CSRFGuard_token,
    };
    $.post(
        baseDir + '/ajax/user/signin/',
        formData,
        function (result) {
            if (result.status) {
                window.location.reload();
            } else {
                KTApp.unblock('#kt_login_signin_form');
                $('#kt_login_signin_submit').attr('disabled', false).removeClass('spinner spinner-left spinner-white');
                swal.fire({
                    text: result.message,
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: __('Fermer'),
                    confirmButtonClass: "btn font-weight-bold btn-light"
                });
            }
        },
        'json'
    );
}