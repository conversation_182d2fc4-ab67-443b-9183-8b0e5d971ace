<?php
if (php_sapi_name() === 'cli') {
   exit('Cli mode is not allowed on this script.');
}

//DOCUMENT_ROOT
if (!defined('FULL_DOCUMENT_ROOT')) {
    define('FULL_DOCUMENT_ROOT', str_replace('/web', '', __DIR__));
}

require_once '../bootstrap.php';

// Get dispatch data
$dispatcher = $container->get(MatGyver\Services\DispatcherService::class);
$data = $dispatcher->dispatch();

// Get controller
try {
    $controller = $container->get($data['controller']);
} catch (DI\NotFoundException $e) {
    $controller = $container->get(\MatGyver\Controllers\Site\AbstractSiteController::class);
}

if (isset($data['view_class']) && !empty($data['view_class']) && method_exists($controller, 'setView')) {
    $controller->setView($container->get($data['view_class']));
}

if (isset($data['gameQuest']) && !empty($data['gameQuest']) && method_exists($controller, 'setGameQuest')) {
    $controller->setGameQuest($data['gameQuest']);
    if (isset($data['gameQuestStep']) && !empty($data['gameQuestStep']) && method_exists($controller, 'setGameQuestStep')) {
        $controller->setGameQuestStep($data['gameQuestStep']);
    }
}
if (isset($data['gameLevel']) && !empty($data['gameLevel']) && method_exists($controller, 'setGameLevel')) {
    $controller->setGameLevel($data['gameLevel']);
}

// Call controller's dispatch
$controller->dispatch($data['action'], $data['param'], $data['query'], $data['route'], $data['params']);
