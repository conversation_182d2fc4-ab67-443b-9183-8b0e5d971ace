<?php
define('FULL_DOCUMENT_ROOT', __DIR__);
try {
    require_once FULL_DOCUMENT_ROOT . '/config/environments.php';
    require_once FULL_DOCUMENT_ROOT . '/config/config.php';
    require_once HELPERS_PATH . '/datetime.php';
    require_once HELPERS_PATH . '/functions.php';
} catch(\Exception $e) {}

$config = [
    'environments' => [
        'default_environment' => 'main',
        'main' => [
            'migration_table' => 'migrations',
            'adapter' => 'mysql',
            'name' => DB_NAME,
            'host' => DB_HOST,
            'user' => DB_LOGIN,
            'pass' => DB_PASS,
            'port' => DB_PORT,
            'charset' =>  'utf8'
        ]
    ],
    'paths' => [
        'migrations' => 'var/migrations'
    ]
];
if (ENV !== ENV_PROD) {
    $config['environments']['main']['unix_socket'] = '/Applications/MAMP/tmp/mysql/mysql.sock';
}

return $config;