<?php

namespace MatGyver\Menus\Voter;

use Knp\Menu\ItemInterface;
use Knp\Menu\Matcher\Voter\VoterInterface;

class UriPatternVoter implements VoterInterface
{
    /**
     * @var string|null
     */
    private $uri;

    /**
     * UriPatternVoter constructor.
     * @param string|null $uri
     */
    public function __construct(?string $uri = null)
    {
        $this->uri = $uri;
    }

    /**
     * @param ItemInterface $item
     * @return bool|null
     */
    public function matchItem(ItemInterface $item): ?bool
    {
        if ($this->uri === null or $item->getUri() === null) {
            return null;
        }

        if ($item->getUri() === $this->uri) {
            return true;
        }

        if ($item->getExtra('matching_pattern') !== null and preg_match($item->getExtra('matching_pattern'), $this->uri)) {
            return true;
        }

        return null;
    }
}
