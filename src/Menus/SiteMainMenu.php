<?php

namespace MatGyver\Menus;

use Knp\Menu\ItemInterface;
use MatGyver\Helpers\Iconify;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Dossier\DossierService;

class SiteMainMenu extends AbstractMenu
{
    /**
     * @return ItemInterface
     */
    public function createMenu(): ItemInterface
    {
        $menu = $this->factory->createItem('site_main_menu', [
            'childrenAttributes' => [
                'class' => 'nav navbar-nav main-menu',
            ],
        ]);

        if (!isset($_SESSION['email'])) {
            $menu->addChild(__('Accueil'), [
                'uri' => Tools::makeLink('site', 'index'),
                'extras' => [
                    'matching_pattern' => '/^\/(index)\/$/',
                    'icon' => Iconify::getIcon('home-2-outline'),
                ]
            ]);
        } else {
            $menu->addChild(__('Mon compte'), [
                'uri' => Tools::makeLink('site', 'account'),
                'extras' => [
                    'matching_pattern' => '/^\/(account)\/$/',
                    'icon' => Iconify::getIcon('home-2-outline'),
                ]
            ]);

            //find dossier
            $container = ContainerBuilderService::getInstance();
            $dossier = $container->get(DossierService::class)->getRepository()->findOneBy(['user' => $_SESSION['user']['id']]);
            if ($dossier) {
                $menu->addChild(__('Mon dossier'), [
                    'uri' => Tools::makeLink('site', 'dossier', $dossier->getReference()),
                    'extras' => [
                        'matching_pattern' => '/^\/dossier\/(' . $dossier->getReference() . ')\/$/',
                        'icon' => Iconify::getIcon('folder-open-outline'),
                    ]
                ]);
            }

            $menu->addChild(__('Rendez-vous'), [
                'uri' => Tools::makeLink('site', 'account', 'appointments'),
                'extras' => [
                    'matching_pattern' => '/^\/account\/(appointment.*)\/$/',
                    'mobile' => true,
                    'icon' => Iconify::getIcon('calendar-outline'),
                ]
            ]);
            $menu->addChild(__('Transactions'), [
                'uri' => Tools::makeLink('site', 'account', 'transactions'),
                'extras' => [
                    'matching_pattern' => '/^\/account\/(transaction.*)\/$/',
                    'mobile' => true,
                    'icon' => Iconify::getIcon('wallet-money-outline'),
                ]
            ]);
        }

        return $menu;
    }
}
