<?php

namespace MatGyver\Repository\Support\Note;

use MatGyver\Entity\Support\Note\SupportNote;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method SupportNote|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupportNote|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method SupportNote[]    findAll()
 * @method SupportNote[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class SupportNoteRepository extends AbstractEntityRepository
{

}
