<?php

namespace MatGyver\Repository\Support\SavedReply;

use MatGyver\Entity\Support\SavedReply\SupportSavedReply;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method SupportSavedReply|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupportSavedReply|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method SupportSavedReply[]    findAll()
 * @method SupportSavedReply[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class SupportSavedReplyRepository extends AbstractEntityRepository
{

}
