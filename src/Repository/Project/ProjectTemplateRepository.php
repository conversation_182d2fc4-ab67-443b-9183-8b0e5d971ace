<?php
namespace MatGyver\Repository\Project;

use MatGyver\Entity\Project\ProjectTemplate;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ProjectTemplate|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProjectTemplate|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ProjectTemplate[]    findAll()
 * @method ProjectTemplate[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProjectTemplateRepository extends AbstractEntityRepository
{

}
