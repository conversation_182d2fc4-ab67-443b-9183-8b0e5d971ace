<?php

namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierProjectUser;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierProjectUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierProjectUser|null findOneBy(array $criteria, array $orderBy = null)
 * @method DossierProjectUser[]    findAll()
 * @method DossierProjectUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierProjectUserRepository extends AbstractEntityRepository
{
}
