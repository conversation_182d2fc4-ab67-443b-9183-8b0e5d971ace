<?php

namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierObjective;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierObjective|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierObjective|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierObjective[]    findAll()
 * @method DossierObjective[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class DossierObjectiveRepository extends AbstractEntityRepository
{
}
