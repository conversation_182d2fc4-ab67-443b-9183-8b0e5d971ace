<?php
namespace MatGyver\Repository\Dossier;

use MatGyver\Entity\Dossier\DossierPensionContract;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method DossierPensionContract|null find($id, $lockMode = null, $lockVersion = null)
 * @method DossierPensionContract|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method DossierPensionContract[]    findAll()
 * @method DossierPensionContract[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DossierPensionContractRepository extends AbstractEntityRepository
{

}
