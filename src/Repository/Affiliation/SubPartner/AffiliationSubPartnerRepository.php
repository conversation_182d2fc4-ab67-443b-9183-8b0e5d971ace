<?php

namespace MatGyver\Repository\Affiliation\SubPartner;

use MatGyver\Entity\Affiliation\SubPartner\AffiliationSubPartner;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method AffiliationSubPartner|null find($id, $lockMode = null, $lockVersion = null)
 * @method AffiliationSubPartner|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method AffiliationSubPartner[]    findAll()
 * @method AffiliationSubPartner[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class AffiliationSubPartnerRepository extends AbstractEntityRepository
{
    /**
     * @return array|null
     */
    public function getAllSubPartners(): ?array
    {
        return $this->createQueryBuilder('s')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array|null
     */
    public function getCountSubPartnersGroupByClient(): ?array
    {
        return $this->createQueryBuilder('s')
            ->select('IDENTITY(s.client) as clientId, count(s.id) as nbSubPartners')
            ->groupBy('s.client')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array|null
     */
    public function getSubPartnersClients(): ?array
    {
        return $this->createQueryBuilder('s')
            ->select('IDENTITY(s.client) as clientId, IDENTITY(s.subPartnerClient) as subPartnerClientId')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $date
     * @param int|null $idClient
     * @return int
     */
    public function getCountSubPartnersLastMonth(string $date, ?int $idClient = null): int
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        $result = $this->createQueryBuilder('s')
            ->select('count(s.id) as nbSubPartners')
            ->andWhere('s.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('s.date >= :date')
            ->setParameter('date', $date)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
        if ($result === null) {
            $result = 0;
        }
        return $result;
    }
}
