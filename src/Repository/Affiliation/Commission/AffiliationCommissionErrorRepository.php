<?php

namespace MatGyver\Repository\Affiliation\Commission;

use MatGyver\Entity\Affiliation\Commission\AffiliationCommissionError;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method AffiliationCommissionError|null find($id, $lockMode = null, $lockVersion = null)
 * @method AffiliationCommissionError|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method AffiliationCommissionError[]    findAll()
 * @method AffiliationCommissionError[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class AffiliationCommissionErrorRepository extends AbstractEntityRepository
{

}
