<?php

namespace MatGyver\Repository\Affiliation\Commission;

use MatGyver\Entity\Affiliation\Commission\AffiliationCommission;
use MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method AffiliationCommission|null find($id, $lockMode = null, $lockVersion = null)
 * @method AffiliationCommission|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method AffiliationCommission[]    findAll()
 * @method AffiliationCommission[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class AffiliationCommissionRepository extends AbstractEntityRepository
{
    /**
     * @param int $idPartner
     * @return AffiliationCommission[]
     */
    public function findCommissionsToPay(int $idPartner): array
    {
        return $this->createQueryBuilder('c')
            ->leftJoin('c.invoices', 'i')
            ->andWhere('c.partner = :partner')
            ->andWhere('c.status = :commissionStatusValid')
            ->andWhere('c.paid = :paid')
            ->andWhere('i IS NULL OR (i.status NOT IN (:statusSent, :statusValidated, :statusPaid))')
            ->setParameters([
                'partner' => $idPartner,
                'statusSent' => AffiliationInvoice::STATUS_SENT,
                'statusValidated' => AffiliationInvoice::STATUS_VALIDATED,
                'statusPaid' => AffiliationInvoice::STATUS_PAID,
                'commissionStatusValid' => AffiliationCommission::STATE_VALID,
                'paid' => 0,
            ])
            ->andWhere('c.date < DATE_ADD(CURRENT_TIMESTAMP(), \'-' . COMMISSIONS_AVAILABLE_DELAY . '\', \'DAY\')')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $idPartner
     * @return float
     */
    public function getCommissionsAmountToPay(int $idPartner): float
    {
        $result = $this->createQueryBuilder('c')
            ->select('SUM(c.commission) as amount')
            ->leftJoin('c.invoices', 'i')
            ->andWhere('c.partner = :partner')
            ->andWhere('c.status = :commissionStatusValid')
            ->andWhere('c.paid = :paid')
            ->andWhere('i IS NULL OR (i.status NOT IN (:statusSent, :statusValidated, :statusPaid))')
            ->setParameters([
                'partner' => $idPartner,
                'statusSent' => AffiliationInvoice::STATUS_SENT,
                'statusValidated' => AffiliationInvoice::STATUS_VALIDATED,
                'statusPaid' => AffiliationInvoice::STATUS_PAID,
                'commissionStatusValid' => AffiliationCommission::STATE_VALID,
                'paid' => 0,
            ])
            ->andWhere('c.date < DATE_ADD(CURRENT_TIMESTAMP(), \'-' . COMMISSIONS_AVAILABLE_DELAY . '\', \'DAY\')')
            ->getQuery()
            ->getSingleScalarResult();
        if ($result === null) {
            $result = 0;
        }
        return $result;
    }

    /**
     * @return array
     */
    public function getCommissionsAmountsToPayGroupByPartner(): array
    {
        return $this->createQueryBuilder('c')
            ->select('SUM(c.commission) as amount, IDENTITY(c.partner) as idPartner')
            ->leftJoin('c.invoices', 'i')
            ->andWhere('c.status = :commissionStatusValid')
            ->andWhere('c.paid = :paid')
            ->andWhere('i IS NULL OR (i.status NOT IN (:statusSent, :statusValidated, :statusPaid))')
            ->andWhere('c.date < DATE_ADD(CURRENT_TIMESTAMP(), \'-' . COMMISSIONS_AVAILABLE_DELAY . '\', \'DAY\')')
            ->setParameters([
                'statusSent' => AffiliationInvoice::STATUS_SENT,
                'statusValidated' => AffiliationInvoice::STATUS_VALIDATED,
                'statusPaid' => AffiliationInvoice::STATUS_PAID,
                'commissionStatusValid' => AffiliationCommission::STATE_VALID,
                'paid' => 0,
            ])
            ->groupBy('c.partner')
            ->getQuery()
            ->getArrayResult();
    }

    /**
     * @param int $idPartner
     * @return float|null
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getValidCommissionsAmountByPartner(int $idPartner): ?float
    {
        return $this->createQueryBuilder('c')
            ->select('SUM(c.commission) as amount')
            ->where('c.status = :commissionStatusValid')
            ->andWhere('c.partner = :idPartner')
            ->setParameters([
                'idPartner' => $idPartner,
                'commissionStatusValid' => AffiliationCommission::STATE_VALID,
            ])
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @return array
     */
    public function getValidCommissionsAmountGroupByPartner(): array
    {
        return $this->createQueryBuilder('c')
            ->select('SUM(c.commission) as amount, IDENTITY(c.partner) as idPartner')
            ->where('c.status = :commissionStatusValid')
            ->setParameter('commissionStatusValid', AffiliationCommission::STATE_VALID)
            ->groupBy('c.partner')
            ->getQuery()
            ->getArrayResult();
    }

    /**
     * @param array $idsCommissions
     * @return int|mixed|string
     */
    public function findByCommissionsIds(array $idsCommissions)
    {
        return $this->createQueryBuilder('c')
            ->where('c.id IN (:ids)')
            ->setParameter('ids', $idsCommissions)
            ->getQuery()->getResult();
    }

    /**
     * @param int|null $idPartner
     * @param int $startDelay
     * @param int $endDelay
     * @return int|mixed|string|void
     */
    public function findCommissionsToExpire(int $idPartner = null, int $startDelay = 0, int $endDelay = 0)
    {
        $qb = $this->createQueryBuilder('c')
            ->leftJoin('c.invoices', 'i')
            ->andWhere('c.status = :commissionStatusValid')
            ->andWhere('c.paid = :paid')
            ->andWhere('i IS NULL OR (i.status NOT IN (:statusSent, :statusValidated, :statusPaid))')
            ->setParameters([
                'statusSent' => AffiliationInvoice::STATUS_SENT,
                'statusValidated' => AffiliationInvoice::STATUS_VALIDATED,
                'statusPaid' => AffiliationInvoice::STATUS_PAID,
                'commissionStatusValid' => AffiliationCommission::STATE_VALID,
                'paid' => false,
            ]);

        if ($idPartner) {
            $qb->andWhere('c.partner = :idPartner')
                ->setParameter('idPartner', $idPartner);
        }

        if ($startDelay or $endDelay) {
            if ($startDelay < $endDelay) {
                return;
            }
            $dateNow = new \DateTime();
            $dateStart = clone $dateNow;
            $dateStart->modify('-' . $startDelay . ' days');
            $dateEnd = clone $dateNow;
            $dateEnd->modify('-' . $endDelay . ' days');
            $qb->andWhere('c.date BETWEEN :dateStart AND :dateEnd')
                ->setParameter('dateStart', $dateStart)
                ->setParameter('dateEnd', $dateEnd);
        } else {
            $date = new \DateTime();
            $date->modify('-' . COMMISSIONS_EXPIRE_DELAY . ' days');
            $qb->andWhere('c.date < :date')
                ->setParameter('date', $date);
        }

        return $qb->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $idPartner
     * @param string|null $dateStart
     * @param string|null $dateEnd
     * @param string|null $status
     * @return array|null
     */
    public function getAllCommissions(?int $idPartner = null, ?string $dateStart = null, ?string $dateEnd = null, ?string $status = null): ?array
    {
        $qb = $this->createQueryBuilder('c');

        if ($idPartner) {
            $qb->andWhere('c.partner = :partner')
                ->setParameter('partner', $idPartner);
        }
        if ($dateStart) {
            $qb->andWhere('c.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('c.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }
        if ($status) {
            $qb->andWhere('c.status = :status')
                ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int|null $idPartner
     * @param bool|null $valid
     * @param string|null $dateStart
     * @param string|null $dateEnd
     * @return int
     */
    public function getCountAllCommissions(?int $idPartner = null, ?bool $valid = null, ?string $dateStart = null, ?string $dateEnd = null): int
    {
        $qb = $this->createQueryBuilder('c')
            ->select('count(c.id)');

        if ($idPartner) {
            $qb->andWhere('c.partner = :partner')
                ->setParameter('partner', $idPartner);
        }
        if ($valid !== null) {
            $qb->andWhere('c.valid = :valid')
                ->setParameter('valid', $valid);
        }
        if ($dateStart) {
            $qb->andWhere('c.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('c.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }

        $result = $qb->getQuery()->getSingleScalarResult();
        if ($result === null) {
            $result = 0;
        }
        return $result;
    }

    /**
     * @param int $idPartner
     * @param int $limit
     * @return array
     */
    public function getLastCommissionsByPartner(int $idPartner, int $limit = 10): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.partner = :partner')
            ->setParameter('partner', $idPartner)
            ->setMaxResults($limit)
            ->orderBy('c.date', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string|null $dateStart
     * @param string|null $dateEnd
     * @return array
     */
    public function getCountCommissionsGroupByPartner(?string $dateStart = null, ?string $dateEnd = null): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('IDENTITY(c.partner) as partnerId, count(c.id) as nbCommissions')
            ->andWhere('c.status = :status')
            ->setParameter('status', AffiliationCommission::STATE_VALID)
            ->groupBy('partnerId');

        if ($dateStart) {
            $qb->andWhere('c.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('c.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int|null $idPartner
     * @param string $status
     * @param string $dateStart
     * @param string $dateEnd
     * @param string $search
     * @param string $orderVar
     * @param string $orderDir
     * @param int $start
     * @param int $length
     * @return AffiliationCommission[]
     */
    public function findCommissions(?int $idPartner = null, string $status = '', string $dateStart = '', string $dateEnd = '', string $search = '', string $orderVar = '', string $orderDir = '', int $start = 0, int $length = 0): array
    {
        $qb = $this->createQueryBuilder('c')
            ->leftJoin('c.partner', 'p');

        if ($dateStart) {
            $qb->andWhere('c.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('c.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }
        if ($status) {
            $qb->andWhere('c.status = :status')
                ->setParameter('status', $status);
        }
        if ($idPartner) {
            $qb->andWhere('c.partner = :partner')
                ->setParameter('partner', $idPartner);
        }
        if ($search) {
            $columns = [
                'c.id',
                'c.transactionReference',
                'p.lastName',
                'p.firstName',
                'p.email',
                'c.product',
                'c.commission',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }
        if ($orderVar and $orderDir) {
            $qb->orderBy($orderVar, $orderDir);
        }
        if ($start or $length) {
            $qb->setFirstResult($start);
            $qb->setMaxResults($length);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int|null $idPartner
     * @param string $status
     * @param string $dateStart
     * @param string $dateEnd
     * @param string $search
     * @return int
     */
    public function countFindCommissions(?int $idPartner = null, string $status = '', string $dateStart = '', string $dateEnd = '', string $search = ''): int
    {
        $qb = $this->createQueryBuilder('c')
            ->select('count(c.id) as nb')
            ->leftJoin('c.partner', 'p');

        if ($dateStart) {
            $qb->andWhere('c.date >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('c.date <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }
        if ($status) {
            $qb->andWhere('c.status = :status')
                ->setParameter('status', $status);
        }
        if ($idPartner) {
            $qb->andWhere('c.partner = :partner')
                ->setParameter('partner', $idPartner);
        }
        if ($search) {
            $columns = [
                'c.id',
                'c.transactionReference',
                'p.lastName',
                'p.firstName',
                'p.email',
                'c.product',
                'c.commission',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
