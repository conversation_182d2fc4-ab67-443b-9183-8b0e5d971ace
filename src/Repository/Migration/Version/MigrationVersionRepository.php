<?php

namespace MatGyver\Repository\Migration\Version;

use MatGyver\Entity\Migration\Version\MigrationVersion;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method MigrationVersion|null find($id, $lockMode = null, $lockVersion = null)
 * @method MigrationVersion|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method MigrationVersion[]    findAll()
 * @method MigrationVersion[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class MigrationVersionRepository extends AbstractEntityRepository
{

}
