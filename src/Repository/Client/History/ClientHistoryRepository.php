<?php
namespace MatGyver\Repository\Client\History;

use Mat<PERSON>yver\Entity\Client\History\ClientHistory;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ClientHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClientHistory|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ClientHistory[]    findAll()
 * @method ClientHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ClientHistoryRepository extends AbstractEntityRepository
{

}
