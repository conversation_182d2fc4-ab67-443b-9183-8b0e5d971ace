<?php

namespace MatGyver\Repository\Client\Subscription;

use Mat<PERSON><PERSON>ver\Entity\Client\Subscription\ClientSubscription;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ClientSubscription|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClientSubscription|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ClientSubscription[]    findAll()
 * @method ClientSubscription[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ClientSubscriptionRepository extends AbstractEntityRepository
{
    /**
     * @param string $type
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionByType(string $type, ?int $idClient = null): ?ClientSubscription
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->createQueryBuilder('a')
            ->leftJoin('a.product', 'p')
            ->andWhere('a.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('p.type = :type')
            ->setParameter('type', $type)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $type
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getSubscriptionActiveByType(string $type, ?int $idClient = null): ?ClientSubscription
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->createQueryBuilder('a')
            ->leftJoin('a.product', 'p')
            ->andWhere('a.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('a.status = :status')
            ->setParameter('status', 'active')
            ->andWhere('p.type = :type')
            ->setParameter('type', $type)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $type
     * @param int|null $idClient
     * @return array
     */
    public function getAllSubscriptionsActiveByType(string $type, ?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('a')
            ->leftJoin('a.product', 'p')
            ->andWhere('a.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('a.status = :status')
            ->setParameter('status', 'active')
            ->andWhere('p.type = :type')
            ->setParameter('type', $type)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $type
     * @param int|null $idClient
     * @return ClientSubscription|null
     */
    public function getLastSubscriptionByType(string $type, ?int $idClient = null): ?ClientSubscription
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->createQueryBuilder('a')
            ->leftJoin('a.product', 'p')
            ->andWhere('a.client = :client')
            ->setParameter('client', $idClient)
            ->andWhere('p.type = :type')
            ->setParameter('type', $type)
            ->setMaxResults(1)
            ->orderBy('a.id', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();
    }
}
