<?php

namespace MatGyver\Repository\Country;

use MatGyver\Entity\Country\Country;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Country|null find($id, $lockMode = null, $lockVersion = null)
 * @method Country|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Country[]    findAll()
 * @method Country[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class CountryRepository extends AbstractEntityRepository
{

}
