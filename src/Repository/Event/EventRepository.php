<?php

namespace MatGyver\Repository\Event;

use MatGyver\Entity\Event\Event;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Event|null find($id, $lockMode = null, $lockVersion = null)
 * @method Event|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Event[]    findAll()
 * @method Event[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class EventRepository extends AbstractEntityRepository
{
    /**
     * @return array|null
     */
    public function getActiveEvents(): ?array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.active = :active')
            ->setParameter('active', true)
            ->orderBy('e.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Event|null
     */
    public function getOneActiveWithBg(): ?Event
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.active = :active')
            ->andWhere('e.dateStart <= :date')
            ->andWhere('e.bgImage != \'\'')
            ->andWhere('e.dateEnd IS NULL or e.dateEnd >= :date')
            ->setParameters([
                'active' => true,
                'date' => new \DateTime(),
            ])
            ->orderBy('e.id', 'ASC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
