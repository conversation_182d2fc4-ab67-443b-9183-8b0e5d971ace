<?php
namespace MatGyver\Repository\Request;

use MatGyver\Entity\Request\RequestPage;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method RequestPage|null find($id, $lockMode = null, $lockVersion = null)
 * @method RequestPage|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method RequestPage[]    findAll()
 * @method RequestPage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RequestPageRepository extends AbstractEntityRepository
{

}
