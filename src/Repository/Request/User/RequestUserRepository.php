<?php
namespace MatGyver\Repository\Request\User;

use MatGyver\Entity\Request\User\RequestUser;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method RequestUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method RequestUser|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method RequestUser[]    findAll()
 * @method RequestUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RequestUserRepository extends AbstractEntityRepository
{

}
