<?php

namespace MatGyver\Repository\User;

use MatGyver\Entity\User\UserEmailNotification;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method UserEmailNotification|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserEmailNotification|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method UserEmailNotification[]    findAll()
 * @method UserEmailNotification[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class UserEmailNotificationRepository extends AbstractEntityRepository
{
    /**
     * @param int $userId
     * @return mixed
     */
    public function getByUserIndexedByType(int $userId)
    {
        return $this->createQueryBuilder('ue', 'ue.type')
            ->where('ue.user = :userId')
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getResult();
    }
}
