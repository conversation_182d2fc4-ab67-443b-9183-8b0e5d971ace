<?php
namespace MatGyver\Repository\Calendar;

use DateTime;
use <PERSON>G<PERSON><PERSON>\Entity\Calendar\Calendar;
use <PERSON><PERSON><PERSON>ver\Entity\Calendar\CalendarAvailability;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method CalendarAvailability|null find($id, $lockMode = null, $lockVersion = null)
 * @method CalendarAvailability|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method CalendarAvailability[]    findAll()
 * @method CalendarAvailability[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class CalendarAvailabilityRepository extends AbstractEntityRepository
{
    /**
     * @param DateTime $start
     * @param DateTime $end
     * @return CalendarAvailability[]
     */
    public function findByDates(DateTime $start, DateTime $end): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('a.dateStart >= :dateStart')
            ->setParameter('dateStart', $start)
            ->andWhere('a.dateStart <= :dateEnd')
            ->setParameter('dateEnd', $end)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param DateTime $start
     * @return CalendarAvailability[]
     */
    public function findRecurringByDates(DateTime $start): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.client = :client')
            ->setParameter('client', $_SESSION['client']['id'])
            ->andWhere('a.dateStart < :dateStart')
            ->setParameter('dateStart', $start)
            ->andWhere('a.recurring = :recurring')
            ->setParameter('recurring', true)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Calendar $calendar
     * @param DateTime $start
     * @param DateTime $end
     * @return CalendarAvailability[]
     */
    public function findByCalendarAndDates(Calendar $calendar, DateTime $start, DateTime $end): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.client = :client')
            ->setParameter('client', $calendar->getClient())
            ->andWhere('a.calendar = :calendar')
            ->setParameter('calendar', $calendar)
            ->andWhere('a.dateStart >= :dateStart')
            ->setParameter('dateStart', $start)
            ->andWhere('a.dateStart <= :dateEnd')
            ->setParameter('dateEnd', $end)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Calendar $calendar
     * @param DateTime $start
     * @return CalendarAvailability[]
     */
    public function findRecurringByCalendarAndDates(Calendar $calendar, DateTime $start): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.client = :client')
            ->setParameter('client', $calendar->getClient())
            ->andWhere('a.calendar = :calendar')
            ->setParameter('calendar', $calendar)
            ->andWhere('a.dateStart < :dateStart')
            ->setParameter('dateStart', $start)
            ->andWhere('a.recurring = :recurring')
            ->setParameter('recurring', true)
            ->getQuery()
            ->getResult();
    }
}
