<?php
namespace MatGyver\Repository\Calendar;

use Mat<PERSON>yver\Entity\Calendar\CalendarAppointmentHistory;
use MatG<PERSON>ver\Repository\AbstractEntityRepository;

/**
 * @method CalendarAppointmentHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CalendarAppointmentHistory|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method CalendarAppointmentHistory[]    findAll()
 * @method CalendarAppointmentHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class CalendarAppointmentHistoryRepository extends AbstractEntityRepository
{

}
