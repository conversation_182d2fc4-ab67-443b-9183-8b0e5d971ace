<?php

namespace MatGyver\Repository\Shop\Autoresponder;

use MatGyver\Entity\Shop\Autoresponder\ShopAutoresponder;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopAutoresponder|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopAutoresponder|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopAutoresponder[]    findAll()
 * @method ShopAutoresponder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopAutoresponderRepository extends AbstractEntityRepository
{

}
