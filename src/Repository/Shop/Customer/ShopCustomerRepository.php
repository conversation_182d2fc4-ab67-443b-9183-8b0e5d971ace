<?php

namespace Mat<PERSON><PERSON>ver\Repository\Shop\Customer;

use Mat<PERSON><PERSON>ver\Entity\Shop\Cart\ShopCart;
use Mat<PERSON><PERSON><PERSON>\Entity\Shop\Cart\ShopCartProduct;
use MatGyver\Entity\Shop\Customer\ShopCustomer;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopCustomer|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopCustomer|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopCustomer[]    findAll()
 * @method ShopCustomer[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopCustomerRepository extends AbstractEntityRepository
{
    /**
     * @param int|null $idProduct
     * @param string $search
     * @param string $dateStart
     * @param string $dateEnd
     * @param string $orderVar
     * @param string $orderDir
     * @param int $start
     * @param int $length
     * @return ShopCustomer[]
     */
    public function searchCustomers(?int $idProduct = null, string $search = '', string $dateStart = '', string $dateEnd = '', string $orderVar = 'date', string $orderDir = 'DESC', int $start = 0, int $length = 10): array
    {
        $qb = $this->createQueryBuilder('c')
            ->andWhere('c.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($idProduct) {
            $qb->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
                ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
                ->andWhere('cartProduct.shopProduct = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = [
                'c.lastName',
                'c.firstName',
                'c.email',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        if ($dateStart) {
            $qb->andWhere("c.date >= :dateStart")
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere("c.date <= :dateEnd")
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->orderBy('c.' . $orderVar, $orderDir)
            ->setFirstResult($start)
            ->setMaxResults($length)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $idProduct
     * @param string $search
     * @param string $dateStart
     * @param string $dateEnd
     * @return int
     */
    public function countSearchCustomers(?int $idProduct = null, string $search = '', string $dateStart = '', string $dateEnd = ''): int
    {
        $qb = $this->createQueryBuilder('c')
            ->select('count(c.id) as nb')
            ->andWhere('c.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($idProduct) {
            $qb->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
                ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
                ->andWhere('cartProduct.shopProduct = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = [
                'c.lastName',
                'c.firstName',
                'c.email',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        if ($dateStart) {
            $qb->andWhere("c.date >= :dateStart")
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere("c.date <= :dateEnd")
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param int|null $idProduct
     * @param string $search
     * @param string $orderVar
     * @param string $orderDir
     * @param int $start
     * @param int $length
     * @return ShopCustomer[]
     */
    public function searchProspects(?int $idProduct = null, string $search = '', string $orderVar = 'date', string $orderDir = 'DESC', int $start = 0, int $length = 10): array
    {
        $qb = $this->createQueryBuilder('c')
            ->leftJoin(ShopTransaction::class, 't', 'c = t.customer')
            ->andWhere('t IS NULL')
            ->andWhere('c.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($idProduct) {
            $qb->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
                ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
                ->andWhere('cartProduct.shopProduct = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = [
                'c.lastName',
                'c.firstName',
                'c.email',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->orderBy('c.' . $orderVar, $orderDir)
            ->setFirstResult($start)
            ->setMaxResults($length)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $idProduct
     * @param string $search
     * @return int
     */
    public function countSearchProspects(?int $idProduct = null, string $search = ''): int
    {
        $qb = $this->createQueryBuilder('c')
            ->select('count(c.id) as nb')
            ->leftJoin(ShopTransaction::class, 't', 'c = t.customer')
            ->andWhere('t IS NULL')
            ->andWhere('c.client = :client')
            ->setParameter('client', $_SESSION['client']['id']);

        if ($idProduct) {
            $qb->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
                ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
                ->andWhere('cartProduct.shopProduct = :product')
                ->setParameter('product', $idProduct);
        }

        if ($search) {
            $columns = [
                'c.lastName',
                'c.firstName',
                'c.email',
            ];
            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param int|null $idProduct
     * @param int|null $idClient
     * @return ShopCustomer[]
     */
    public function getCustomersByProduct(int $idProduct, ?int $idClient = null): array
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->createQueryBuilder('c')
            ->select('c')
            ->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
            ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
            ->andWhere('cartProduct.shopProduct = :product')
            ->setParameter('product', $idProduct)
            ->andWhere('c.client = :client')
            ->setParameter('client', $idClient)
            ->groupBy('c.id')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int|null $idProduct
     * @param int|null $idClient
     * @return int
     */
    public function getCountCustomersByProduct(int $idProduct, ?int $idClient = null): int
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }
        return $this->createQueryBuilder('c')
            ->select('COUNT(DISTINCT(c.id)) as nb')
            ->leftJoin(ShopCart::class, 'cart', \Doctrine\ORM\Query\Expr\Join::WITH, 'cart.customer = c')
            ->leftJoin(ShopCartProduct::class, 'cartProduct', \Doctrine\ORM\Query\Expr\Join::WITH, 'cartProduct.shopCart = cart')
            ->andWhere('cartProduct.shopProduct = :product')
            ->setParameter('product', $idProduct)
            ->andWhere('c.client = :client')
            ->setParameter('client', $idClient)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
