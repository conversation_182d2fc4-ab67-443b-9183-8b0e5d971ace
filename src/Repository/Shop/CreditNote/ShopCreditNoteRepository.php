<?php

namespace MatGyver\Repository\Shop\CreditNote;

use MatG<PERSON>ver\Entity\Shop\CreditNote\ShopCreditNote;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ShopCreditNote|null find($id, $lockMode = null, $lockVersion = null)
 * @method ShopCreditNote|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ShopCreditNote[]    findAll()
 * @method ShopCreditNote[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ShopCreditNoteRepository extends AbstractEntityRepository
{
    /**
     * @param string $dateStart
     * @param string $dateEnd
     * @return ShopCreditNote[]
     */
    public function getAllCreditNotesByDate(string $dateStart = '', string $dateEnd = ''): array
    {
        $qb = $this->createQueryBuilder('cn');

        if ($dateStart) {
            $qb->andWhere('cn.dateCreation >= :dateStart')
                ->setParameter('dateStart', $dateStart);
        }
        if ($dateEnd) {
            $qb->andWhere('cn.dateCreation <= :dateEnd')
                ->setParameter('dateEnd', $dateEnd);
        }

        return $qb->getQuery()
            ->getResult();
    }

    /**
     * @return array
     */
    public function getCreditNoteAmountByTransaction(): array
    {
        return $this->createQueryBuilder('cn')
            ->select('cn.transactionReference, SUM(cn.amountTaxIncl) AS total')
            ->groupBy('cn.transactionReference')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $reference
     * @return float|null
     */
    public function getCreditNoteAmountByTransactionId(string $reference): ?float
    {
        return $this->createQueryBuilder('cn')
            ->select('SUM(cn.amountTaxIncl) AS total')
            ->andWhere('cn.transactionReference = :reference')
            ->setParameter('reference', $reference)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param string $search
     * @param string $orderVar
     * @param string $orderDir
     * @param int $start
     * @param int $length
     * @return ShopCreditNote[]
     */
    public function searchCreditNotes(string $search = '', string $orderVar = 'number', string $orderDir = 'DESC', int $start = 0, int $length = 10): array
    {
        $qb = $this->createQueryBuilder('cn');

        if ($search) {
            $columns = [
                'cn.number',
                'cn.customer',
                'cn.transactionReference',
                'cn.product',
                'cn.amountTaxIncl',
                'cn.paymentMethod',
            ];

            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->orderBy('cn.' . $orderVar, $orderDir)
            ->setFirstResult($start)
            ->setMaxResults($length)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $search
     * @return int
     */
    public function countSearchCreditNotes(string $search = ''): int
    {
        $qb = $this->createQueryBuilder('cn')
            ->select('count(cn.id) as nb');

        if ($search) {
            $columns = [
                'cn.number',
                'cn.customer',
                'cn.transactionReference',
                'cn.product',
                'cn.amountTaxIncl',
                'cn.paymentMethod',
            ];

            $searchCriteria = '(' . implode(' LIKE :search OR ', $columns) . ' LIKE :search)';
            $qb->andWhere($searchCriteria)
                ->setParameter('search', '%' . $search . '%');
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
