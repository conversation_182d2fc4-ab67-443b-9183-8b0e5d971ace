<?php

namespace MatGyver\Repository\Version;

use MatGyver\Entity\Version\VersionClient;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method VersionClient|null find($id, $lockMode = null, $lockVersion = null)
 * @method VersionClient|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method VersionClient[]    findAll()
 * @method VersionClient[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class VersionClientRepository extends AbstractEntityRepository
{
    /**
     * @param int|null $idClient
     * @return VersionClient|null
     */
    public function getLastViewByClient(?int $idClient = null): ?VersionClient
    {
        if ($idClient === null) {
            $idClient = $_SESSION['client']['id'];
        }

        return $this->createQueryBuilder('vc')
            ->andWhere('vc.client = :client')
            ->setParameter('client', $idClient)
            ->orderBy('vc.id', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();
    }
}
