<?php

namespace MatGyver\Repository\PageEditor;

use MatGyver\Entity\PageEditor\PageEditor;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method PageEditor|null find($id, $lockMode = null, $lockVersion = null)
 * @method PageEditor|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method PageEditor[]    findAll()
 * @method PageEditor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class PageEditorUserRepository extends AbstractEntityRepository
{
    public function findPageEditor(?int $idObject = null, ?int $idClient = null)
    {
        return $this->findOneBy(['client' => $idClient]);
    }
}
