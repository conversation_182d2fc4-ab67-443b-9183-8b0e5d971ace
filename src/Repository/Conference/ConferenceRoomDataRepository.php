<?php

namespace MatGyver\Repository\Conference;

use MatGyver\Entity\Conference\ConferenceRoomData;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method ConferenceRoomData|null find($id, $lockMode = null, $lockVersion = null)
 * @method ConferenceRoomData|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method ConferenceRoomData[]    findAll()
 * @method ConferenceRoomData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ConferenceRoomDataRepository extends AbstractEntityRepository
{
}
