<?php

namespace MatGyver\Repository\Config;

use MatGyver\Entity\Config\Config;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Config|null find($id, $lockMode = null, $lockVersion = null)
 * @method Config|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Config[]    findAll()
 * @method Config[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ConfigRepository extends AbstractEntityRepository
{
}
