<?php

namespace MatGyver\Repository\Activity;

use MatGyver\Entity\Activity\Activity;
use MatGyver\Repository\AbstractEntityRepository;

/**
 * @method Activity|null find($id, $lockMode = null, $lockVersion = null)
 * @method Activity|null findOneBy(array $criteria, array $orderBy = null, bool $addClientCriteria = true)
 * @method Activity[]    findAll()
 * @method Activity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null, bool $addClientCriteria = true)
 */
class ActivityTransactionRepository extends AbstractEntityRepository
{

}
