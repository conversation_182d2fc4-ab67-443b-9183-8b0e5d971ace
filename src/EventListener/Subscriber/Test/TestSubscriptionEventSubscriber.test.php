<?php
namespace Mat<PERSON><PERSON><PERSON>\EventListener\Subscriber\Test;

use MatG<PERSON><PERSON>\Event\Test\TestSubscriptionEvent;
use <PERSON><PERSON><PERSON><PERSON>\Helpers\Game\GameCondition;
use MatGyver\Services\Game\GameConditionsService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class TestSubscriptionEventSubscriber implements EventSubscriberInterface
{
    /**
     * @var GameConditionsService
     */
    private $conditionsService;

    /**
     * @param GameConditionsService $conditionsService
     */
    public function __construct(
        GameConditionsService $conditionsService
    ) {
        $this->conditionsService = $conditionsService;
    }

    /**
     * @return array
     */
    public static function getSubscribedEvents(): array
    {
        return [
            TestSubscriptionEvent::EVENT_SUBSCRIPTION => 'onSubscription',
        ];
    }

    /**
     * @param TestSubscriptionEvent $event
     * @return void
     */
    public function onSubscription(TestSubscriptionEvent $event)
    {
        $this->conditionsService->checkDailyQuests([GameCondition::TYPE_NB_SUBSCRIPTIONS]);
        $this->conditionsService->checkBadges([GameCondition::TYPE_NB_SUBSCRIPTIONS]);
    }
}
