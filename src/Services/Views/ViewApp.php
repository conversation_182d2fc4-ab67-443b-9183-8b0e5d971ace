<?php

namespace MatGyver\Services\Views;

use Knp\Menu\ItemInterface;
use Mat<PERSON>yver\Entity\Game\GameQuestClient;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use Mat<PERSON>yver\Helpers\View\ViewHeader;
use MatGyver\Menus\AppMenu;
use MatGyver\Services\CsrfService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Events\EventsService;
use MatGyver\Services\Game\GameQuestClientService;
use MatGyver\Services\Game\GameQuestService;
use MatGyver\Services\Game\GameQuestStepService;
use MatGyver\Services\Help\HelpUniverseService;
use MatGyver\Services\TwigService;
use MatGyver\Services\UniversesService;
use DI\Attribute\Inject as Inject;

/**
 * Class ViewApp
 * @package MatGyver\Services\Views
 */
class ViewApp extends AbstractView
{
    #[Inject]
    private AppMenu $appMenu;

    #[Inject]
    private UniversesService $universesService;

    #[Inject]
    protected CsrfService $csrfService;

    protected TwigService $parser;

    #[Inject]
    protected ViewHeader $viewHeader;

    #[Inject]
    protected ViewLeftMenu $viewLeftMenu;

    #[Inject]
    protected ViewObjectSubMenu $viewObjectSubMenu;

    /**
     * ViewApp constructor.
     */
    public function __construct()
    {
        $this->parser = TwigService::getInstance();
        parent::__construct();
    }

    public function initData()
    {
        $this->set('title', ($this->data['title'] ?: __('Administration')));
        $this->set('logo', Tools::getLogo('app'));

        $notifications = $this->appMenu->getNotifications();
        $this->set('header_right_menu', $this->appMenu->getRightMenu($notifications));
        $this->set('header_main_menu', $this->appMenu->getMainMenu());
        $this->set('bottom_left_menu', $this->appMenu->getBottomLeftMenu($this->data['route']));
        $this->set('footer_mobile_menu', $this->appMenu->getFooterMobileMenu());
        $this->set('CSRFGuard_token', $this->csrfService->getToken());
        $this->set('universe', $this->universesService->getCurrentUniverse());

        $this->checkEvent();
        if (GAME_ACTIVATED) {
            $this->checkQuest();
        }

        $this->addOffCanvas();
    }

    private function checkEvent()
    {
        if (EVENTS_ACTIVATED) {
            $container = ContainerBuilderService::getInstance();
            $event = $container->get(EventsService::class)->getRepository()->getOneActiveWithBg();
            if ($event) {
                $this->setBackground($event->getBgImage());
            }
        }
    }

    private function checkQuest()
    {
        $container = ContainerBuilderService::getInstance();

        $questId = $stepId = null;
        if (!isset($_GET['quest']) or !isset($_GET['step'])) {
            if (isset($this->data['quest_step']) and $this->data['quest_step']) {
                $step = $container->get(GameQuestStepService::class)->getRepository()->findOneBy(['identifier' => $this->data['quest_step']]);
                if ($step and $step->getQuest()->getActive()) {
                    $stepId = $step->getId();
                    $questId = $step->getQuest()->getId();
                }
            }
        }

        if (isset($_GET['quest'])) {
            $questId = filter_input(INPUT_GET, 'quest', FILTER_VALIDATE_INT);
        }
        if (isset($_GET['step'])) {
            $stepId = filter_input(INPUT_GET, 'step', FILTER_VALIDATE_INT);
        }
        if (!$questId or !$stepId) {
            return;
        }

        $quest = $container->get(GameQuestService::class)->getRepository()->find($questId);
        if (!$quest) {
            return;
        }

        $questClient = $container->get(GameQuestClientService::class)->getRepository()->findOneBy(['quest' => $quest, 'status' => GameQuestClient::STATUS_IN_PROGRESS]);
        if (!$questClient) {
            return;
        }

        $step = $container->get(GameQuestStepService::class)->getRepository()->find($stepId);
        if (!$step or $step->getQuest() !== $quest) {
            return;
        }

        if ($questClient->getStep() !== $step) {
            $container->get(GameQuestClientService::class)->goToStep($quest, $step);
        }

        $container->get(GameQuestStepService::class)->renderDiscussion($step);
    }

    /**
     * @param bool $value
     */
    public function setDefaultContentFullsize(bool $value): void
    {
        if (!isset($this->data['content_fullsize'])) {
            $this->set('content_fullsize', $value);
        }
    }

    public function setBackground(string $image): void
    {
        $this->set('bg_image', $image);
    }

    /**
     * @param string $content
     * @param bool   $quit
     */
    public function render(string $content = '', bool $quit = false): void
    {
        $this->initData();

        if ($this->get('settings_menu') !== null) {
            $content = $this->parser->set('menu', $this->get('settings_menu'))
                ->set('content', $content)
                ->render('layouts/app/settings.php');
        }

        parent::render($content, $quit);
    }

    /**
     * @return void
     */
    protected function renderHeader(): void
    {
        require LAYOUTS_PATH . '/app/header.php';
    }

    /**
     * @return void
     */
    protected function renderFooter(): void
    {
        require LAYOUTS_PATH . '/app/footer.php';
        $this->postFooter();
    }

    /**
     * @return ViewApp
     */
    protected function setMainCss(): ViewApp
    {
        parent::setMainCss();
        Assets::addCss('plugins/datatables.bundle.css');
        Assets::addCss('app/style.css');
        Assets::addCss('app/help.css');
        Assets::addCss('pages/wizard-quickpanel.css');
        return $this;
    }

    /**
     * @return ViewApp
     */
    protected function setMainJs(): ViewApp
    {
        parent::setMainJs();
        Assets::addJs('plugins/datatables/datatables.bundle.js');
        Assets::addJs(APP_URL . '/lib/ckfinder/ckfinder.js'); // use APP_URL to not be loaded with CDN_URL
        Assets::addJs('plugins/ckeditor/ckeditor-classic.js');
        Assets::addJs('app/help.js');
        Assets::addJs('app/app.js');
        Assets::addJs('app/search.js');
        return $this;
    }

    /**
     * @return void
     */
    protected function postFooter(): void
    {
        $this->renderErrorMessages();
        $this->renderSuccessMessages();
        $this->renderInfoMessages();

        echo $this->renderHelpDialog();

        if (isset($_SESSION['help_article'])) {
            echo '
            <script type="text/javascript">
            $(document).ready(function() {
                showHelpArticle(' . $_SESSION['help_article'] . ', ' . $_SESSION['help_universe'] . ');
            });
            </script>';
        }

        //closed tags
        echo '
</body>
</html>';
    }

    /**
     * @return string
     */
    public function renderHelpDialog(): string
    {
        $universe = $this->universesService->getCurrentUniverse();

        $container = ContainerBuilderService::getInstance();
        $helpUniverses = $container->get(HelpUniverseService::class)->renderAppUniverses();
        return $this->parser
            ->set('universe', $universe)
            ->set('helpUniverses', $helpUniverses)
            ->set('action', $this->data['action'])
            ->render('app/help/help_dialog.php');
    }

    protected function renderHeaderComponent(): void
    {
        $header = $this->viewHeader->render();
        $this->set('universe_menu', $header);
    }

    /**
     * @param ItemInterface $menu
     * @return void
     */
    protected function setLeftMenu(ItemInterface $menu): void
    {
        $this->set('left_menu', $this->viewLeftMenu->render($menu, 'app'));
    }

    protected function setObjectSubMenu(ItemInterface $menu): void
    {
        $this->set('object_submenu', $this->viewObjectSubMenu->render($menu, 'app'));
    }

    protected function addOffCanvas()
    {
        Assets::addCss('pages/wizard-quickpanel.css');
        Assets::addJs('common/offcanvas.js');
        $content = TwigService::getInstance()
            ->render(VIEWS_PATH . '/common/display/offcanvas.php');
        Assets::addInlineJs($content);
    }
}
