<?php

namespace MatGyver\Services\Views;

use DI\Attribute\Inject as Inject;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Helpers\Assets;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\FlashBagService;
use MatGyver\Services\Help\HelpService;
use MatGyver\Services\Integration\Services\FacebookPixelsService;
use MatGyver\Services\Integration\Services\GoogleAnalytics\GoogleAnalyticsService;
use MatGyver\Services\Integration\Services\GoogleTagManagerService;
use MatGyver\Services\TwigService;

/**
 * Class AbstractView
 * @package MatGyver\Services\Views
 */
abstract class AbstractView
{
    protected array $data = [
        'title' => '',
        'facebook_pixel_id' => '',
        'facebook_pixel_event' => '',
        'google_analytics_id' => '',
        'google_tag_manager_id' => '',
        'google_optimize_id' => '',
        'is_purchased' => false,
        'reference' => null,
        'breadcrumb' => [],
        'actions' => [],
        'videos' => [],
        'no_footer' => false,
        'route' => '',
        'action' => '',
    ];

    #[Inject]
    protected FlashBagService $flashBagService;

    #[Inject]
    protected ConfigService $configService;

    #[Inject]
    protected HelpService $helpService;

    /**
     * AbstractView constructor.
     */
    public function __construct()
    {
        $this->setMainCss()
            ->setMainJs();
    }

    /**
     * @param string $content (default: '')
     * @param bool $quit (default: false)
     */
    public function render(string $content = '', bool $quit = false): void
    {
        if (!$content) {
            Assets::addCss('common/blank_state.css');
            $content = TwigService::getInstance()->render('common/404.php');
        }

        if (isset($this->data['videos']) and $this->data['videos']) {
            $this->helpService->addModalVideo();
        }

        $this->renderHeader();
        echo $content;
        $this->renderFooter();

        if ($quit) {
            exit();
        }
    }

    /**
     * @param $data
     * @param null $value
     * @return AbstractView
     */
    final public function set($data, $value = null): self
    {
        if (is_array($data)) {
            $this->data = $data;
        } else {
            $this->data[$data] = $value;
        }

        return $this;
    }

    /**
     * @param string $data
     * @return mixed|null
     */
    final public function get(string $data)
    {
        if (isset($this->data[$data])) {
            return $this->data[$data];
        }

        return null;
    }

    /**
     * @return array
     */
    final public function getData(): array
    {
        return $this->data;
    }

    /**
     * @param string $title
     * @param string $link
     * @return AbstractView
     */
    public function addBreadcrumbItem(string $title, string $link = ''): AbstractView
    {
        $this->data['breadcrumb'][] = ['title' => $title, 'link' => $link];
        return $this;
    }

    /**
     * @param string $title
     * @param string $link
     * @param string $class
     * @param string $icon
     * @param string $id
     * @return AbstractView
     */
    public function addAction(string $title, string $link, string $class = '', string $icon = '', string $id = '', string $tooltip = ''): AbstractView
    {
        $this->data['actions'][] = [
            'title' => $title,
            'link' => $link,
            'class' => $class,
            'icon' => $icon,
            'id' => $id,
            'tooltip' => $tooltip
        ];
        return $this;
    }

    /**
     * @param string $title
     * @param string $onClick
     * @param string $class
     * @param string $icon
     * @param string $id
     * @return $this
     */
    public function addActionOnClick(string $title, string $onClick, string $class = '', string $icon = '', string $id = '', string $tooltip = ''): AbstractView
    {
        $this->data['actions'][] = [
            'title' => $title,
            'link' => '',
            'onClick' => $onClick,
            'class' => $class,
            'icon' => $icon,
            'id' => $id,
            'tooltip' => $tooltip
        ];
        return $this;
    }

    /**
     * @param string $title
     * @param string $video
     * @param string $class
     * @param string $icon
     * @return AbstractView
     */
    public function addVideo(string $title, string $video, string $class = '', string $icon = 'fas fa-video'): AbstractView
    {
        $this->data['videos'][] = [
            'title' => $title,
            'video' => $video,
            'class' => $class,
            'icon' => $icon
        ];
        return $this;
    }

    /**
     * @param string $title
     */
    public function setTitle(string $title)
    {
        $this->set('title', $title);
    }

    public function forceTitle(): void
    {
        $this->set('force-title', true);
    }

    /**
     * @param string $containerSize
     */
    public function setContainerSize(string $containerSize)
    {
        $this->set('container-size', $containerSize);
    }

    public function setSmallContainer()
    {
        $this->set('container-size', 'container-small');
    }

    /**
     * Reset CSS & JS
     */
    final public function resetAssets(): void
    {
        Assets::reset();
        $this->setMainCss()
            ->setMainJs();
    }

    /**
     * Reset CSS & JS & stop
     */
    final public function clearAssets(): void
    {
        Assets::reset();
    }

    /**
     * setMainCss function.
     *
     * @return AbstractView
     */
    protected function setMainCss(): AbstractView
    {
        Assets::addCss('plugins/plugins.bundle.css');
        Assets::addCss('common/style.bundle.css');
        return $this;
    }

    /**
     * setMainJs function.
     *
     * @return AbstractView
     */
    protected function setMainJs(): AbstractView
    {
        if (isset($_SESSION['language']) and $_SESSION['language']) {
            Assets::addJs('https://unpkg.com/i18next/dist/umd/i18next.min.js');
            Assets::addJs('common/locales/locale.'. $_SESSION['language'] .'.js');
        }
        Assets::addJs('https://code.iconify.design/iconify-icon/1.0.7/iconify-icon.min.js', null);
        Assets::addJs('common/sprintf.min.js');
        Assets::addJs('common/settings.js');
        Assets::addJs('plugins/plugins.bundle.js');
        Assets::addJs('common/scripts.bundle.js');
        return $this;
    }

    protected function addTarteAuCitron()
    {
        Assets::addJs('plugins/tarteaucitron/tarteaucitron.js', 'assets/js/', true);
        Assets::addJs('plugins/tarteaucitron/config.js', 'assets/js/', true);
    }

    /**
     * renderHeader.
     */
    protected function addTrackingCodes(): void
    {
        if (isset($_SESSION['controller']) and $_SESSION['controller'] == ADMIN_URL) {
            return;
        }

        $container = ContainerBuilderService::getInstance();
        $facebookPixelId = $container->get(ConfigService::class)->findByName(ConfigEnum::FACEBOOK_PIXEL_ID, CLIENT_MASTER);
        if ($facebookPixelId and $facebookPixelId->getValue()) {
            $eventParams = [
                'content_name' => $this->data['title'],
            ];
            $jsFacebookPixels = $container->get(FacebookPixelsService::class)->renderScript($facebookPixelId->getValue(), $this->data['facebook_pixel_event'], $eventParams);
            if ($jsFacebookPixels) {
                Assets::addInlineJs($jsFacebookPixels, true);
            }
        }

        $googleAnalyticsId = $container->get(ConfigService::class)->findByName(ConfigEnum::GOOGLE_ANALYTICS_ID, CLIENT_MASTER);
        if ($googleAnalyticsId and $googleAnalyticsId->getValue()) {
            $jsGoogleAnalytics = $container->get(GoogleAnalyticsService::class)->renderScript($googleAnalyticsId->getValue(), $this->data['google_optimize_id']);
            if ($jsGoogleAnalytics) {
                Assets::addInlineJs($jsGoogleAnalytics, true);
            }
        }

        $googleTagManagerId = $container->get(ConfigService::class)->findByName(ConfigEnum::GOOGLE_TAG_MANAGER_ID, CLIENT_MASTER);
        if ($googleTagManagerId and $googleTagManagerId->getValue()) {
            $jsGoogleTagManager = $container->get(GoogleTagManagerService::class)->renderScript($googleTagManagerId->getValue(), $this->data['is_purchased'], $this->data['reference']);
            $jsGoogleTagManagerNoScript = $container->get(GoogleTagManagerService::class)->renderNoScript($googleTagManagerId->getValue());
            if ($jsGoogleTagManager) {
                Assets::addInlineJs($jsGoogleTagManager, true);
                Assets::addInlineJs($jsGoogleTagManagerNoScript, false);
            }
        }
    }

    protected function setFooter()
    {
        $footer = '';

        $footerConfig = $this->configService->findByName('footer', CLIENT_MASTER);
        if ($footerConfig and $footerConfig->getValue()) {
            $config = json_decode($footerConfig->getValue(), true);
            foreach ($config as $column => $content) {
                if (!$content) {
                    continue;
                }
                $content = str_replace('[[APP_NAME]]', APP_NAME, $content);
                $content = str_replace('[[APP_URL]]', APP_URL, $content);
                $content = str_replace('[[YEAR]]', date('Y'), $content);
                $footer .= '<div class="text-dark order-' . $column . ' order-md-' . $column . '">' . $content . '</div>';
            }
        }

        $this->data['footer'] = $footer;
    }

    protected function renderErrorMessages()
    {
        if ($this->flashBagService->has(FlashBagService::ERROR_TYPE)) {
            $flashBagMessages = $this->flashBagService->get(FlashBagService::ERROR_TYPE);
            foreach ($flashBagMessages as $flashBagMessage) {
                echo '<script>ShowToast(\'error\', \'' . addslashes($flashBagMessage) . '\')</script>';
            }
        }

        // old code
        if (isset($_SESSION['error-message']) and trim($_SESSION['error-message']) != '') {
            $errorMessage = $_SESSION['error-message'];
            unset($_SESSION['error-message']);
            echo '<script>ShowToast(\'error\', \'' . addslashes($errorMessage) . '\')</script>';
        }
    }

    protected function renderSuccessMessages()
    {
        if ($this->flashBagService->has(FlashBagService::SUCCESS_TYPE)) {
            $flashBagMessages = $this->flashBagService->get(FlashBagService::SUCCESS_TYPE);
            foreach ($flashBagMessages as $flashBagMessage) {
                echo '<script>ShowToast(\'success\', \'' . addslashes($flashBagMessage) . '\')</script>';
            }
        }

        if (isset($_SESSION['success-message']) and trim($_SESSION['success-message']) != '') {
            $successMessage = $_SESSION['success-message'];
            unset($_SESSION['success-message']);
            echo '<script>ShowToast(\'success\', \'' . $successMessage . '\')</script>';
        }
    }

    protected function renderInfoMessages()
    {
        if ($this->flashBagService->has(FlashBagService::INFO_TYPE)) {
            $flashBagMessages = $this->flashBagService->get(FlashBagService::INFO_TYPE);
            foreach ($flashBagMessages as $flashBagMessage) {
                echo '<script>ShowToast(\'notice\', \'' . addslashes($flashBagMessage) . '\')</script>';
            }
        }

        if (isset($_SESSION['information-message']) and trim($_SESSION['information-message']) != '') {
            $informationMessage = $_SESSION['information-message'];
            unset($_SESSION['information-message']);
            echo '<script>ShowToast(\'notice\', \'' . addslashes($informationMessage) . '\');</script>' . "\n";
        }
    }

    protected function postFooter(): void
    {
        $this->renderErrorMessages();
        $this->renderSuccessMessages();
        $this->renderInfoMessages();
        echo '
</body>
</html>';
    }

    /**
     * @param string $type
     */
    protected function displayInfosFlashBag(string $type)
    {
        $html = $this->getInfosFlashBagHtml($type);
        if (!empty($html)) {
            echo $html;
        }
    }

    /**
     * @param string $type
     * @return string
     */
    public function getInfosFlashBagHtml(string $type): string
    {
        $message = '';

        if ($this->flashBagService->has($type)) {
            $flashMessages = $this->flashBagService->get($type);

            switch ($type) {
                case FlashBagService::SUCCESS_TYPE:
                    $cssClass = 'alert-light-success';
                    $icon = 'flaticon2-check-mark';
                    break;
                case FlashBagService::NOTICE_TYPE:
                    $cssClass = 'alert-light-info';
                    $icon = 'flaticon-information';
                    break;
                default:
                    $cssClass = 'alert-light-danger';
                    $icon = 'flaticon-warning';
                    break;
            }

            $message = '
                <div class="alert alert-custom alert-notice ' . $cssClass . ' fade show" role="alert">
                    <div class="alert-icon"><i class="' . $icon . '"></i></div>
                    <div class="alert-text">';

            if (count($flashMessages) > 1) {
                $message .= '
                    <ul>' .
                    implode('', array_map(function ($flashMessage) {
                        return '<li>' . $flashMessage . '</li>';
                    }, $flashMessages)) .
                    '</ul>';
            } else {
                $message .= reset($flashMessages);
            }

            $message .= '
                    </div>
                    <div class="alert-close">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true"><i class="ki ki-close"></i></span>
                        </button>
                    </div>
                </div>';
        }

        return $message;
    }

    /**
     * renderFooter function.
     */
    abstract protected function renderHeader();

    /**
     * renderFooter function.
     */
    abstract protected function renderFooter();
}
