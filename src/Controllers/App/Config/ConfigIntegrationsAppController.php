<?php

namespace MatGyver\Controllers\App\Config;

use MatGyver\Factories\BlankStates\ErrorBlankState;
use MatGyver\Forms\Integration\IntegrationAccountsForm;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\ConfigService;
use MatGyver\Services\FlashBagService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Integration\IntegrationsService;
use MatGyver\Services\TwigService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class ConfigIntegrationsAppController
 * @package MatGyver\Controllers\App\Config
 */
class ConfigIntegrationsAppController extends AbstractConfigAppController
{
    #[Route('/app/integrations/', name: 'app_integrations')]
    public function integrations()
    {
        $this->view->setTitle(__('Intégrations'));
        $this->view->addBreadcrumbItem(__('Réglages'), Tools::makeLink('app', 'settings'));

        $integrations = $this->get(IntegrationsService::class)->getAppIntegrations();
        if (!$integrations) {
            $this->display404();
        }

        $settings = $this->get(ConfigService::class)->getConfig();

        foreach ($integrations as $param => $integration) {
            $integrations[$param]['active'] = $this->get(IntegrationAccountsService::class)->isConnected($param);
        }

        $content = $this->parser->set('integrations', $integrations)
            ->set('settings', $settings)
            ->render('app/settings/integrations.php');

        $this->displayOutput($content);
    }

    #[Route('/app/integration/{type}/', name: 'app_integration')]
    public function integration(string $type)
    {
        $this->view->setTitle(__('Intégration'));
        $this->view->addBreadcrumbItem(__('Réglages'), Tools::makeLink('app', 'settings'));
        $this->view->addBreadcrumbItem(__('Intégrations'), Tools::makeLink('app', 'integrations'));

        $integration = $this->get(IntegrationsService::class)->getAppIntegration($type);
        if (!$integration) {
            $this->displayError(__('Cette intégration n\'existe pas.'), Tools::makeLink('app', 'integrations'));
            return;
        }

        $this->view->setTitle($integration['name']);

        if ($type == 'apple_calendar' or $type == 'outlook_calendar') {
            Assets::addJs('common/clipboard.min.js');
            Assets::addJs('common/clipboard-copy.js');
            $service = $this->get(IntegrationAccountsService::class)->getService($integration['service']);
            $calendarLink = $service->getCalendarLink();
            $content = $this->parser->set('calendarLink', $calendarLink)
                ->render('forms/admin/integrations/' . $type . '.php');
            $this->displayOutput($content);
            return;
        }

        $accounts = $this->get(IntegrationAccountsService::class)->getAccountsByType($type);

        $accountIntegrations = [];
        if ($accounts) {
            foreach ($accounts as $account) {
                $accountIntegrations[$account->getId()] = $this->get(IntegrationAccountsService::class)->getCountIntegrationsByAccounts($integration, $account->getId());
            }
        }

        $factory = new ErrorBlankState(__('Aucun compte enregistré'), Tools::makeLink('app', 'integration', 'add/' . $type), __('Ajouter un compte'));
        $factory->setImage(Assets::getImageUrl(($integration['type'] == 'autoresponder' ? 'autoresponders/' : 'integrations/') . $type . '-icon.svg'));
        $noAccount = $factory->render();

        $content = $this->parser->set('param', $type)
            ->set('integration', $integration)
            ->set('accountIntegrations', $accountIntegrations)
            ->set('accounts', $accounts)
            ->set('noAccount', $noAccount)
            ->render('app/settings/integration.php');

        $this->displayOutput($content);
    }

    #[Route("/app/integration/add/{param}/", name: "app_integration_add", methods: ["GET"])]
    #[Route("/app/integration/edit/{param}/{idAccount}/", name: "app_integration_edit", requirements: ['idAccount' => Requirement::POSITIVE_INT], methods: ["GET"])]
    public function integrationEdit(string $param, int $idAccount = 0)
    {
        $this->view->setTitle(__('Ajout d\'un compte'));
        $this->view->addBreadcrumbItem(__('Réglages'), Tools::makeLink('app', 'settings'));
        $this->view->addBreadcrumbItem(__('Intégrations'), Tools::makeLink('app', 'integrations'));

        $integration = $this->get(IntegrationsService::class)->getAppIntegration($param);
        if (!$integration) {
            $this->displayError(__('Cette intégration n\'existe pas.'), Tools::makeLink('app', 'integrations'));
            return;
        }

        $this->view->setTitle(__('Ajout d\'un compte') . ' ' . $integration['name']);

        $formFile = FORMS_PATH . '/admin/integrations/' . $param . '.php';
        if (!file_exists($formFile)) {
            $this->displayError(__('Cette intégration n\'existe pas.'), Tools::makeLink('app', 'integrations'));
            return;
        }

        $service = $this->get(IntegrationAccountsService::class)->getService($integration['service']);
        if (!$service) {
            $this->displayError(__('Cette intégration n\'existe pas.'), Tools::makeLink('app', 'integrations'));
            return;
        }

        $accountData = [];
        $account = null;
        if ($idAccount) {
            $account = $this->get(IntegrationAccountsService::class)->getAccount($idAccount);
            if (!$account or $account->getType() != $param) {
                $this->displayError(__('Cette intégration n\'existe pas.'), Tools::makeLink('app', 'integrations'));
                return;
            }
            $accountData = json_decode($account->getDatas(), true);
        }

        $fields = $this->get(IntegrationAccountsService::class)->getAccountFields($service, $this->submittedData, $accountData);

        $helpFile = VIEWS_PATH . '/admin/help/' . $param . '.php';
        $helpContent = '';
        if (file_exists($helpFile)) {
            $helpContent = TwigService::getInstance()->render($helpFile);
        }

        $defaultName = $integration['name'] . ' #1';
        $integrations = $this->get(IntegrationAccountsService::class)->getAccountsByType($param);
        if ($integrations) {
            $defaultName = $integration['name'] . ' #' . (count($integrations) + 1);
        }

        $formContent = TwigService::getInstance()->set('param', $param)
            ->set('name', $integration['name'])
            ->set('fields', $fields)
            ->set('account', $account)
            ->render($formFile);

        $content = $this->parser->set('param', $param)
            ->set('integration', $integration)
            ->set('formContent', $formContent)
            ->set('helpContent', $helpContent)
            ->set('account', $account)
            ->set('defaultName', $defaultName)
            ->set('returnLink', Tools::makeLink('app', 'integration', $param))
            ->render('forms/admin/integrations/integration.php');

        $this->displayOutput($content);
    }

    #[Route("/app/integration/add/{param}/", name: "app_integration_add_post", methods: ["POST"])]
    public function integrationAddPost(string $param)
    {
        $this->checkRequest();

        $validatePost = $this->get(IntegrationAccountsForm::class)->validatePost($this->submittedData);
        if (!$validatePost['valid']) {
            $this->get(FlashBagService::class)->addError($validatePost['message']);
            $this->integrationEdit($param);
            return;
        }

        $insert = $this->get(IntegrationAccountsForm::class)->insert($this->submittedData);
        if (!$insert['valid']) {
            $this->get(FlashBagService::class)->addError($insert['message']);
            $this->integrationEdit($param);
            return;
        }

        $redirect = Tools::makeLink('app', 'integration', $param);
        if (isset($this->submittedData['redirect']) and $this->submittedData['redirect']) {
            $redirect = $this->submittedData['redirect'];
        }
        $this->displaySuccessAndRedirect(__('Les paramètres ont bien été enregistrés.'), $redirect);
    }

    #[Route("/app/integration/edit/{param}/{idAccount}/", name: "app_integration_edit_post", requirements: ['idAccount' => Requirement::POSITIVE_INT], methods: ["POST"])]
    public function integrationEditPost(string $param, int $idAccount)
    {
        $this->checkRequest();

        $validatePost = $this->get(IntegrationAccountsForm::class)->validatePost($this->submittedData);
        if (!$validatePost['valid']) {
            $this->get(FlashBagService::class)->addError($validatePost['message']);
            $this->integrationEdit($param, $idAccount);
            return;
        }

        $update = $this->get(IntegrationAccountsForm::class)->update($this->submittedData);
        if (!$update['valid']) {
            $this->get(FlashBagService::class)->addError($update['message']);
            $this->integrationEdit($param, $idAccount);
            return;
        }

        $redirect = Tools::makeLink('app', 'integration', $param);
        if (isset($this->submittedData['redirect']) and $this->submittedData['redirect']) {
            $redirect = $this->submittedData['redirect'];
        }
        $this->displaySuccessAndRedirect(__('Les paramètres ont bien été enregistrés.'), $redirect);
    }

    #[Route('/app/integration/remove/{param}/{idAccount}/', name: 'app_integration_remove', requirements: ['idAccount' => Requirement::POSITIVE_INT])]
    public function integrationRemove(string $param, int $idAccount)
    {
        $removeIntegration = $this->get(IntegrationAccountsForm::class)->removeIntegration($idAccount);
        if (!$removeIntegration['valid']) {
            $this->displayErrorAndRedirect($removeIntegration['message'], Tools::makeLink('app', 'integration', $param));
        }

        $this->displaySuccessAndRedirect(__('L\'intégration a bien été supprimée.'), Tools::makeLink('app', 'integration', $param));
    }
}
