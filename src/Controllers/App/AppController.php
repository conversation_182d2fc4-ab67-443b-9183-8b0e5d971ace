<?php

namespace MatGyver\Controllers\App;

use MatGyver\Services\Users\UsersLoginService;
use MatGyver\Helpers\Assets;
use MatGyver\Services\Home\HomeService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class AppController
 * @package MatGyver\Controllers\App
 */
class AppController extends AbstractAppController
{
    #[Route('/app/index/', name: 'app_index')]
    public function index()
    {
        $this->view->setTitle(__('Accueil'));

        Assets::addJs('app/page.js');
        $content = $this->parser->render('app/page.php');
        $this->displayOutput($content);
        return;

        Assets::addJs('app/news.js');
        $output = $this->get(HomeService::class)->render();
        $this->displayOutput($output);
    }

    #[Route('/app/logout/', name: 'app_logout')]
    public function logout(): void
    {
        UsersLoginService::logout();
    }
}
