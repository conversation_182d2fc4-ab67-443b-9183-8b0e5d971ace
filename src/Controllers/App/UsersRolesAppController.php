<?php

namespace Mat<PERSON>yver\Controllers\App;

use Mat<PERSON>yver\Attributes\Universe;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\User\RoleFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Users\AppUsersRolesViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersRolesService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_APP_USERS)]
class UsersRolesAppController extends AbstractAppController
{

    #[Route('/app/roles/', name: 'app_roles')]
    public function roles(): void
    {
        $this->view->setTitle(__('Rôles'));
        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_USERS)) {
            $this->view->addAction(__('Ajouter un rôle'), Tools::makeLink('app', 'role', 'add'));
        }

        $usersRoles = $this->get(UsersRolesService::class)->getAll();
        if (!$usersRoles) {
            $this->displayError(__('Aucun rôle enregistré.'), Tools::makeLink('app', 'role', 'add'));
            return;
        }

        $output = $this->get(AppUsersRolesViewHelper::class)->getContent($usersRoles);
        $this->displayOutputCard($output);
    }

    #[Route('/app/role/add/', name: 'app_role_add')]
    public function roleAdd(): void
    {
        $this->view->setTitle(__('Création d\'un rôle'));
        $this->view->addBreadcrumbItem(__('Rôles'), Tools::makeLink('app', 'roles'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_USERS);

        $userRoleForm = new RoleFormFactory();
        if ($userRoleForm->isSubmitted() and $userRoleForm->isValid()) {
            $process = $userRoleForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_roles');
            }
        }
        $this->displayOutput($this->builderForm->render($userRoleForm));
    }

    #[Route('/app/role/edit/{userRoleId}/', name: 'app_role_edit', requirements: ['userRoleId' => Requirement::POSITIVE_INT])]
    public function roleEdit(int $userRoleId): void
    {
        $this->view->setTitle(__('Modification d\'un rôle'));
        $this->view->addBreadcrumbItem(__('Rôles'), Tools::makeLink('app', 'roles'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_APP_USERS);

        $userRole = $this->get(UsersRolesService::class)->getRepository()->find($userRoleId);
        if (!$userRole) {
            $this->displayError(__('Erreur : ce rôle n\'existe pas.'), Tools::makeLink('app', 'roles'));
            return;
        }

        $userRoleForm = new RoleFormFactory();
        if ($userRoleForm->isSubmitted() and $userRoleForm->isValid()) {
            $process = $userRoleForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_roles');
            }
        }
        $this->displayOutput($this->builderForm->render($userRoleForm, $userRole));
    }

    #[Route('/app/role/delete/{userRoleId}/', name: 'app_role_delete', requirements: ['userRoleId' => Requirement::POSITIVE_INT])]
    public function roleDelete(int $userRoleId): void
    {
        $redirect = Tools::makeLink('app', 'roles');
        $this->denyAccessUnlessGranted(ATTRIBUTE_DELETE, UNIVERSE_APP_USERS);

        $userRole = $this->get(UsersRolesService::class)->getRepository()->find($userRoleId);
        if (!$userRole) {
            $this->displayErrorAndRedirect(__('Erreur : ce rôle n\'existe pas.'), $redirect);
        }

        $deleteUserRole = $this->get(UsersRolesService::class)->removeUserRole($userRoleId);
        if (!$deleteUserRole['valid']) {
            $this->displayErrorAndRedirect($deleteUserRole['message'], $redirect);
        }

        $this->displaySuccessAndRedirect(__('Le rôle a bien été supprimé.'), $redirect);
    }
}
