<?php
namespace Mat<PERSON>yver\Controllers\App\Dossier;

use Mat<PERSON><PERSON><PERSON>\Attributes\Universe;
use MatG<PERSON>ver\Enums\ConfigEnum;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use Mat<PERSON><PERSON>ver\FormsFactory\FormFactory;
use Mat<PERSON><PERSON>ver\FormsFactory\Shop\ShopQuoteFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\DossierQuotesViewHelper;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Quotes\ShopQuotesService;
use MatGyver\Services\Shop\ShopAccountingService;
use MatGyver\Services\Shop\ShopVatRulesService;
use Symfony\Component\Routing\Annotation\Route;

#[Universe(UNIVERSE_APP_SHOP)]
class DossierQuotesAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/quotes/{dossierId}/', name: 'app_dossier_quotes')]
    public function dossierQuotes()
    {
        $this->view->setTitle(__('Devis'));

        $dossier = $this->getDossier();

        $quoteForm = new ShopQuoteFormFactory($dossier);
        $this->builderForm->renderAsPanel(__('Créer un devis'), 'quote', $quoteForm);

        $quotes = $this->get(ShopQuotesService::class)->getRepository()->findBy(['dossier' => $dossier]);
        if (!$quotes) {
            $factory = new EmptyBlankState(
                __('Devis'),
                __('Aucun devis enregistré.'),
            );
            $factory->setButtonId('quote_toggle');
            $factory->setButtonText(__('Créer un devis'));
            $output = $factory->render();
            $this->displayOutput($output);
            return;
        }

        $content = $this->get(DossierQuotesViewHelper::class)->getContent($quotes, $dossier);

        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP)) {
            $this->view->addAction(__('Créer un devis'), '', '', '', 'quote_toggle');
        }

        $this->displayOutput($content);
    }

    #[Route('/app/dossier/quote/add/{dossierId}/', name: 'app_dossier_quote_add')]
    public function dossierQuoteAdd()
    {
        $this->view->setTitle(__('Création d\'un devis'));
        $this->view->addBreadcrumbItem(__('Devis'), Tools::makeLink('app', 'shop', 'quotes'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP);

        $dossier = $this->getDossier();
        $quoteForm = new ShopQuoteFormFactory($dossier);
        if ($quoteForm->isSubmitted() and $quoteForm->isValid()) {
            $process = $quoteForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_quote', ['id' => $process['id'], 'dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($quoteForm));
    }

    #[Route('/app/dossier/quote/edit/{quoteId}/{dossierId}/', name: 'app_dossier_quote_edit')]
    public function dossierQuoteEdit()
    {
        $this->view->setTitle(__('Modification d\'un devis'));
        $this->view->addBreadcrumbItem(__('Devis'), Tools::makeLink('app', 'shop', 'quotes'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_APP_SHOP);

        list($quoteId, $dossierId) = explode('/', $this->param);
        $quoteId = filter_var($quoteId, FILTER_VALIDATE_INT);
        $dossier = $this->getDossier();

        $quote = $this->get(ShopQuotesService::class)->getRepository()->findOneBy(['id' => $quoteId, 'dossier' => $dossier]);
        if (!$quote) {
            $this->displayError(__('Erreur : ce devis n\'existe pas.'), Tools::makeLink('app', 'dossier', 'quotes/' . $dossier->getId()));
            return;
        }
        if ($quote->isSigned()) {
            $this->displayError(__('Ce devis a été signé et n\'est donc plus modifiable.'), Tools::makeLink('app', 'dossier', 'quotes/' . $dossier->getId()));
            return;
        }

        $quoteForm = new ShopQuoteFormFactory($dossier);
        if ($quoteForm->isSubmitted() and $quoteForm->isValid()) {
            $process = $quoteForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_quote', ['id' => $quote->getId(), 'dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($quoteForm, $quote));
    }

    #[Route('/app/dossier/quote/{id}/{dossierId}/', name: 'app_dossier_quote')]
    public function dossierQuote()
    {
        $this->view->setTitle(__('Détail d\'un devis'));
        $this->view->addBreadcrumbItem(__('Devis'), Tools::makeLink('app', 'shop', 'quotes'));

        $dossier = $this->getDossier();

        list($idQuote, $dossierId) = explode('/', $this->param);
        $idQuote = filter_var($idQuote, FILTER_VALIDATE_INT);
        if (!$idQuote) {
            $this->displayError(__('Erreur : impossible de récupérer le devis'), Tools::makeLink('app', 'shop', 'quotes'));
            return;
        }

        $quote = $this->get(ShopQuotesService::class)->getRepository()->findOneBy(['id' => $idQuote, 'dossier' => $dossier]);
        if (!$quote) {
            $this->displayError(__("Erreur : ce devis n'existe pas"), Tools::makeLink('app', 'shop', 'quotes'));
            return;
        }

        $quoteUser = json_decode($quote->getCustomer(), true);

        $newQuotesSettings = $this->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
        if ($newQuotesSettings) {
            $newQuotesSettings = json_decode($newQuotesSettings->getValue(), true);
        }

        $quoteSettings = array();
        if ($quote->getSettings() and $quote->getSettings() != '[]') {
            $quoteSettings = json_decode($quote->getSettings(), true);
        }
        if (!$quoteSettings) {
            $quoteSettings = array();
            $getSettings = $this->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
            if ($getSettings) {
                $quoteSettings = json_decode($getSettings->getValue(), true);
            }

            $config = $this->get(ConfigService::class)->getConfig();
            $quoteSettings['company'] = ($config['company'] ?? APP_NAME);
            $quoteSettings['address'] = ($config['address'] ?? '');
            $quoteSettings['address2'] = ($config['address2'] ?? '');
            $quoteSettings['city'] = ($config['city'] ?? '');
            $quoteSettings['zip'] = ($config['zip'] ?? '');
            $quoteSettings['country'] = ($config['country'] ?? '');
            $quoteSettings['state'] = ($config['state'] ?? '');
            $quoteSettings['telephone'] = ($config['telephone'] ?? '');

            $quote->setSettings(json_encode($quoteSettings));
            try {
                $this->get(ShopQuotesService::class)->persistAndFlush($quote);
            } catch (\Exception $e) {
                $this->displayError(__('Erreur : impossible de sauvegarder les paramètres des devis'), Tools::makeLink('app', 'shop', 'quotes'));
                return;
            }
        }

        $logo = APP_LOGO;
        if ($newQuotesSettings and isset($newQuotesSettings['logo']) and $newQuotesSettings['logo']) {
            $logo = $newQuotesSettings['logo'];
        }

        $bgColor = '#985AED';
        if (isset($newQuotesSettings['bg_color']) and $newQuotesSettings['bg_color']) {
            $bgColor = $newQuotesSettings['bg_color'];
        }

        $details = $this->get(ShopAccountingService::class)->getDetailsForQuote($quote->getId());

        $card = new Card();
        $card->setTitle(__('Devis %s', $quote->getPrefix() . $quote->getNumber()));
        $action = new Action();
        $action->setTitle(__('Retour'));
        $action->setClass('btn-light-primary');
        $action->setHref(Tools::makeLink('app', 'dossier', 'quotes/' . $dossierId));
        $card->addAction($action);
        if (!$quote->isSigned()) {
            $content = __('Ce devis n\'est pas encore signé et peut donc être modifié. Une fois que vous avez terminé, vous pouvez envoyer ce devis pour signature au client. Une fois signé, vous pourrez l\'exporter mais il ne sera plus modifiable.');
            if (isset($_GET['quote_sent'])) {
                $content .= '<div class="alert alert-success mt-4">' . __('Le devis a bien été envoyé par email.') . '</div>';
            }
            $content .= '
            <div class="mt-4">
                <a href="' . Tools::makeLink('app', 'dossier', 'quote/edit/' . $quote->getId() . '/' . $quote->getDossier()->getId()) . '" class="btn btn-light-primary mr-2">' . __('Modifier le devis') . '</a>
                <a href="' . Tools::makeLink('app', 'shop', 'quote/send/' . $quote->getId()) . '" class="btn btn-primary">' . __('Envoyer le devis pour signature') . '</a>
            </div>';
            $card->setBody($content);
        }
        if (RightsService::isGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP)) {
            $action = new Action();
            $action->setTitle(__('Télécharger'));
            $action->setClass('btn-primary ml-4');
            $action->setHref(Tools::makeLink('app', 'shop', 'quote/export/' . $quote->getId()));
            $action->setTarget('_blank');
            $card->addAction($action);
        }
        $content = $card->getContent();

        $vatRates = $this->get(ShopVatRulesService::class)->getVatRates();

        $infosFooter = '';
        if ($quoteSettings and isset($quoteSettings['code_siret']) and $quoteSettings['code_siret']) {
            $infosFooter .= __('N° SIRET') . ' : ' . $quoteSettings['code_siret'] . ' - ';
        }
        if ($quoteSettings and isset($quoteSettings['tva_intra']) and $quoteSettings['tva_intra']) {
            $infosFooter .= __('N° TVA') . ' : ' . $quoteSettings['tva_intra'] . ' - ';
        }
        if ($quoteSettings and isset($quoteSettings['code_naf']) and $quoteSettings['code_naf']) {
            $infosFooter .= __('NAF') . ' : ' . $quoteSettings['code_naf'] . ' - ';
        }
        $infosFooter = rtrim($infosFooter, ' - ');

        $content .= $this->parser->set('quote', $quote)
            ->set('id_quote', $idQuote)
            ->set('quoteUser', $quoteUser)
            ->set('logo', $logo)
            ->set('bgColor', $bgColor)
            ->set('quoteSettings', $quoteSettings)
            ->set('details', $details)
            ->set('vatRates', $vatRates)
            ->set('infosFooter', $infosFooter)
            ->render('admin/shop/quotes/quote.php');
        $this->displayOutput($content);
    }
}
