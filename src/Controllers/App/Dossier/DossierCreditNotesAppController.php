<?php
namespace MatG<PERSON>ver\Controllers\App\Dossier;

use MatG<PERSON>ver\Attributes\Universe;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use Mat<PERSON><PERSON>ver\FormsFactory\FormFactory;
use MatG<PERSON>ver\FormsFactory\Shop\ShopCreditNoteFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\DossierCreditNotesViewHelper;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\CreditNotes\ShopCreditNotesService;
use MatGyver\Services\Shop\ShopAccountingService;
use MatGyver\Services\Shop\ShopVatRulesService;
use MatGyver\Services\Shop\Transaction\ShopTransactionService;
use Symfony\Component\Routing\Annotation\Route;

#[Universe(UNIVERSE_APP_SHOP)]
class DossierCreditNotesAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/credit_notes/{dossierId}/', name: 'app_dossier_credit_notes')]
    public function dossierCreditNotes()
    {
        $this->view->setTitle(__('Avoirs'));

        $dossier = $this->getDossier();

        $creditNoteForm = new ShopCreditNoteFormFactory($dossier);
        $this->builderForm->renderAsPanel(__('Créer un avoir'), 'credit_note', $creditNoteForm);

        $creditNotes = $this->get(ShopCreditNotesService::class)->getRepository()->findBy(['dossier' => $dossier]);
        if (!$creditNotes) {
            $factory = new EmptyBlankState(
                __('Avoirs'),
                __('Aucun avoir enregistré.'),
            );
            $factory->setButtonId('credit_note_toggle');
            $factory->setButtonText(__('Créer un avoir'));
            $output = $factory->render();
            $this->displayOutput($output);
            return;
        }

        $content = $this->get(DossierCreditNotesViewHelper::class)->getContent($creditNotes, $dossier);

        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP)) {
            $this->view->addAction(__('Créer un avoir'), '', '', '', 'credit_note_toggle');
        }

        $this->displayOutput($content);
    }

    #[Route('/app/dossier/credit_note/add/{dossierId}/', name: 'app_dossier_credit_note_add')]
    public function dossierCreditNoteAdd()
    {
        $this->view->setTitle(__('Création d\'un avoir'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_APP_SHOP);

        $dossier = $this->getDossier();
        $creditNoteForm = new ShopCreditNoteFormFactory($dossier);
        if ($creditNoteForm->isSubmitted() and $creditNoteForm->isValid()) {
            $process = $creditNoteForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_credit_note', ['id' => $process['id'], 'dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($creditNoteForm));
    }

    #[Route('/app/dossier/credit_note/edit/{creditNoteId}/{dossierId}/', name: 'app_dossier_credit_note_edit')]
    public function dossierCreditNoteEdit()
    {
        $this->view->setTitle(__('Modification d\'un avoir'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_APP_SHOP);

        list($creditNoteId, $dossierId) = explode('/', $this->param);
        $creditNoteId = filter_var($creditNoteId, FILTER_VALIDATE_INT);
        $dossier = $this->getDossier();

        $creditNote = $this->get(ShopCreditNotesService::class)->getRepository()->findOneBy(['id' => $creditNoteId, 'dossier' => $dossier]);
        if (!$creditNote) {
            $this->displayError(__('Erreur : cet avoir n\'existe pas.'), Tools::makeLink('app', 'dossier', 'credit_notes/' . $dossier->getId()));
            exit();
        }
        if ($creditNote->getLocked()) {
            $this->displayError(__('Erreur : cet avoir est verrouillé et n\'est donc plus modifiable.'), Tools::makeLink('app', 'dossier', 'credit_notes/' . $dossier->getId()));
            exit();
        }

        $creditNoteForm = new ShopCreditNoteFormFactory($dossier);
        if ($creditNoteForm->isSubmitted() and $creditNoteForm->isValid()) {
            $process = $creditNoteForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_credit_note', ['id' => $creditNote->getId(), 'dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($creditNoteForm, $creditNote));
    }

    #[Route('/app/dossier/credit_note/{id}/{dossierId}/', name: 'app_dossier_credit_note')]
    public function dossierCreditNote()
    {
        $this->view->setTitle(__('Détail d\'un avoir'));
        $this->view->addBreadcrumbItem(__('Avoirs'), Tools::makeLink('app', 'shop', 'credit_notes'));

        $dossier = $this->getDossier();

        list($idCreditNote, $dossierId) = explode('/', $this->param);
        $idCreditNote = filter_var($idCreditNote, FILTER_VALIDATE_INT);
        if (!$idCreditNote) {
            $this->displayError(__('Erreur : impossible de récupérer l\'avoir'), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $creditNote = $this->get(ShopCreditNotesService::class)->getRepository()->findOneBy(['id' => $idCreditNote, 'dossier' => $dossier]);
        if (!$creditNote) {
            $this->displayError(__("Erreur : cet avoir n'existe pas"), Tools::makeLink('app', 'shop', 'credit_notes'));
            return;
        }

        $relatedTransactions = array();
        if ($creditNote->getTransactionReference()) {
            $transaction = $this->get(ShopTransactionService::class)->getTransactionByReference($creditNote->getTransactionReference());
            if ($transaction) {
                //$relatedTransactions = $this->get(ShopTransactionService::class)->getRelatedTransactions($transaction->getId());
            }
        }

        $creditNoteUser = json_decode($creditNote->getCustomer(), true);

        $newCreditNotesSettings = $this->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
        if ($newCreditNotesSettings) {
            $newCreditNotesSettings = json_decode($newCreditNotesSettings->getValue(), true);
        }

        $creditNoteSettings = array();
        if ($creditNote->getSettings() and $creditNote->getSettings() != '[]') {
            $creditNoteSettings = json_decode($creditNote->getSettings(), true);
        }
        if (!$creditNoteSettings) {
            $creditNoteSettings = array();
            $getSettings = $this->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
            if ($getSettings) {
                $creditNoteSettings = json_decode($getSettings->getValue(), true);
            }

            $config = $this->get(ConfigService::class)->getConfig();
            $creditNoteSettings['company'] = ($config['company'] ?? APP_NAME);
            $creditNoteSettings['address'] = ($config['address'] ?? '');
            $creditNoteSettings['address2'] = ($config['address2'] ?? '');
            $creditNoteSettings['city'] = ($config['city'] ?? '');
            $creditNoteSettings['zip'] = ($config['zip'] ?? '');
            $creditNoteSettings['country'] = ($config['country'] ?? '');
            $creditNoteSettings['state'] = ($config['state'] ?? '');
            $creditNoteSettings['telephone'] = ($config['telephone'] ?? '');

            $creditNote->setSettings(json_encode($creditNoteSettings));
            try {
                $this->get(ShopCreditNotesService::class)->persistAndFlush($creditNote);
            } catch (\Exception $e) {
                $this->displayError(__('Erreur : impossible de sauvegarder les paramètres de l\'avoir'), Tools::makeLink('app', 'shop', 'credit_notes'));
                return;
            }
        }

        $logo = APP_LOGO;
        if ($newCreditNotesSettings and isset($newCreditNotesSettings['logo']) and $newCreditNotesSettings['logo']) {
            $logo = $newCreditNotesSettings['logo'];
        }

        $bgColor = '#985AED';
        if (isset($newCreditNotesSettings['bg_color']) and $newCreditNotesSettings['bg_color']) {
            $bgColor = $newCreditNotesSettings['bg_color'];
        }

        $details = $this->get(ShopAccountingService::class)->getDetailsForCreditNote($creditNote->getId());

        $card = new Card();
        $card->setTitle(__('Avoir %s', $creditNote->getPrefix() . $creditNote->getNumber()));
        $action = new Action();
        $action->setTitle(__('Retour'));
        $action->setClass('btn-light-primary');
        $action->setHref(Tools::makeLink('app', 'dossier', 'credit_notes/' . $dossierId));
        $card->addAction($action);
        if (!$creditNote->getLocked()) {
            $content = __('Cet avoir n\'est pas finalisé et peut donc être modifié. Une fois que vous avez terminé, vous pouvez finaliser l\'avoir : vous pourrez alors l\'exporter mais il ne sera plus modifiable.');
            $content .= '
            <div class="mt-4">
                <a href="' . Tools::makeLink('app', 'dossier', 'credit_note/edit/' . $creditNote->getId() . '/' . $creditNote->getDossier()->getId()) . '" class="btn btn-light-primary mr-2">' . __('Modifier l\'avoir') . '</a>
                <a href="' . Tools::makeLink('app', 'shop', 'credit_note/lock/' . $creditNote->getId()) . '" class="btn btn-primary">' . __('Finaliser l\'avoir') . '</a>
            </div>';
            $card->setBody($content);
        } else {
            if (RightsService::isGranted(ATTRIBUTE_EXPORT, UNIVERSE_APP_SHOP)) {
                $action = new Action();
                $action->setTitle(__('Télécharger'));
                $action->setClass('btn-primary ml-4');
                $action->setHref(Tools::makeLink('app', 'shop', 'credit_note/export/' . $creditNote->getId()));
                $action->setTarget('_blank');
                $card->addAction($action);
            }
        }
        $content = $card->getContent();

        $vatRates = $this->get(ShopVatRulesService::class)->getVatRates();

        $infosFooter = '';
        if ($creditNoteSettings and isset($creditNoteSettings['code_siret']) and $creditNoteSettings['code_siret']) {
            $infosFooter .= __('N° SIRET') . ' : ' . $creditNoteSettings['code_siret'] . ' - ';
        }
        if ($creditNoteSettings and isset($creditNoteSettings['tva_intra']) and $creditNoteSettings['tva_intra']) {
            $infosFooter .= __('N° TVA') . ' : ' . $creditNoteSettings['tva_intra'] . ' - ';
        }
        if ($creditNoteSettings and isset($creditNoteSettings['code_naf']) and $creditNoteSettings['code_naf']) {
            $infosFooter .= __('NAF') . ' : ' . $creditNoteSettings['code_naf'] . ' - ';
        }
        $infosFooter = rtrim($infosFooter, ' - ');

        $content .= $this->parser->set('creditNote', $creditNote)
            ->set('id_credit_note', $idCreditNote)
            ->set('relatedTransactions', $relatedTransactions)
            ->set('creditNoteUser', $creditNoteUser)
            ->set('logo', $logo)
            ->set('bgColor', $bgColor)
            ->set('creditNoteSettings', $creditNoteSettings)
            ->set('details', $details)
            ->set('vatRates', $vatRates)
            ->set('infosFooter', $infosFooter)
            ->render('admin/shop/credit_notes/credit_note.php');

        $this->displayOutput($content);
    }
}
