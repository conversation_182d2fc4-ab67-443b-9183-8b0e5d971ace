<?php
namespace MatGyver\Controllers\App\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierLoan;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use Mat<PERSON>yver\FormsFactory\FormFactory;
use MatGyver\FormsFactory\Dossier\DossierLoanFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\CardObject\Dossier\DossierDocumentViewHelper;
use MatGyver\Helpers\View\PanelStat\Dossier\DossierLoanPanelStatsHelper;
use MatGyver\Helpers\View\Table\Dossier\DossierLoanViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierLoanService;
use MatGyver\Services\Request\User\RequestUserAnswerService;
use MatG<PERSON>ver\Services\Request\User\RequestUserPageService;
use MatGyver\Services\Request\User\RequestUserService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

class DossierLoanAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/loans/{dossierId}/', name: 'app_dossier_loans')]
    public function dossierLoans()
    {
        $this->view->setTitle(__('Prêts'));

        $dossier = $this->getDossier();

        $dossierLoanForm = new DossierLoanFormFactory($dossier);
        $this->builderForm->renderAsPanel(__('Ajouter un prêt'), 'loan', $dossierLoanForm);

        $dossierLoans = $this->get(DossierLoanService::class)->getRepository()->findBy(['dossier' => $dossier]);
        if (!$dossierLoans) {
            $factory = new EmptyBlankState(
                __('Prêts'),
                __('Aucun prêt enregistré.')
            );
            $factory->setButtonId('loan_toggle');
            $factory->setButtonText(__('Ajouter un prêt'));
            $output = $factory->render();
            $output .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'budgets/revenus/' . $dossier->getId()));
            $this->displayOutput($output);
            return;
        }

        $this->view->addAction(__('Ajouter un prêt'), '', '', '', 'loan_toggle');

        $content = $this->get(DossierLoanViewHelper::class)->getContent($dossierLoans);
        $content .= $this->displayBtnNextStep(Tools::makeLink('app', 'dossier', 'budgets/revenus/' . $dossier->getId()));
        $this->displayOutput($content);
    }

    #[Route('/app/dossier/loan/add/{dossierId}/', name: 'app_dossier_loan_add')]
    public function dossierLoanAdd()
    {
        $this->view->setTitle(__('Création d\'un prêt'));

        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Prêts'), Tools::makeLink('app', 'dossier', 'loans/' . $dossier->getId()));

        $dossierLoanForm = new DossierLoanFormFactory($dossier);
        if ($dossierLoanForm->isSubmitted() and $dossierLoanForm->isValid()) {
            $process = $dossierLoanForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_loans', ['dossierId' => $dossier->getId()]);
            }
        }
        $this->displayOutput($this->builderForm->render($dossierLoanForm));
    }

    #[Route('/app/dossier/loan/edit/{dossierLoanId}/{dossierId}/', name: 'app_dossier_loan_edit', requirements: ['dossierLoanId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierLoanEdit(int $dossierLoanId, int $dossierId)
    {
        $this->view->setTitle(__('Modification d\'un prêt'));

        $dossier = $this->getDossier();
        $this->view->addBreadcrumbItem(__('Prêts'), Tools::makeLink('app', 'dossier', 'loans/' . $dossier->getId()));

        $dossierLoan = $this->getFromRoute($dossier, $dossierLoanId);

        $dossierLoanForm = new DossierLoanFormFactory($dossier);
        if ($dossierLoanForm->isSubmitted() and $dossierLoanForm->isValid()) {
            $process = $dossierLoanForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('app_dossier_loans', ['dossierId' => $dossier->getId()]);
            }
        }
        $output = $this->builderForm->render($dossierLoanForm, $dossierLoan);
        $this->displayOutput($output);
    }

    #[Route('/app/dossier/loan/delete/{dossierLoanId}/{dossierId}/', name: 'app_dossier_loan_delete', requirements: ['dossierLoanId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierLoanDelete(int $dossierLoanId, int $dossierId)
    {
        $dossier = $this->getDossier();
        $dossierLoan = $this->getFromRoute($dossier, $dossierLoanId);
        try {
            $this->get(DossierLoanService::class)->deleteAndFlush($dossierLoan);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce prêt.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('app_dossier_loans', ['dossierId' => $dossier->getId()]);
    }

    #[Route('/app/dossier/loan/view/{dossierLoanId}/{dossierId}/', name: 'app_dossier_loan_view', requirements: ['dossierLoanId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierLoanView(int $dossierLoanId, int $dossierId)
    {
        $dossier = $this->getDossier();
        $dossierLoan = $this->getFromRoute($dossier, $dossierLoanId);

        $content = $this->parser->set('loan', $dossierLoan)
            ->render('app/dossier/loans/header.php');

        $panelStats = new DossierLoanPanelStatsHelper($dossierLoan);
        $content .= $panelStats->getContent();

        $content .= $this->parser->set('loan', $dossierLoan)
            ->render('app/dossier/loans/loan.php');

        if ($dossierLoan->getDocuments()) {
            $card = new Card();
            $card->setTitle(__('Documents'));
            $card->setBody($this->get(DossierDocumentViewHelper::class)->getContent($dossierLoan->getDocuments()));
            $content .= $card->getContent();
        }

        $requestUser = $this->get(RequestUserService::class)->getRepository()->findOneBy(['dossier' => $dossier]);
        if ($requestUser) {
            $requestUserPage = $this->get(RequestUserPageService::class)->getRepository()->findOneBy(['requestUser' => $requestUser, 'loan' => $dossierLoan]);
            if ($requestUserPage) {
                $content .= $this->get(RequestUserAnswerService::class)->renderAnswers($requestUserPage);
            }
        }

        $this->displayOutput($content);
    }

    /**
     * @param Dossier $dossier
     * @param int $dossierLoanId
     * @return DossierLoan|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(Dossier $dossier, int $dossierLoanId): ?DossierLoan
    {
        $dossierLoan = $this->get(DossierLoanService::class)->getRepository()->findOneBy(['id' => $dossierLoanId, 'dossier' => $dossier]);
        if (!$dossierLoan) {
            $this->displayError(__('Erreur : ce prêt n\'existe pas.'), Tools::makeLink('app', 'dossier', 'loans/' . $dossier->getId()));
            exit();
        }

        return $dossierLoan;
    }
}
