<?php
namespace MatGyver\Controllers\App\Dossier;

use MatGyver\Components\Chat\ChatRoomComponent;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Chat\ChatRoomService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class DossierChatAppController
 * @package MatGyver\Controllers\App\Dossier
 */
class DossierChatAppController extends AbstractDossierAppController
{
    #[Route('/app/dossier/conversation/{dossierId}/', name: 'app_dossier_conversation')]
    public function conversations()
    {
        if (!CHAT_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Conversation'));
        $dossier = $this->getDossier();

        $chatRoom = $dossier->getChatRoom();
        if (!$chatRoom) {
            $createChatRoom = $this->get(ChatRoomService::class)->createFromDossier($dossier);
            if (!$createChatRoom['valid']) {
                $this->displayError(__('Erreur lors de la création de la discussion.'), Tools::makeLink('app', 'dossier', $dossier->getId()));
                return;
            }

            $chatRoom = $dossier->getChatRoom();
        }
        if (!$chatRoom) {
            $this->displayError(__('Cette discussion n\'existe pas.'), Tools::makeLink('app', 'dossier', $dossier->getId()));
            return;
        }

        Assets::addCss('common/blank_state.css');
        Assets::addJs('common/chat-room.js');
        Assets::addInlineCss('#kt_chat_content { margin-left: 0 !important; }');

        $room = $this->get(ChatRoomComponent::class)->renderChatRoom($chatRoom, true);

        $output = $this->parser->set('rooms', $room)
            ->set('actions', '')
            ->set('leftPanel', false)
            ->render('common/chat/layout.php');
        $this->displayOutput($output);
    }
}
