<?php

namespace MatGyver\Controllers\App;

use Mat<PERSON><PERSON>ver\Attributes\Universe;
use MatGyver\Services\Dossier\DossiersStatsService;
use MatGyver\Services\Filters\DossiersStatsFiltersService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Annotation\Route;

#[Universe(UNIVERSE_APP_DOSSIER)]
class StatsAppController extends AbstractAppController
{
    #[Route('/app/stats/', name: 'app_stats')]
    public function stats()
    {
        $filtersService = $this->get(DossiersStatsFiltersService::class);
        $filtersService->setOptions();
        $filtersService->getFilters();
        $filters = $filtersService->render();
        $filtersButton = $filtersService->renderButton();
        if (RightsService::hasRole(ROLE_EDITOR)) {
            $filters = '';
            $filtersButton = '';
        }

        $chartDossiersCreated = $this->get(DossiersStatsService::class)->displayChartDossiersCreated();
        if (RightsService::hasAccess(UNIVERSE_APP_SHOP)) {
            $chartPayments = $this->get(DossiersStatsService::class)->displayChartPayments();
        }
        $dossiersSummaryDelayGraph = $this->get(DossiersStatsService::class)->displayDossiersSummaryDelayGraph();
        $dossiersStudyDelayGraph = $this->get(DossiersStatsService::class)->displayDossiersStudyDelayGraph();
        $dossiersByStatusGraph = $this->get(DossiersStatsService::class)->displayDossiersByStatusGraph();
        $output = $this->parser->set('filters', $filters)
            ->set('filtersButton', $filtersButton)
            ->set('chartDossiersCreated', $chartDossiersCreated)
            ->set('chartPayments', $chartPayments ?? '')
            ->set('dossiersSummaryDelayGraph', $dossiersSummaryDelayGraph)
            ->set('dossiersStudyDelayGraph', $dossiersStudyDelayGraph)
            ->set('dossiersByStatusGraph', $dossiersByStatusGraph)
            ->render('app/stats.php');
        $this->displayOutput($output);
    }
}
