<?php

namespace MatGyver\Controllers\App\Affiliation;

use MatG<PERSON>ver\Attributes\View;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Alert\Affiliation\AppAffiliationCommissionsBalanceAlertHelper;
use MatGyver\Helpers\View\Card\Affiliation\AppAffiliationCommissionsCardHelper;
use MatGyver\Helpers\View\Table\Affiliation\AppAffiliationCommissionsViewHelper;
use MatGyver\Helpers\View\Table\Affiliation\AppAffiliationTransactionsViewHelper;
use MatGyver\Services\Affiliation\AffiliationCommissionService;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\Affiliation\AffiliationTransactionsService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class AffiliationCommissionsAppController
 * @package MatGyver\Controllers\App\Affiliation
 */
#[View('MatGyver\Services\Views\ViewAppAffiliation')]
class AffiliationCommissionsAppController extends AbstractAffiliationAppController
{
    #[Route('/app/affiliation/commissions/', name: 'app_affiliation_commissions')]
    public function affiliationCommissions()
    {
        $this->checkIfPartnerIsDefined();
        $this->view->setTitle(__('Commissions'));
        $this->view->addBreadcrumbItem(__('Affiliation'), Tools::makeLink('app', 'affiliation'));

        $amount = $this->get(AffiliationCommissionService::class)->getCommissionsAmountByPartner($_SESSION['client']['partner_id']);
        $balance = $this->get(AffiliationCommissionService::class)->getAvailableCommissionsAmountByPartner($_SESSION['client']['partner_id']);

        $commissions = $this->get(AffiliationCommissionService::class)->getRepository()->getAllCommissions($_SESSION['client']['partner_id']);

        $output = $this->get(AffiliationCommissionService::class)->renderCommissionsExpirationAlert($_SESSION['client']['partner_id']);

        $alert = new AppAffiliationCommissionsBalanceAlertHelper($amount, $balance);
        $output .= $alert->getContent();

        $tableCommissions = $this->get(AppAffiliationCommissionsViewHelper::class)->getContent($commissions);
        $card = new AppAffiliationCommissionsCardHelper($balance, $commissions, $tableCommissions);
        $output .= $card->getContent();

        $this->displayOutput($output);
    }

    #[Route('/app/affiliation/transactions/{idSubPartner}/', name: 'app_affiliation_transactions', requirements: ['idSubPartner' => Requirement::POSITIVE_INT])]
    public function affiliationTransactions(int $idSubPartner)
    {
        $this->checkIfPartnerIsDefined();
        $this->view->setTitle(__('Transactions d\'un client'));
        $this->view->addBreadcrumbItem(__('Affiliation'), Tools::makeLink('app', 'affiliation'));
        $this->view->addBreadcrumbItem(__('Commissions'), Tools::makeLink('app', 'affiliation', 'commissions'));

        $subPartner = $this->get(AffiliationSubPartnersService::class)->getSubPartnerById($idSubPartner);
        if (!$subPartner) {
            $this->displayError(__("Erreur : ce client n'existe pas"), Tools::makeLink('app', 'affiliation'));
            return;
        }

        $transactions = $this->get(AffiliationTransactionsService::class)->getAllTransactionsBySubPartner($idSubPartner);
        if (!$transactions) {
            $this->displayError(__("Aucun commission trouvée pour ce client"));
            return;
        }

        $output = $this->get(AppAffiliationTransactionsViewHelper::class)->getContent($transactions);
        $this->displayOutputCard($output);
    }
}
