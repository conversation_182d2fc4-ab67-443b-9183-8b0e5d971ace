<?php

namespace MatGyver\Controllers\App;

use Mat<PERSON>yver\Attributes\Universe;
use MatGyver\Helpers\Assets;
use MatGyver\Services\Events\EventsService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_APP_NEWS)]
class EventsAppController extends AbstractAppController
{

    #[Route('/app/event/{idEvent}/', name: 'app_event', requirements: ['idEvent' => Requirement::POSITIVE_INT])]
    public function event(int $idEvent)
    {
        $this->view->setTitle(__('Evènement'));

        $event = $this->get(EventsService::class)->getRepository()->find($idEvent);
        if (!$event or !$event->getActive()) {
            $this->displayError(__('Cet évènement n\'existe pas.'));
            return;
        }

        $this->view->setTitle($event->getName());
        if ($event->getDateEnd() and new \DateTime() > $event->getDateEnd()) {
            $this->displayError(__('Cet évènement est terminé.'));
            return;
        }

        if ($event->getBgImage()) {
            $this->view->setBackground($event->getBgImage());
        }

        Assets::addCss('plugins/jquery.countdown.css');
        Assets::addJs('plugins/countdown/jquery.plugin.min.js');
        Assets::addJs('plugins/countdown/jquery.countdown.min.js');
        Assets::addJs('plugins/countdown/start-countdowns.js');
        if (isset($_SESSION['language']) and $_SESSION['language'] == 'fr_FR') {
            Assets::addJs('plugins/countdown/jquery.countdown-fr.js');
        }

        $output = $this->parser->set('event', $event)
            ->render('app/event.php');
        $this->displayOutput($output);
    }
}
