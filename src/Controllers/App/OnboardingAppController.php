<?php

namespace MatGyver\Controllers\App;

use MatGyver\Services\DispatcherService;
use MatGyver\Services\Onboarding\OnboardingGroupService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class OnboardingAppController
 * @package MatGyver\Controllers\App
 */
class OnboardingAppController extends AbstractAppController
{

    #[Route('/app/welcome/', name: 'app_welcome')]
    public function welcome()
    {
        if (!ONBOARDING_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Bienvenue sur %s', APP_NAME));

        if (isset($_POST['onboarding_welcome'])) {
            $this->get(DispatcherService::class)->redirectToRoute('app_index');
        }

        $onBoardingGroup = $this->get(OnboardingGroupService::class)->getRepository()->findOneBy(['controller' => $_SESSION['controller'], 'onLogin' => true]);
        if (!$onBoardingGroup) {
            $this->display404();
            return;
        }

        $this->get(OnboardingGroupService::class)->renderWelcome($onBoardingGroup);
    }

    #[Route('/app/onboarding/', name: 'app_onboarding')]
    #[Route('/app/onboarding/{idGroup}/', name: 'app_onboarding_group', requirements: ['idGroup' => Requirement::POSITIVE_INT])]
    public function onboarding(int $idGroup = 0)
    {
        if (!ONBOARDING_ACTIVATED) {
            $this->display404();
        }

        $this->view->setTitle(__('Onboarding'));
        $output = $this->get(OnboardingGroupService::class)->renderLayout($idGroup);
        $this->displayOutput($output);
    }
}
