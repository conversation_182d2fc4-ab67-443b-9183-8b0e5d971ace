<?php

namespace MatGyver\Controllers\Admin\Affiliation;

use MatGyver\Helpers\View\Table\Affiliation\AffiliationViewHelper;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class AffiliationAdminController
 * @package MatGyver\Controllers\Admin\Affiliation
 */
class AffiliationAdminController extends AbstractAffiliationController
{
    #[Route('/admin/affiliation/', name: 'admin_affiliation')]
    public function affiliation()
    {
        $this->view->setTitle(__('Affiliation'));

        $partners = $this->get(AffiliationPartnersService::class)->getRepository()->getAll();
        if (!$partners) {
            $this->displayEmpty(__('Affiliés'), __('Aucun affilié enregistré'));
            return;
        }

        $output = $this->get(AffiliationViewHelper::class)->getContent($partners);
        $this->displayOutput($output);
    }
}
