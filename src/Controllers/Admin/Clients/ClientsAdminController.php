<?php

namespace MatGyver\Controllers\Admin\Clients;

use MatGyver\Attributes\Universe;
use MatGyver\Attributes\View;
use MatGyver\Controllers\Admin\AbstractAdminController;
use MatGyver\Factories\BlankStates\ClientsBlankState;
use MatGyver\Forms\Clients\ClientReconnectForm;
use MatGyver\FormsFactory\Client\ClientFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Card\Client\ClientAffiliationCardHelper;
use MatGyver\Helpers\View\Card\Client\ClientCancelsCardHelper;
use MatGyver\Helpers\View\Card\Client\ClientSubscriptionsCardHelper;
use MatGyver\Helpers\View\Card\Client\ClientTosCardHelper;
use MatGyver\Helpers\View\Card\Client\ClientTransactionsCardHelper;
use MatGyver\Helpers\View\Table\Clients\ClientsTransactionsViewHelper;
use MatGyver\Helpers\View\Table\Clients\ClientsViewHelper;
use MatGyver\Services\Clients\ClientsCancelService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\Clients\ClientsTransactionsService;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Mail\MailHistoryService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Tos\TosClientsService;
use MatGyver\Services\Users\UserDisplayService;
use MatGyver\Services\Users\UsersLoginService;
use MatGyver\Services\WidgetsService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_ADMIN_SAV)]
class ClientsAdminController extends AbstractAdminController
{
    #[Route('/admin/clients/', name: 'admin_clients')]
    public function clients()
    {
        $this->view->setTitle(__('Clients'));
        if (RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SAV)) {
            $this->view->addAction(__('Ajouter un client'), Tools::makeLink('admin', 'client', 'add'));
        }

        $client = $this->get(ClientsService::class)->getRepository()->findOneBy([]);
        if (!$client) {
            $factory = new ClientsBlankState();
            $this->displayOutput($factory->render());
            return;
        }

        $output = $this->get(ClientsViewHelper::class)->getContent();
        Assets::addJs('admin/tables/clients.js');
        $this->displayOutputCard($output);
    }

    #[Route('/admin/administrators/', name: 'admin_administrators')]
    public function administrators()
    {
        $this->view->setTitle(__('Administrateurs'));

        Assets::addInlineJs('<script>let adminClientRoute = "' . Tools::makeLink('admin', 'client') . '";</script>');
        Assets::addJs('admin/administrators.js');

        $output = $this->parser->render('admin/administrators.php');
        $this->displayOutput($output);
    }

    #[Route('/admin/client/add/', name: 'admin_client_add')]
    public function clientAdd()
    {
        $this->view->setTitle(__('Création d\'un client'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SAV);

        $clientForm = new ClientFormFactory();
        if ($clientForm->isSubmitted() and $clientForm->isValid()) {
            $process = $clientForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $_SESSION['success-message'] = __('Le client a bien été enregistré.');
                $this->get(DispatcherService::class)->redirectToRoute('admin_client', ['idClient' => $process['id_client']]);
            }
        }
        $this->displayOutput($this->builderForm->render($clientForm));
    }

    #[Route('/admin/client/edit/{idClient}/', name: 'admin_client_edit', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientEdit(int $idClient)
    {
        $this->view->setTitle(__('Modification d\'un client'));
        $this->denyAccessUnlessGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV);

        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayError(__('Ce client n\'existe pas'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $client = $this->get(ClientsService::class)->getRepository()->find($idClient);
        if (!$client) {
            $this->displayError(__('Ce client n\'existe pas'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $clientForm = new ClientFormFactory();
        if ($clientForm->isSubmitted() and $clientForm->isValid()) {
            $process = $clientForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $_SESSION['success-message'] = __('Le client a bien été modifié.');
                $this->get(DispatcherService::class)->redirectToRoute('admin_client', ['idClient' => $idClient]);
            }
        }
        $this->displayOutput($this->builderForm->render($clientForm, $client));
    }

    #[Route('/admin/client/mails/{idClient}/', name: 'admin_client_mails', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientMails(int $idClient)
    {
        $this->view->setTitle(__('Emails'));
        $this->view->addBreadcrumbItem(__('Clients'), Tools::makeLink('admin', 'clients'));

        @ini_set('memory_limit', '2048M');

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayError(__('Aucun client spécifié'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $mails = $this->get(MailHistoryService::class)->getRepository()->findBy(['client' => $idClient]);
        if (!$mails) {
            $this->displaySuccess(__('Aucun mail envoyé récemment.'), Tools::makeLink('admin', 'client', $idClient));
            return;
        }

        $output = $this->parser->set('mails', $mails)
            ->set('client_id', $idClient)
            ->set('client', $client)
            ->render('admin/clients/mails.php');
        Assets::addJs('admin/clients_mails.js');

        $this->displayOutputCard($output);
    }

    #[Route('/admin/clients/transactions/', name: 'admin_clients_transactions')]
    public function clientsTransactions()
    {
        $this->view->setTitle(__('Transactions Clients'));
        $this->view->addBreadcrumbItem(__('Clients'), Tools::makeLink('admin', 'clients'));

        set_time_limit(0);

        $clients = $this->get(ClientsService::class)->getAllClients();
        $output = $this->get(ClientsTransactionsViewHelper::class)->getContent($clients);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/client/connect/{idClient}/', name: 'admin_client_connect', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientConnect(int $idClient)
    {
        if (!RightsService::isSuperAdmin()) {
            $this->displayError(__('Vous n\'avez pas les autorisations nécessaires'), Tools::makeLink('admin', 'clients'));
            return;
        }
        if (SUBDOMAIN_ENABLED) {
            $this->displayError(__('Fonctionnalité non disponible'), Tools::makeLink('admin', 'clients'));
            return;
        }

        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayError(__('Erreur : impossible de récupérer ce client'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayError(__('Ce client n\'existe pas'), Tools::makeLink('admin', 'clients'));
            return;
        }
        if (!$client->getActive()) {
            $this->displayError(__('Ce client est désactivé.'), Tools::makeLink('admin', 'client', $idClient));
            return;
        }

        $user = $client->getMainAdmin();
        if (!$user) {
            $this->displayError(__('Ce client n\'a pas d\'administrateur principal'), Tools::makeLink('admin', 'client', $idClient));
            return;
        }
        if ($user->getRestricted() or !$user->getValidated()) {
            $this->displayError(__('Utilisateur désactivé.'));
            return;
        }

        //pour la reconnexion
        $_SESSION['admin_email'] = $_SESSION['email'];

        $connectUser = $this->get(UsersLoginService::class)->insertSignIn(array('email' => $user->getEmail()), true);
        if (!$connectUser['valid']) {
            LoggerService::logError('Erreur lors de la connexion de l\'utilisateur (id : ' . $user->getId() . ' / client : ' . $idClient . ') : ' . $connectUser['message']);
            $this->displayError(__('Une erreur est survenue lors de la connexion') . ' : ' . $connectUser['message'], Tools::makeLink('admin', 'client', $idClient));
            return;
        }

        header('Location: ' . Tools::makeLink('app', 'index'));
        exit();
    }

    #[Route('/admin/client/reconnect/', name: 'admin_client_reconnect')]
    public function clientReconnect()
    {
        $idClient = 0;
        if (isset($_GET['id'])) {
            $idClient = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
        }
        if (!$idClient) {
            $this->displayError(__('Ce client n\'existe pas'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayError(__('Ce client n\'existe pas'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $process = $this->get(ClientReconnectForm::class)->insert([]);
        if (!$process['valid']) {
            $this->displayError($process['message'], Tools::makeLink('admin', 'clients'));
            return;
        }

        $this->display404();
    }

    #[Route("/admin/client/{idClient}/", name: "admin_client", requirements: ['idClient' => Requirement::POSITIVE_INT])]
    #[View('MatGyver\Services\Views\ViewAdminClient')]
    public function client(int $idClient)
    {
        $this->view->setTitle(__('Client'));
        $this->view->addBreadcrumbItem(__('Clients'), Tools::makeLink('admin', 'clients'));

        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayError(__('Erreur : impossible de récupérer ce client'), Tools::makeLink('admin', 'clients'));
            return;
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayError(__("Erreur : ce client n'existe pas"), Tools::makeLink('admin', 'clients'));
            return;
        }

        $this->view->setTitle($client->getName());

        Assets::addInlineJs('<script>let adminClientRoute = "' . Tools::makeLink('admin', 'client') . '";</script>');
        Assets::addJs('admin/clients.js');

        $clientCancels = $this->get(ClientsCancelService::class)->getAllSubscriptionsCancelByIdClient($idClient);
        if ($clientCancels) {
            $card = new ClientCancelsCardHelper($clientCancels);
            $this->parser->set('clientCancelsContent', $card->getContent());
        }

        $clientSubscriptions = $this->get(ClientsSubscriptionsService::class)->getSubscriptionsByClient($idClient);
        $card = new ClientSubscriptionsCardHelper($clientSubscriptions);
        $this->parser->set('clientSubscriptionsContent', $card->getContent());

        $clientTransactions = $this->get(ClientsTransactionsService::class)->getTransactionsByClient($idClient);
        if ($clientTransactions) {
            $card = new ClientTransactionsCardHelper($clientTransactions);
            $this->parser->set('clientTransactionsContent', $card->getContent());
        }

        if (RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_AFFILIATION)) {
            $card = new ClientAffiliationCardHelper($client);
            $this->parser->set('affiliationContent', $card->getContent());
        }

        $tos = $this->get(TosClientsService::class)->getRepository()->findBy(['client' => $client->getId()]);
        $card = new ClientTosCardHelper($tos);
        $this->parser->set('clientTosContent', $card->getContent());

        $widgetUser = $this->get(WidgetsService::class)->displayWidgetUser($client->getMainAdmin(), $client->getMainAdmin()->getUserConfigsAsArray(), false);
        $this->parser->set('widgetUser', $widgetUser);

        $notesContent = $this->get(UserDisplayService::class)->renderAdminClientNotesTab($client);
        $this->parser->set('notesContent', $notesContent);

        $output = $this->parser->set('client', $client)
            ->render('admin/clients/client.php');
        $this->displayOutput($output);
    }

    #[Route('/admin/client/active/{idClient}/', name: 'admin_client_active', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientActive(int $idClient)
    {
        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayErrorAndRedirect(__('Erreur : impossible de récupérer ce client'), Tools::makeLink('admin', 'clients'));
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayErrorAndRedirect(__("Erreur : ce client n'existe pas"), Tools::makeLink('admin', 'clients'));
        }

        $activeClient = $this->get(ClientsService::class)->active_client($idClient);
        if (!$activeClient['valid']) {
            $this->displayErrorAndRedirect($activeClient['message'], Tools::makeLink('admin', 'client', $idClient));
        }

        $this->displaySuccessAndRedirect(__('Le client a été activé avec succès.'), Tools::makeLink('admin', 'client', $idClient));
    }

    #[Route('/admin/client/desactive/{idClient}/', name: 'admin_client_desactive', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientDesactive(int $idClient)
    {
        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayErrorAndRedirect(__('Erreur : impossible de récupérer ce client'), Tools::makeLink('admin', 'clients'));
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayErrorAndRedirect(__("Erreur : ce client n'existe pas"), Tools::makeLink('admin', 'clients'));
        }

        $desactiveClient = $this->get(ClientsService::class)->desactive_client($idClient);
        if (!$desactiveClient['valid']) {
            $this->displayErrorAndRedirect($desactiveClient['message'], Tools::makeLink('admin', 'client', $idClient));
        }

        $this->displaySuccessAndRedirect(__('Le client a été désactivé avec succès.'), Tools::makeLink('admin', 'client', $idClient));
    }

    #[Route('/admin/client/delete/{idClient}/', name: 'admin_client_delete', requirements: ['idClient' => Requirement::POSITIVE_INT])]
    public function clientDelete(int $idClient)
    {
        if (!$idClient or $idClient == CLIENT_MASTER) {
            $this->displayErrorAndRedirect(__('Erreur : impossible de récupérer ce client'), Tools::makeLink('admin', 'clients'));
        }

        $client = $this->get(ClientsService::class)->getClientById($idClient);
        if (!$client) {
            $this->displayErrorAndRedirect(__("Erreur : ce client n'existe pas"), Tools::makeLink('admin', 'clients'));
        }

        $deleteClient = $this->get(ClientsService::class)->delete_client($idClient);
        if (!$deleteClient['valid']) {
            $this->displayErrorAndRedirect($deleteClient['message'], Tools::makeLink('admin', 'client', $idClient));
        }

        $this->displaySuccessAndRedirect(__('Le client a bien été supprimé.'), Tools::makeLink('admin', 'clients'));
    }

    #[Route('/admin/clients/delete/', name: 'admin_clients_delete')]
    public function clientsDelete()
    {
        $redirect = (isset($this->submittedData['redirect']) ? filter_var($this->submittedData['redirect'], FILTER_VALIDATE_URL) : Tools::makeLink('admin', 'clients'));

        $deleteClients = $this->get(ClientsService::class)->delete_some_clients($this->submittedData);
        if (!$deleteClients['valid']) {
            $this->displayErrorAndRedirect($deleteClients['message'], $redirect);
        }

        $this->displaySuccessAndRedirect(__('Les clients ont bien été supprimés.'), $redirect);
    }

    #[Route("/admin/client/subscription/desactive/", name: "admin_client_subscription_desactive", methods: ["POST"])]
    public function clientSubscriptionDesactive()
    {
        $this->checkRequest();

        $idClient = filter_var($this->submittedData['client_id'], FILTER_VALIDATE_INT);
        $idClientSubscription = filter_var($this->submittedData['client_subscription_id'], FILTER_VALIDATE_INT);
        if (!$idClient or !$idClientSubscription or $idClient == CLIENT_MASTER) {
            $this->displayError(__('Paramètres incorrects'));
            return;
        }

        $cancelSubscription = $this->get(ClientsSubscriptionsService::class)->cancelSubscription($idClientSubscription, $idClient);
        if (!$cancelSubscription['valid']) {
            $this->displayError($cancelSubscription['message']);
            return;
        }

        $this->displaySuccessAndRedirect(__('L\'abonnement a été désactivé avec succès.'), Tools::makeLink('admin', 'client', $idClient));
    }
}
