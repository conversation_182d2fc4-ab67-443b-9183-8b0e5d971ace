<?php

namespace MatGyver\Controllers\Admin;

use Mat<PERSON>yver\Attributes\Universe;
use MatGyver\Factories\BlankStates\VersionsBlankState;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\FormFactory;
use Mat<PERSON><PERSON>ver\FormsFactory\Version\VersionFormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Table\Admin\VersionsViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Versions\VersionsService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[Universe(UNIVERSE_ADMIN_DEV)]
class VersionsAdminController extends AbstractAdminController
{

    #[Route('/admin/versions/', name: 'admin_versions')]
    public function versions()
    {
        $this->view->setTitle(__('Versions'));

        $versionForm = new VersionFormFactory();
        $this->builderForm->renderAsModal($versionForm);
        $this->view->addActionOnClick(__('Ajouter une version'), $versionForm->getModalOnClick());

        $versions = $this->get(VersionsService::class)->getRepository()->findAll();
        if (!$versions) {
            $factory = new VersionsBlankState();
            $this->displayOutput($factory->render());
            return;
        }

        $output = $this->get(VersionsViewHelper::class)->getContent($versions);
        $this->displayOutputCard($output);
    }

    #[Route('/admin/version/add/', name: 'admin_version_add')]
    public function versionAdd()
    {
        $this->view->setTitle(__('Création d\'une version'));
        $this->view->addBreadcrumbItem(__('Versions'), Tools::makeLink('admin', 'versions'));

        $versionForm = new VersionFormFactory();
        if ($versionForm->isSubmitted() and $versionForm->isValid()) {
            $process = $versionForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_version_view', ['id' => $process['id']]);
            }
        }
        $this->displayOutput($this->builderForm->render($versionForm));
    }

    #[Route('/admin/version/edit/{id}/', name: 'admin_version_edit', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function versionEdit(int $id)
    {
        $this->view->setTitle(__('Edition d\'une version'));
        $this->view->addBreadcrumbItem(__('Versions'), Tools::makeLink('admin', 'versions'));

        $version = $this->get(VersionsService::class)->getRepository()->find($id);
        if (!$version) {
            $this->displayError(__('Cette version n\'existe pas'), Tools::makeLink('admin', 'versions'));
            return;
        }

        $versionForm = new VersionFormFactory();
        if ($versionForm->isSubmitted() and $versionForm->isValid()) {
            $process = $versionForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('admin_version_view', ['id' => $id]);
            }
        }
        $this->displayOutput($this->builderForm->render($versionForm, $version));
    }

    #[Route('/admin/version/view/{id}/', name: 'admin_version_view', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function versionView(int $id)
    {
        $this->view->setTitle(__('Affichage d\'une version'));
        $this->view->addBreadcrumbItem(__('Versions'), Tools::makeLink('admin', 'versions'));

        $version = $this->get(VersionsService::class)->getRepository()->find($id);
        if (!$version) {
            $this->displayError(__('Cette version n\'existe pas'), Tools::makeLink('admin', 'versions'));
            return;
        }

        $output = $this->parser->set('version', $version)
            ->render('admin/versions/version.php');
        $this->displayOutput($output);
    }

    #[Route('/admin/version/delete/{id}/', name: 'admin_version_delete', requirements: ['id' => Requirement::POSITIVE_INT])]
    public function versionDelete(int $id)
    {
        $version = $this->get(VersionsService::class)->getRepository()->find($id);
        if (!$version) {
            $this->displayErrorAndRedirect(__("Erreur : cette version n'existe pas"), Tools::makeLink('admin', 'versions'));
        }

        $deleteVersion = $this->get(VersionsService::class)->deleteVersion($id);
        if (!$deleteVersion['valid']) {
            $this->displayErrorAndRedirect($deleteVersion['message'], Tools::makeLink('admin', 'versions'));
        }

        $this->displaySuccessAndRedirect(__('Cette version a bien été supprimée.'), Tools::makeLink('admin', 'versions'));
    }
}
