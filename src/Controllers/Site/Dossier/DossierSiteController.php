<?php
namespace MatGyver\Controllers\Site\Dossier;

use MatGyver\Enums\ConfigEnum;
use MatGyver\Factories\BlankStates\ErrorBlankState;
use MatGyver\FormsFactory\Dossier\Site\SiteDossierAcceptFormFactory;
use MatGyver\FormsFactory\Dossier\Site\SiteDossierEditFormFactory;
use MatGyver\FormsFactory\Dossier\Site\SiteDossierSignatureFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DispatcherService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class DossierSiteController
 * @package MatGyver\Controllers\Site\Dossier
 */
class DossierSiteController extends AbstractDossierSiteController
{
    #[Route('/dossier/{reference}/', name: 'site_dossier')]
    public function dossier(string $reference)
    {
        $this->view->setTitle(__('Dossier'));
        $this->view->set('no_title', true);

        $dossier = $this->getDossier();
        if (!$dossier) {
            $this->displayError(__('Erreur : ce dossier n\'existe pas.'));
            exit();
        }

        if (isset($_GET['step']) and $_GET['step'] == 'signature') {
            header('Location: ' . Tools::makeLink('site', 'dossier', 'signature/' . $dossier->getReference()));
            exit();
        }

        if (!$dossier->getConditionsAccepted()) {
            $dossierAcceptForm = new SiteDossierAcceptFormFactory($dossier);
            if ($dossierAcceptForm->isSubmitted() and $dossierAcceptForm->isValid()) {
                $process = $dossierAcceptForm->process(FormFactory::TYPE_PROCESS_INSERT);
                if ($process['valid']) {
                    $this->get(DispatcherService::class)->redirectToRoute('site_dossier_contact', ['reference' => $dossier->getReference()]);
                }
            }
            $this->parser->set('dossierAcceptForm', $this->builderForm->render($dossierAcceptForm));
        }

        $output = $this->parser->set('dossier', $dossier)
            ->render('site/dossier/home.php');
        $this->displayOutput($output);
    }

    #[Route('/dossier/edit/{reference}/', name: 'site_dossier_edit')]
    public function dossierEdit()
    {
        $this->view->setTitle(__('Observations'));

        $dossier = $this->getDossier(true);

        $dossierForm = new SiteDossierEditFormFactory();
        if ($dossierForm->isSubmitted() and $dossierForm->isValid()) {
            $process = $dossierForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('site_dossier_signature', ['reference' => $dossier->getReference()]);
            }
        }
        $output = $this->builderForm->render($dossierForm, $dossier);
        $this->displayOutput($output);
    }

    #[Route('/dossier/end/{reference}/', name: 'site_dossier_end')]
    public function dossierEnd()
    {
        $this->view->setTitle(__('Confirmation du dossier'));
        $this->view->set('no_title', true);

        $dossier = $this->getDossier();
        if (!$dossier->getContact()->getConfirmed()) {
            $factory = new ErrorBlankState(
                __('Ce dossier n\'est pas encore confirmé. Merci de signer ce dossier en cliquant sur le bouton ci-dessous.'),
                Tools::makeLink('site', 'dossier', 'signature/' . $dossier->getReference()),
                __('Signer le dossier')
            );
            $factory->setTitle(__('Dossier non confirmé'));
            $factory->setImage('');
            $factory->setIcon('<i class="fas fa-exclamation-triangle text-danger"></i>');
            $this->displayOutput($factory->render());
            return;
        }

        Assets::addCss('common/blank_state.css');
        $content = $this->parser->set('dossier', $dossier)
            ->render('site/dossier/end.php');
        $this->displayOutput($content);
    }

    #[Route('/dossier/cgu/{reference}/', name: 'site_dossier_cgu_params')]
    public function cguByDossier(string $reference): void
    {
        $dossier = $this->getDossier();
        if (!$dossier) {
            $this->displayError(__('Erreur : ce dossier n\'existe pas.'));
            exit();
        }

        $generalConditions = $dossier->getClient()->getClientConfig(ConfigEnum::GENERAL_CONDITIONS);
        if (!$generalConditions) {
            $this->display404();
            exit();
        }

        $output = $this->parser->set('generalConditions', $generalConditions)
            ->render('site/cgu.php');
        $this->displayOutput($output);
    }
}
