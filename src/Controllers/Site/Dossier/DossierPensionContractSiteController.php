<?php
namespace MatGyver\Controllers\Site\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierPensionContract;
use MatGyver\Factories\BlankStates\EmptyBlankState;
use Mat<PERSON>yver\FormsFactory\Dossier\DossierPensionContractFormFactory;
use MatGyver\FormsFactory\FormFactory;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
use MatGyver\Helpers\View\Card\Card;
use MatGyver\Helpers\View\Table\Dossier\PensionContractViewHelper;
use MatGyver\Services\DispatcherService;
use MatGyver\Services\Dossier\DossierPensionContractService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

class DossierPensionContractSiteController extends AbstractDossierSiteController
{
    #[Route('/dossier/pension/contracts/{dossierReference}/', name: 'site_dossier_pension_contracts')]
    public function pensionContracts()
    {
        $this->view->setTitle(__('Contrats de prévoyances'));

        $dossier = $this->getDossier(true);

        $pensionContractForm = new DossierPensionContractFormFactory($dossier);
        $this->builderForm->renderAsPanel(__('Contrat de prévoyance'), 'pension_contract', $pensionContractForm);

        $pensionContracts = $this->get(DossierPensionContractService::class)->getRepository()->findBy(['dossier' => $dossier]);
        if (!$pensionContracts) {
            $factory = new EmptyBlankState(
                __('Contrats de prévoyances'),
                __('Aucun contrat de prévoyance enregistré.')
            );
            $factory->setButtonId('pension_contract_toggle');
            $factory->setButtonText(__('Ajouter un contrat de prévoyance'));
            $output = $factory->render();
            $output .= $this->displayBtnNextStep(Tools::makeLink('site', 'dossier', 'loans/' . $dossier->getReference()));
            $this->displayOutput($output);
            return;
        }

        $this->view->addAction(__('Ajouter un contrat de prévoyance'), '', '', '', 'pension_contract_toggle');

        $content = $this->get(PensionContractViewHelper::class)->getContent($pensionContracts);
        $content .= $this->displayBtnNextStep(Tools::makeLink('site', 'dossier', 'loans/' . $dossier->getReference()));
        $this->displayOutput($content);
    }

    #[Route('/dossier/pension/contract/add/{dossierReference}/', name: 'site_dossier_pension_contract_add')]
    public function pensionContractAdd()
    {
        $this->view->setTitle(__('Création d\'un contrat de prévoyance'));

        $dossier = $this->getDossier(true);
        $this->view->addBreadcrumbItem(__('Contrats de prévoyances'), Tools::makeLink('site', 'dossier', 'pension/contracts/' . $dossier->getReference()));

        $pensionContractForm = new DossierPensionContractFormFactory($dossier);
        if ($pensionContractForm->isSubmitted() and $pensionContractForm->isValid()) {
            $process = $pensionContractForm->process(FormFactory::TYPE_PROCESS_INSERT);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('site_dossier_pension_contracts', ['dossierReference' => $dossier->getReference()]);
            }
        }
        $this->displayOutput($this->builderForm->render($pensionContractForm));
    }

    #[Route('/dossier/pension/contract/edit/{pensionContractId}/{dossierReference}/', name: 'site_dossier_pension_contract_edit', requirements: ['pensionContractId' => Requirement::POSITIVE_INT])]
    public function pensionContractEdit(int $pensionContractId, string $dossierReference)
    {
        $this->view->setTitle(__('Modification d\'un contrat de prévoyance'));

        $dossier = $this->getDossier(true);
        $this->view->addBreadcrumbItem(__('Contrats de prévoyances'), Tools::makeLink('site', 'dossier', 'pension/contracts/' . $dossier->getReference()));

        $pensionContract = $this->getFromRoute($dossier, $pensionContractId);

        $pensionContractForm = new DossierPensionContractFormFactory($dossier);
        if ($pensionContractForm->isSubmitted() and $pensionContractForm->isValid()) {
            $process = $pensionContractForm->process(FormFactory::TYPE_PROCESS_UPDATE);
            if ($process['valid']) {
                $this->get(DispatcherService::class)->redirectToRoute('site_dossier_pension_contracts', ['dossierReference' => $dossier->getReference()]);
            }
        }
        $output = $this->builderForm->render($pensionContractForm, $pensionContract);
        $this->displayOutput($output);
    }

    #[Route('/dossier/pension/contract/delete/{pensionContractId}/{dossierReference}/', name: 'site_dossier_pension_contract_delete', requirements: ['pensionContractId' => Requirement::POSITIVE_INT])]
    public function pensionContractDelete(int $pensionContractId, string $dossierReference)
    {
        $dossier = $this->getDossier(true);
        $pensionContract = $this->getFromRoute($dossier, $pensionContractId);
        try {
            $this->get(DossierPensionContractService::class)->deleteAndFlush($pensionContract);
        } catch (\Exception $e) {
            $this->displayError(__('Impossible de supprimer ce contrat de prévoyance.'));
            return;
        }
        $this->get(DispatcherService::class)->redirectToRoute('site_dossier_pension_contracts', ['dossierReference' => $dossier->getReference()]);
    }

    /**
     * @param Dossier $dossier
     * @param int $pensionContractId
     * @return DossierPensionContract|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function getFromRoute(Dossier $dossier, int $pensionContractId): ?DossierPensionContract
    {
        $pensionContract = $this->get(DossierPensionContractService::class)->getRepository()->findOneBy(['id' => $pensionContractId, 'dossier' => $dossier]);
        if (!$pensionContract) {
            $this->displayError(__('Erreur : ce contrat de prévoyance n\'existe pas.'), Tools::makeLink('site', 'dossier', 'pension/contracts/' . $dossier->getReference()));
            exit();
        }

        return $pensionContract;
    }
}
