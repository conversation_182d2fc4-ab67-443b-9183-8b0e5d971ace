<?php

namespace MatGyver\Controllers\Site;

use MatGyver\Entity\Help\Universe\HelpUniverse;
use MatGyver\Factories\BlankStates\NoResultsBlankState;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Help\HelpArticlesService;
use MatGyver\Services\Help\HelpService;
use MatGyver\Services\Help\HelpUniverseService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class SiteHelpController
 * @package MatGyver\Controllers\Site
 */
class SiteHelpController extends AbstractSiteController
{

    #[Route('/help/', name: 'site_help')]
    public function help()
    {
        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            $this->display404();
        }

        $this->view->setTitle(__('Aide'));
        $this->view->addAction(__('Contacter le support'), Tools::makeLink('site', 'support', 'ticket/create'));
        Assets::addCss('site/help.css');

        $universes = $this->get(HelpUniverseService::class)->getRepository()->findAll();
        if (!$universes) {
            $this->displayError(__('Aucun article disponible.'));
            return;
        }

        $idPage = (isset($_GET['p']) ? filter_input(INPUT_GET, 'p', FILTER_VALIDATE_INT) : 1);
        $search = (isset($_GET['s']) ? filter_input(INPUT_GET, 's', FILTER_UNSAFE_RAW) : '');
        $searchContent = '';
        if ($search) {
            $searchContent = $this->renderSearch($search, null, $idPage);
        }

        $toolbar = $this->get(HelpService::class)->getInboxToolbar(null, 'site', 0, $idPage, $search);
        $content = $this->parser->set('universes', $universes)
            ->set('searchContent', $searchContent)
            ->set('toolbar', $toolbar)
            ->render('site/help/universes.php');
        $this->displayOutput($content);
    }

    #[Route('/help/{permalink}/', name: 'site_help_universe', requirements: ['permalink' => Requirement::ASCII_SLUG])]
    public function helpUniverse(string $permalink)
    {
        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            $this->display404();
        }

        $this->view->setTitle(__('Aide'));
        $this->view->addAction(__('Contacter le support'), Tools::makeLink('site', 'support', 'ticket/create'));
        Assets::addCss('site/help.css');

        $universe = $this->get(HelpUniverseService::class)->getRepository()->findOneBy(['permalink' => $permalink]);
        if (!$universe) {
            $this->displayError(__('Cette page n\'existe pas.'));
            return;
        }

        $this->view->setTitle('<a href="' . Tools::makeLink('site', 'help') . '" class="btn btn-icon btn-clean mr-2"><i class="fas fa-arrow-left"></i></a>' . $universe->getName());

        $idPage = (isset($_GET['p']) ? filter_input(INPUT_GET, 'p', FILTER_VALIDATE_INT) : 1);
        $search = (isset($_GET['s']) ? filter_input(INPUT_GET, 's', FILTER_UNSAFE_RAW) : '');
        if ($search) {
            $searchContent = $this->renderSearch($search, $universe, $idPage);
            $this->displayOutput($searchContent);
            return;
        }

        $this->displayOutput($this->get(HelpService::class)->renderUniverse($universe, 'site', $idPage));
    }

    #[Route('/help/{universePermalink}/{articlePermalink}/', name: 'site_help_universe_article', requirements: ['universePermalink' => Requirement::ASCII_SLUG, 'articlePermalink' => Requirement::ASCII_SLUG])]
    public function helpArticle(string $universePermalink, string $articlePermalink)
    {
        if ($_SESSION['client']['id'] != CLIENT_MASTER) {
            $this->display404();
        }

        $this->view->setTitle(__('Aide'));
        $this->view->addAction(__('Contacter le support'), Tools::makeLink('site', 'support', 'ticket/create'));

        $universe = $this->get(HelpUniverseService::class)->getRepository()->findOneBy(['permalink' => $universePermalink]);
        if (!$universe) {
            $this->displayError(__('Cette page n\'existe pas.'));
            return;
        }

        $article = $this->get(HelpArticlesService::class)->getRepository()->findOneBy(['permalink' => $articlePermalink]);
        if (!$article or !$article->getActive()) {
            $this->displayError(__('Cet article n\'existe pas.'));
            return;
        }

        $link = Tools::makeLink('site', 'help', $universe->getPermalink());
        if (isset($_GET['s'])) {
            $link = Tools::makeLink('site', 'help', $universe->getPermalink(), 's=' . filter_input(INPUT_GET, 's', FILTER_UNSAFE_RAW));
        }
        $this->view->setTitle('<a href="' . $link . '" class="btn btn-icon btn-clean mr-2"><i class="fas fa-arrow-left"></i></a>' . $article->getTitle());

        Assets::addCss('site/help.css');
        Assets::addJs('site/help.js');

        $this->displayOutput($this->get(HelpService::class)->renderArticle($article));
    }

    /**
     * @param string $search
     * @param HelpUniverse|null $universe
     * @param int $idPage
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function renderSearch(string $search, ?HelpUniverse $universe = null, int $idPage = 1): string
    {
        $articles = $this->get(HelpArticlesService::class)->getRepository()->searchArticles($search);
        if (!$articles) {
            $title = __('Aucun résultat');
            $message = __('Nous n\'avons trouvé aucun article correspondant. Veuillez poursuivre votre recherche avec une nouvelle requête.');
            $buttonLink = Tools::makeLink('site', 'support', 'ticket/create');
            $buttonText = __('Contacter le support');
            $factory = new NoResultsBlankState($title, $message, $buttonLink, $buttonText);
            $content = '<div class="col-md-6 offset-3">' . $factory->render() . '</div>';

            $toolbar = $this->get(HelpService::class)->getInboxToolbar($universe, 'site', 0, $idPage, $search);
            $output = $this->get(HelpService::class)->renderLayout($content, $toolbar, 'site', $universe);
            return $output;
        }

        $output = $this->get(HelpService::class)->renderArticles($articles, 'site', $search);
        return $output;
    }
}
