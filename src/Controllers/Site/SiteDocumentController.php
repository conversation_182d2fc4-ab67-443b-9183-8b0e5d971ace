<?php

namespace MatGyver\Controllers\Site;

use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Dossier\DossierContactService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\RightsService;
use MatGyver\Services\SiteService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class SiteDocumentController
 * @package MatGyver\Controllers\Site
 */
class SiteDocumentController extends AbstractSiteController
{
    #[Route('/medias/{file}', name: 'site_medias_file', requirements: ['file' => '.+'])]
    public function medias(): void
    {
        $file = WEB_PATH . '/medias/' . $this->param;
        if (!file_exists($file)) {
            $this->display404();
            return;
        }
        if (is_dir($file)) {
            $this->display404();
            return;
        }

        //this route should be protected only for documents in a folder called /documents/
        if (str_contains($file, 'master/aide/')) {
            $this->sendFile($file);
            return;
        }
        if (str_contains($file, 'logos/')) {
            $this->sendFile($file);
            return;
        }

        $arrayFile = explode('/', $this->param);
        $clientUniqId = array_shift($arrayFile);
        $client = $this->get(ClientsService::class)->getClientByUniqId($clientUniqId);
        if (!$client) {
            LoggerService::logError('unauthorized access to ' . $this->param . ' (client not found)');
            SiteService::display403();
            exit();
        }

        if (!RightsService::isSuperAdmin()) {
            $currentClient = $this->get(ClientsService::class)->getClient();
            if ($currentClient !== $client and isset($_GET['dossier_ref'])) {
                $dossierReference = filter_input(INPUT_GET, 'dossier_ref', FILTER_UNSAFE_RAW);
                $dossier = $this->get(DossierService::class)->getRepository()->findOneBy(['reference' => $dossierReference], null, false);
                if ($dossier) {
                    $currentClient = $dossier->getClient();
                }
            }
            if ($currentClient !== $client and isset($_GET['contact'])) {
                $contactId = filter_input(INPUT_GET, 'contact', FILTER_VALIDATE_INT);
                $contact = $this->get(DossierContactService::class)->getRepository()->findOneBy(['id' => $contactId, 'client' => $client]);
                if ($contact) {
                    $currentClient = $contact->getClient();
                }
            }
            if ($currentClient !== $client and isset($_GET['client'])) {
                $clientId = filter_input(INPUT_GET, 'client', FILTER_VALIDATE_INT);
                $currentClient = $this->get(ClientsService::class)->getClientById($clientId);
            }
            if ($currentClient !== $client) {
                $error = 'unauthorized access to ' . $this->param . ' (different client - current = ' . $currentClient->getId() . ' / client = ' . $client->getId() . ')';
                if (isset($_SERVER['HTTP_REFERER'])) {
                    $error .= ' - referer = ' . $_SERVER['HTTP_REFERER'];
                }
                LoggerService::logError($error);
                SiteService::display403();
                exit();
            }
        }

        $this->sendFile($file);
    }

    private function sendFile(string $file)
    {
        try {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            if (!$finfo) {
                $this->display404();
            }
            $mimeType = finfo_file($finfo, $file);
            finfo_close($finfo);

            header("Content-Type: $mimeType");
            header("Content-Length: " . filesize($file));

            if (isset($_GET['download'])) {
                header("Content-Transfer-Encoding: Binary");
                header("Content-disposition: attachment; filename=\"" . basename($file) . "\"");
            } else {
                header("Content-disposition: inline; filename=\"" . basename($file) . "\"");
            }

            readfile($file);
            exit();
        } catch (\Exception $e) {
            $this->display404();
        }
    }
}
