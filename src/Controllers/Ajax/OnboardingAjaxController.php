<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Services\Onboarding\OnboardingGroupService;
use MatGyver\Services\Onboarding\OnboardingTaskClientService;
use MatGyver\Services\Onboarding\OnboardingTaskService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class OnboardingAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class OnboardingAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/onboarding/task/completed/', name: 'ajax_onboarding_task_completed')]
    public function onboardingTaskCompleted()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $taskId = filter_input(INPUT_POST, 'taskId', FILTER_VALIDATE_INT);

        $task = $this->get(OnboardingTaskService::class)->getRepository()->find($taskId);
        if (!$task) {
            $this->sendError(__('Cet élément n\'existe pas'));
        }

        $setTaskCompleted = $this->get(OnboardingTaskClientService::class)->setTaskCompleted($task);
        if (!$setTaskCompleted['valid']) {
            $this->sendError($setTaskCompleted['message']);
        }

        $groupProgression = $this->get(OnboardingGroupService::class)->getProgression($task->getGroup());
        $overallCompletion = $this->get(OnboardingGroupService::class)->getOverallProgression();

        $this->sendData([
            'group_progression' => $groupProgression,
            'progression' => $overallCompletion,
        ]);
    }

    #[Route('/ajax/onboarding/welcome/', name: 'ajax_onboarding_welcome')]
    public function onboardingWelcome()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $controller = filter_input(INPUT_POST, 'controller', FILTER_UNSAFE_RAW);

        $onBoardingGroup = $this->get(OnboardingGroupService::class)->getRepository()->findOneBy(['controller' => $controller, 'onLogin' => true]);
        if (!$onBoardingGroup) {
            $this->sendError(__('Accès non autorisé'), ['step' => 1]);
        }

        $postProcess = $this->get(OnboardingGroupService::class)->postProcess($onBoardingGroup);
        if (!$postProcess['valid']) {
            $this->sendError($postProcess['message'], ['step' => ($postProcess['step'] ?? 1)]);
        }

        foreach ($onBoardingGroup->getTasks() as $task) {
            $complete = $this->get(OnboardingTaskClientService::class)->setTaskCompleted($task);
            if (!$complete['valid']) {
                $this->sendError($complete['message']);
            }
        }

        $this->sendSuccess();
    }
}
