<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Helpers\Page\PageVideo;
use MatGyver\Services\Pages\PagesEditorHistoryService;
use MatGyver\Services\Pages\PagesEditorService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class PageEditorAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class PageEditorAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/page_editor/save/', name: 'ajax_page_editor_save')]
    public function saveDesign()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $objectId = filter_input(INPUT_POST, 'objectId', FILTER_VALIDATE_INT);
        $pageType = filter_input(INPUT_POST, 'pageType', FILTER_UNSAFE_RAW);
        if (!$pageType) {
            $this->sendError(__('Aucun type ou objet défini'));
        }

        $saveDesign = $this->get(PagesEditorService::class)->saveDesign($pageType, $objectId);
        $saveDesign['status'] = $saveDesign['valid'];
        $this->sendOutput($saveDesign);
    }

    #[Route('/ajax/page_editor/restore/', name: 'ajax_page_editor_restore')]
    public function restoreHistory()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $idHistory = filter_input(INPUT_POST, 'idHistory', FILTER_VALIDATE_INT);

        $restoreHistory = $this->get(PagesEditorHistoryService::class)->restore($idHistory);
        $restoreHistory['status'] = $restoreHistory['valid'];
        $this->sendOutput($restoreHistory);
    }

    #[Route('/ajax/page_editor/delete/', name: 'ajax_page_editor_delete')]
    public function deleteDesign()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $objectId = filter_input(INPUT_POST, 'objectId', FILTER_VALIDATE_INT);
        $pageType = filter_input(INPUT_POST, 'pageType', FILTER_UNSAFE_RAW);
        if (!$pageType) {
            $this->sendOutput( ['status' => false, 'message' => __('Aucun type ou objet défini')]);
        }

        $deleteDesign = $this->get(PagesEditorService::class)->deleteDesign($pageType, $objectId);
        $deleteDesign['status'] = $deleteDesign['valid'];
        $this->sendOutput($deleteDesign);
    }

    #[Route('/ajax/page_editor/video/', name: 'ajax_page_editor_video')]
    public function getVideo()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $video = filter_input(INPUT_POST, 'video', FILTER_UNSAFE_RAW);
        if (!$video) {
            $this->sendData(['html' => '']);
        }

        $getVideo = PageVideo::getVideoSource($video);
        $this->sendData(['html' => $getVideo]);
    }
}
