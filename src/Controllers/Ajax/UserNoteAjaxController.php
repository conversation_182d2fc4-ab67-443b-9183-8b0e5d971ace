<?php

namespace MatGyver\Controllers\Ajax;

use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersNotesService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class UserNoteAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class UserNoteAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/user/note/', name: 'ajax_user_note')]
    public function noteInsert()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $userId = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $label = filter_input(INPUT_POST, 'label', FILTER_UNSAFE_RAW);

        $result = $this->get(UsersNotesService::class)->insert($userId, $content, $label);
        if (!$result['valid']) {
            $this->sendError($result['message']);
        }

        $displayNote = $this->get(UsersNotesService::class)->displayNote($result['note']);
        $this->sendSuccess(__('Note enregistrée'), ['note' => $displayNote]);
    }

    #[Route('/ajax/user/note/get/', name: 'ajax_user_note_get_form')]
    public function noteGet()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);
        $note = $this->get(UsersNotesService::class)->getRepository()->find($idNote);
        if (!$note) {
            $this->sendError(__('Cette note n\'existe pas'));
        }

        $form = $this->get(UsersNotesService::class)->displayEditNoteForm($note);

        $this->sendData(['form' => $form]);
    }

    #[Route('/ajax/user/note/update/', name: 'ajax_user_note_update')]
    public function noteUpdate()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $label = filter_input(INPUT_POST, 'label', FILTER_UNSAFE_RAW);

        $result = $this->get(UsersNotesService::class)->update($idNote, $content, $label);
        if (!$result['valid']) {
            $this->sendError($result['message']);
        }

        $displayNote = $this->get(UsersNotesService::class)->displayNote($result['note']);
        $this->sendSuccess(__('Note enregistrée'), ['note' => $displayNote]);
    }

    #[Route('/ajax/user/note/delete/', name: 'ajax_user_note_delete')]
    public function noteDelete()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_VALIDATE_INT);

        $result = $this->get(UsersNotesService::class)->delete($idNote);
        $this->sendOutput([
            'status' => $result['valid'],
            'message' => $result['message']
        ]);
    }

    #[Route('/ajax/user/notes/positions/', name: 'ajax_user_notes_positions')]
    public function notesPositions()
    {
        if (!RightsService::isEditor()) {
            $this->sendUnauthorizedAccess();
        }

        $notes = filter_input(INPUT_POST, 'notes', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        if (!$notes) {
            $this->sendSuccess();
        }

        $result = $this->get(UsersNotesService::class)->updatePositions($notes);
        if (!$result['valid']) {
            $this->sendError($result['message']);
        }

        $this->sendSuccess($result['message']);
    }
}
