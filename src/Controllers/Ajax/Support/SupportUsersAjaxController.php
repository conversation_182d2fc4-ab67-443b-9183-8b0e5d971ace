<?php

namespace MatGyver\Controllers\Ajax\Support;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Factories\BlankStates\SupportTicketsBlankState;
use MatGyver\Services\Support\SupportService;
use MatGyver\Services\Support\SupportTicketService;
use MatGyver\Services\Users\UsersService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class SupportUsersAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class SupportUsersAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/support/user/tickets/', name: 'ajax_support_user_tickets')]
    public function displayUserTickets()
    {
        $userId = filter_input(INPUT_POST, 'userId', FILTER_VALIDATE_INT);

        $user = $this->get(UsersService::class)->adminGetUserById($userId);
        if (!$user) {
            $this->sendError(__('Cet utilisateur n\'existe pas'));
        }

        $tickets = $this->get(SupportTicketService::class)->getRepository()->getAllTicketsByUser(null, $userId, $user->getClient()->getId());
        if (!$tickets) {
            $factory = new SupportTicketsBlankState();
            $this->sendData([
                'content' => $factory->render(),
                'toolbar' => $this->get(SupportService::class)->getInboxToolbar(0, 1),
            ]);
        }

        $content = '';
        if ($tickets) {
            foreach ($tickets as $ticket) {
                $content .= $this->get(SupportTicketService::class)->getDesignTicketInBox($ticket);
            }
        }

        $toolbar = $this->get(SupportService::class)->getInboxToolbar(count($tickets), 1);

        $this->sendData([
            'content' => $content,
            'toolbar' => $toolbar,
        ]);
    }
}
