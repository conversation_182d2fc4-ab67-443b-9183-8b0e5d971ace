<?php

namespace MatGyver\Controllers\Ajax\Support;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\RightsService;
use MatGyver\Services\Support\SupportSavedReplyService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class SupportSavedReplyAjaxController
 * @package MatGyver\Controllers\Ajax
 */
class SupportSavedReplyAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/support/saved_reply/', name: 'ajax_support_saved_reply')]
    public function savedReply()
    {
        if (!RightsService::hasAccess(UNIVERSE_ADMIN_SAV)) {
            $this->sendUnauthorizedAccess();
        }

        $idSavedReply = filter_input(INPUT_POST, 'id_saved_reply', FILTER_VALIDATE_INT);
        $savedReply = $this->get(SupportSavedReplyService::class)->getSavedReplyById($idSavedReply);
        if (!$savedReply) {
            $this->sendSuccess();
        }
        $this->sendData(['content' => $savedReply->getReply()]);
    }
}
