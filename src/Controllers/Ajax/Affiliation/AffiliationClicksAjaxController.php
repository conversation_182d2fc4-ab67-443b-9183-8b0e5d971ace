<?php

namespace MatGyver\Controllers\Ajax\Affiliation;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Helpers\View\Table\Affiliation\AppAffiliationClicksViewHelper;
use MatGyver\Services\Affiliation\AffiliationClicsService;
use MatGyver\Services\RightsService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class AffiliationClicksAjaxController
 * @package MatGyver\Controllers\Ajax\Affiliation
 */
class AffiliationClicksAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/affiliation/clicks/datatable/', name: 'ajax_affiliation_clicks_datatable')]
    public function affiliationClicksDatatable()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_AFFILIATION)) {
            $this->sendData(['data' => []]);
        }

        $draw = filter_var($_REQUEST['draw'], FILTER_VALIDATE_INT);

        if (RightsService::isSuperAdmin() and isset($_POST['idPartner'])) {
            $idPartner = filter_input(INPUT_POST, 'idPartner', FILTER_VALIDATE_INT);
        } else {
            if (!isset($_SESSION['client']['partner_id'])) {
                $data = [
                    'draw' => intval($draw),
                    'iTotalRecords' => 0,
                    'iTotalDisplayRecords' => 0,
                    'aaData' => [],
                    'resetPage' => true,
                ];
                $this->sendData($data);
            }
            $idPartner = $_SESSION['client']['partner_id'];
        }
        if (!$idPartner) {
            $data = [
                'draw' => intval($draw),
                'iTotalRecords' => 0,
                'iTotalDisplayRecords' => 0,
                'aaData' => [],
            ];
            $this->sendData($data);
        }

        //datatable columns (for ORDER BY)
        $columns = [
            0 => 'id',
            1 => 'date',
            2 => 'ip',
            3 => ''
        ];

        $totalRecords = $this->get(AffiliationClicsService::class)->getRepository()->getCountClicks(null, $idPartner);

        //ordering
        $orderVar = 'id';
        $orderDir = 'DESC';
        if (isset($_REQUEST['order']) and $_REQUEST['order']) {
            foreach ($_REQUEST['order'] as $order) {
                if (!$columns[intval($order['column'])]) {
                    continue;
                }
                $orderVar = $columns[intval($order['column'])];
                $orderDir = ('asc' === $order['dir'] ? 'ASC' : 'DESC');
                break;
            }
        }

        //paging
        $start = 0;
        $length = 10;
        if (isset($_REQUEST['start'])) {
            $start = filter_var($_REQUEST['start'], FILTER_VALIDATE_INT);
        }
        if (isset($_REQUEST['length'])) {
            $length = filter_var($_REQUEST['length'], FILTER_VALIDATE_INT);
        }

        if ($start and $totalRecords < $start) {
            $data = [
                'draw' => intval($draw),
                'iTotalRecords' => intval($totalRecords),
                'iTotalDisplayRecords' => intval($totalRecords),
                'aaData' => [],
                'resetPage' => true,
            ];
            $this->sendData($data);
        }

        $clicks = $this->get(AffiliationClicsService::class)->getClicksWithLimit($idPartner, $start, $length, $orderVar, $orderDir);

        $data = [];
        if ($clicks) {
            $this->get(AppAffiliationClicksViewHelper::class)->setClicksRows($clicks);
            $data = $this->get(AppAffiliationClicksViewHelper::class)->getTable()->getRowsAsArray(false);
        }

        $result = [
            'draw' => intval($draw),
            'iTotalRecords' => intval($totalRecords),
            'iTotalDisplayRecords' => intval($totalRecords),
            'aaData' => $data,
            'resetPage' => false,
        ];
        $this->sendData($result);
    }
}
