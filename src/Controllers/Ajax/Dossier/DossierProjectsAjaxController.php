<?php

namespace MatGyver\Controllers\Ajax\Dossier;

use MatGyver\FormsFactory\Dossier\DossierProjectFormFactory;
use MatGyver\Services\Dossier\DossierProjectService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Requirement\Requirement;

/**
 * Class DossierProjectsAjaxController
 * @package MatGyver\Controllers\Ajax\Shop
 */
class DossierProjectsAjaxController extends AbstractDossierAjaxController
{
    #[Route('/ajax/app/dossier/project/edit/{projectId}/{dossierId}/', name: 'ajax_app_dossier_project_edit', requirements: ['projectId' => Requirement::POSITIVE_INT, 'dossierId' => Requirement::POSITIVE_INT])]
    public function dossierProjectEdit(int $projectId, int $dossierId)
    {
        $dossier = $this->getDossier();

        $dossierProject = $this->get(DossierProjectService::class)->getRepository()->findOneBy(['id' => $projectId, 'dossier' => $dossier]);
        if (!$dossierProject) {
            $this->sendError(__('Ce projet n\'existe pas.'));
            return;
        }

        $dossierProjectForm = new DossierProjectFormFactory($dossier);
        $content = $this->builderForm->renderAsAjaxPanel($dossierProjectForm, $dossierProject);

        $this->sendData([
            'title' => __('Modification d\'un projet'),
            'html' => $content
        ]);
    }
}
