<?php

namespace MatGyver\Controllers\Ajax\Request;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Forms\Request\User\RequestUserPageForm;
use MatGyver\Services\Request\User\RequestUserPageService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersLoginService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class RequestUserPageAjaxController
 * @package MatGyver\Controllers\Ajax\Request
 */
class RequestUserPageAjaxController extends AbstractAjaxController
{

    #[Route('/ajax/site/request/user/page/validate/', name: 'ajax_site_request_user_page_validate')]
    public function requestUserPage()
    {
        $submittedData = getPostData($_POST);
        if (!RightsService::isEditor()) {
            if (!$this->get(UsersLoginService::class)->isConnected()) {
                $this->sendError('You must be connected');
            }

            $requestUserPageId = $submittedData['request_user_page_id'] ?? null;
            if (!$requestUserPageId) {
                $this->sendError('Invalid page');
            }

            $requestUserPage = $this->get(RequestUserPageService::class)->getRepository()->find($requestUserPageId);
            if (!$requestUserPage) {
                $this->sendError('Invalid page');
            }
            if ($requestUserPage->getRequestUser()->getDossier()->getContact()?->getUser()->getId() !== $_SESSION['user']['id']) {
                $this->sendError('Invalid page');
            }
        }

        $validate = $this->get(RequestUserPageForm::class)->validatePostInsert($submittedData);
        if (!$validate['valid']) {
            $this->sendError($validate['message'], ['step' => $validate['step'] ?? 1]);
        }

        $this->sendSuccess();
    }
}
