<?php

namespace MatGyver\Controllers\Ajax\Calendar;

use Mat<PERSON>yver\Controllers\Ajax\AbstractAjaxController;
use Mat<PERSON><PERSON><PERSON>\Entity\Calendar\Calendar;
use MatG<PERSON>ver\Entity\Calendar\CalendarAppointment;
use MatGyver\Entity\Consulting\ConsultingReason;
use MatGyver\Forms\Calendar\CalendarAppointmentTutorialForm;
use MatGyver\Services\Calendar\CalendarAppointmentService;
use MatGyver\Services\Calendar\CalendarService;
use MatGyver\Services\Consulting\ConsultingReasonService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Users\UsersExpertsService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class AppCalendarAvailabilitiesAjaxController
 * @package MatGyver\Controllers\Ajax\Calendar
 */
class AppCalendarAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/app/calendar/appointment/tutorial/', name: 'ajax_app_calendar_appointment_tutorial')]
    public function appCalendarAppointmentTutorial()
    {
        if (!RightsService::hasAccess(UNIVERSE_APP_DOSSIER)) {
            $this->sendUnauthorizedAccess();
        }

        $submittedData = $this->submittedData;

        $validatePost = $this->get(CalendarAppointmentTutorialForm::class)->validatePostInsert($submittedData);
        if (!$validatePost['valid']) {
            $this->sendOutput([
                'status' => false,
                'message' => $validatePost['message'],
                'step' => $validatePost['step'] ?? 1,
            ]);
        }

        $this->sendSuccess();
    }

    #[Route('/ajax/app/calendar/availabilities/', name: 'ajax_app_calendar_availabilities')]
    public function ajaxAppCalendarAvailabilities()
    {
        $events = [];

        $start = new \DateTime(filter_var($this->submittedData['start'], FILTER_UNSAFE_RAW));
        $end = new \DateTime(filter_var($this->submittedData['end'], FILTER_UNSAFE_RAW));

        $appointmentDuration = filter_var($this->submittedData['appointmentDuration'], FILTER_VALIDATE_INT);

        $usersIds = [];
        if (isset($this->submittedData['users']) and $this->submittedData['users']) {
            $usersIds = explode(',', filter_var($this->submittedData['users'], FILTER_UNSAFE_RAW));
        }
        if (!$usersIds) {
            $this->sendError400(__('Aucun administrateur sélectionné'));
            return;
        }

        /** @var Calendar $calendar */
        $calendars = [];
        foreach ($usersIds as $userId) {
            $calendar = $this->get(CalendarService::class)->getRepository()->findOneBy(['user' => (int)$userId]);
            if ($calendar and $calendar->getActive()) {
                $calendars[] = $calendar;
            }
        }
        if (!$calendars) {
            $this->sendError400(__('Aucun calendrier sélectionné'));
            return;
        }

        $consultingReasonId = null;
        if (isset($this->submittedData['consultingReasonId']) and $this->submittedData['consultingReasonId']) {
            $consultingReasonId = filter_var($this->submittedData['consultingReasonId'], FILTER_VALIDATE_INT);
        }
        if (!$consultingReasonId) {
            $this->sendError400(__('Aucun motif de consultation sélectionné'));
            return;
        }

        /** @var ConsultingReason $consultingReason */
        $consultingReason = $this->get(ConsultingReasonService::class)->getRepository()->findWoClient($consultingReasonId);
        if (!$consultingReason) {
            $this->sendError400(__('Ce motif de consultation n\'existe pas'));
            return;
        }

        $allAvailableDates = [];
        foreach ($calendars as $calendar) {
            $availableDates = $this->get(CalendarService::class)->getCalendarAvailableDates($calendar, $consultingReason, $start, $end, $appointmentDuration);
            if ($availableDates) {
                $allAvailableDates[] = $availableDates;
            }
        }
        if (!$allAvailableDates) {
            echo json_encode($events);
            exit();
        }

        // Find common available slots across all calendars
        if (count($allAvailableDates) > 1) {
            $commonAvailableDates = call_user_func_array('array_intersect', $allAvailableDates);
        } else {
            $commonAvailableDates = $allAvailableDates[0] ?? [];
        }
        if (!$commonAvailableDates) {
            echo json_encode($events);
            exit();
        }

        foreach ($commonAvailableDates as $availableDate) {
            $date = new \DateTime($availableDate);
            $dateEnd = clone $date;
            $dateEnd->modify('+' . $appointmentDuration . ' minutes');
            $event = [
                'title' => $date->format('H:i'),
                'start' => $date->format('c'),
                'end' => $dateEnd->format('c'),
                'className' => 'fc-event-success'
            ];
            $events[] = $event;
        }

        echo json_encode($events);
        exit();
    }

    #[Route('/ajax/app/calendar/home/<USER>/days/', name: 'ajax_app_calendar_home_events_days')]
    public function ajaxAppCalendarHomeEventsDays(): void
    {
        $month = filter_var($this->submittedData['month'], FILTER_VALIDATE_INT);
        $year = filter_var($this->submittedData['year'], FILTER_VALIDATE_INT);

        try {
            $dateStart = new \DateTime($year . '-' . ($month + 1) . '-01 00:00:00');
        } catch (\Exception $e) {
            $this->sendError400(__('Date de début incorrecte'));
            return;
        }

        $dateEnd = clone $dateStart;
        $dateEnd->modify('last day of this month 23:59:59');

        $calendarIds = [];
        if (UsersExpertsService::isExpert(null, true)) {
            $calendar = $this->get(CalendarService::class)->findMyCalendar();
            if ($calendar) {
                $calendarIds[] = $calendar->getId();
            }
        }

        $appointments = $this->get(CalendarAppointmentService::class)->getRepository()->findByDates($dateStart, $dateEnd, $calendarIds);
        if (!$appointments) {
            $this->sendData(['days' => []]);
        }

        $days = [];
        foreach ($appointments as $appointment) {
            if (in_array($appointment->getStatus(), [CalendarAppointment::STATUS_CANCELED, CalendarAppointment::STATUS_USER_ABSENT, CalendarAppointment::STATUS_CLIENT_ABSENT])) {
                continue;
            }
            $day = $appointment->getDateStart()->format('Y-m-d');
            $days[] = $day;
        }
        $days = array_filter($days);

        $this->sendData(['days' => $days]);
    }
}
