<?php

namespace MatGyver\Controllers\Ajax\Calendar;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use Mat<PERSON>yver\Entity\Consulting\ConsultingReason;
use MatGyver\Enums\ProductsEnum;
use MatGyver\Forms\Calendar\SiteCalendarAppointmentMakeForm;
use MatGyver\Forms\Shop\ShopEcommerceOrderForm;
use MatGyver\Helpers\Chars;
use MatGyver\Services\Calendar\CalendarService;
use MatGyver\Services\Consulting\ConsultingReasonService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Shop\Ecommerce\ShopEcommerceCartService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class CalendarAvailabilitiesAjaxController
 * @package MatGyver\Controllers\Ajax\Calendar
 */
class SiteCalendarsAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/calendars/list/{consultingReasonId}/', name: 'ajax_site_calendars_list_consulting_reason_id')]
    public function ajaxSiteCalendarsListByConsultingReason()
    {
        $consultingReasonId = filter_var($this->param, FILTER_VALIDATE_INT);

        $consultingReason = $this->get(ConsultingReasonService::class)->getRepository()->findOneBy(['id' => $consultingReasonId, 'active' => true]);
        if (!$consultingReason) {
            $this->sendError(__('Ce motif de consultation n\'est pas disponible.'));
            return;
        }

        $calendars = $this->get(CalendarService::class)->getRepository()->findByConsultingReason($consultingReason);
        if (!$calendars) {
            $this->sendError(__('Aucun calendrier disponible pour ce motif de consultation.'));
            return;
        }

        $content = $this->parser->set('calendars', $calendars)
            ->render('forms/site/calendar/consulting_reason_calendars.php');
        $this->sendData(['content' => $content, 'consulting_reason_message' => $consultingReason->getMessage()]);
        exit();
    }

    #[Route('/ajax/calendar/appointment/make/', name: 'ajax_calendar_appointment_make')]
    public function calendarAppointmentMake()
    {
        $submittedData = $this->submittedData;
        $validate = $this->get(SiteCalendarAppointmentMakeForm::class)->validatePostInsert($submittedData, true);
        if (!$validate['valid']) {
            $this->sendError($validate['message'], ['step' => $validate['step'] ?? 'consulting_reason']);
        }

        $consultingReasonId = filter_var($this->submittedData['consulting_reason_id'], FILTER_VALIDATE_INT);
        $consultingReason = $this->get(ConsultingReasonService::class)->getRepository()->findOneBy(['id' => $consultingReasonId, 'active' => true]);

        $paymentType = $consultingReason->getPaymentType();
        if (!$consultingReason->getPriceTaxIncl()) {
            $paymentType = ConsultingReason::PAYMENT_ON_SITE;
        }
        $output = ['status' => true, 'paymentType' => $paymentType];
        if ($paymentType == ConsultingReason::PAYMENT_ON_SITE) {
            $this->sendData($output);
            return;
        }

        $stripeAccount = $this->get(IntegrationAccountsService::class)->getAccountsByType('stripe');
        if (!$stripeAccount) {
            $output['paymentType'] = ConsultingReason::PAYMENT_ON_SITE;
            $this->sendData($output);
            return;
        }

        $product = $consultingReason->getProduct();
        if (!$product) {
            $product = $this->get(ShopProductsService::class)->getRepository()->findOneBy(['type' => ProductsEnum::TYPE_APPOINTMENT, 'client' => CLIENT_MASTER]);
        }
        if (!$product) {
            $output['paymentType'] = ConsultingReason::PAYMENT_ON_SITE;
            $this->sendData($output);
            return;
        }

        $paymentId = null;
        if (isset($submittedData['id_payment'])) {
            $paymentId = filter_var($submittedData['id_payment'], FILTER_VALIDATE_INT);
        }

        $this->get(ShopEcommerceCartService::class)->addProductInCart($product, 1, [], $paymentId);

        $submittedData = $this->submittedData;
        $submittedData['appointment'] = [
            'consulting_reason_id' => $consultingReasonId,
            'calendar_id' => filter_var($this->submittedData['calendar_id'], FILTER_VALIDATE_INT),
            'date_start' => filter_var($this->submittedData['date_start'], FILTER_VALIDATE_INT),
            'comment' => filter_var($this->submittedData['comment'], FILTER_UNSAFE_RAW),
        ];
        $processProductOrder = $this->get(ShopEcommerceOrderForm::class)->processProductOrder($submittedData);
        if (!$processProductOrder['valid']) {
            $this->sendError($processProductOrder['message'], ['step' => $validate['step'] ?? 'payment']);
        }

        $output = array_merge($output, $processProductOrder);
        $this->sendData($output);
    }

    #[Route('/ajax/calendar/appointment/embed/', name: 'ajax_calendar_appointment_embed')]
    public function calendarAppointmentEmbed()
    {
        $this->submittedData['password'] = Chars::generateUniqid() . strtoupper(Chars::generateUniqid()) . '&!';
        $this->submittedData['password2'] = $this->submittedData['password'];
        $this->submittedData['zip'] = '75000';
        $this->submittedData['country'] = 'FR';

        $this->calendarAppointmentMake();
    }
}
