<?php

namespace MatGyver\Controllers\Ajax\Clients;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Forms\Clients\ClientSignupForm;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class ClientsSignupAjaxController
 * @package MatGyver\Controllers\Ajax\Clients
 */
class ClientsSignupAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/client/signup/tutorial/', name: 'ajax_client_signup_tutorial')]
    public function clientSignupTutorial()
    {
        $submittedData = getPostData($_POST);
        $validate = $this->get(ClientSignupForm::class)->validatePostInsert($submittedData, false);
        if (!$validate['valid']) {
            $this->sendError($validate['message'], ['step' => $validate['step'] ?? 1]);
        }

        $this->sendSuccess();
    }
}
