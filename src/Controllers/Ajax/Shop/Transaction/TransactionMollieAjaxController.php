<?php

namespace MatGyver\Controllers\Ajax\Shop\Transaction;

use MatGyver\Controllers\Ajax\AbstractAjaxController;
use MatGyver\Services\Shop\Transaction\Cards\ShopTransactionMollieCardService;
use MatGyver\Services\Shop\Transaction\ShopTransactionMollieService;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class TransactionMollieAjaxController
 * @package MatGyver\Controllers\Ajax\Shop\Transaction
 */
class TransactionMollieAjaxController extends AbstractAjaxController
{
    #[Route('/ajax/mollie/charge/', name: 'ajax_mollie_charge')]
    public function mollieCharge()
    {
        $charge = $this->get(ShopTransactionMollieService::class)->charge();
        if (isset($charge['valid']) and !$charge['valid']) {
            $this->sendOutput(['error' => $charge['message']]);
            return;
        }

        $this->sendOutput($charge);
    }

    #[Route('/ajax/mollie/add_card/', name: 'ajax_mollie_add_card')]
    public function mollieAddCard()
    {
        $updateCard = $this->get(ShopTransactionMollieCardService::class)->postAddCard();
        if (isset($updateCard['valid']) and !$updateCard['valid']) {
            $this->sendOutput(['error' => $updateCard['message']]);
            return;
        }

        $this->sendOutput($updateCard);
    }
}
