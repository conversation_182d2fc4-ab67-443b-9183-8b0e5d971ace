<?php

namespace MatGyver\Entity\Mail\Queue;

use Doctrine\ORM\Mapping as ORM;
use Mat<PERSON><PERSON><PERSON>\Entity\Client\Client;
use MatGyver\Entity\User\User;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'mg_mail_queue_users')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mail\Queue\MailQueueRepository::class)]
class MailQueueUser
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'mail_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Mail\Queue\MailQueue::class)]
    private $mail;

    #[ORM\JoinColumn(nullable: false, name: 'client_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: true, name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $user;

    #[ORM\Column(type: 'string', length: 255)]
    private $firstName;

    #[ORM\Column(type: 'string', length: 255)]
    private $lastName;

    #[ORM\Column(type: 'string', length: 255)]
    private $email;

    #[ORM\Column(type: 'string')]
    private $vars = '';

    #[ORM\Column(type: 'boolean')]
    private $sent = false;

    #[ORM\Column(type: 'boolean')]
    private $error = false;

    #[ORM\Column(type: 'string')]
    private $errorMessage = '';

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: 'datetime')]
    private $dateAdd;

    #[ORM\Column(type: 'datetime')]
    private $dateSent = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMail(): ?MailQueue
    {
        return $this->mail;
    }

    public function setMail(?MailQueue $mail): self
    {
        $this->mail = $mail;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getVars(): ?string
    {
        return $this->vars;
    }

    public function setVars(string $vars): self
    {
        $this->vars = $vars;

        return $this;
    }

    public function getSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getError(): ?bool
    {
        return $this->error;
    }

    public function setError(bool $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    public function getDateAdd(): ?\DateTimeInterface
    {
        return $this->dateAdd;
    }

    public function setDateAdd(\DateTimeInterface $dateAdd): self
    {
        $this->dateAdd = $dateAdd;

        return $this;
    }

    public function getDateSent(): ?\DateTimeInterface
    {
        return $this->dateSent;
    }

    public function setDateSent(\DateTimeInterface $dateSent): self
    {
        $this->dateSent = $dateSent;

        return $this;
    }
}
