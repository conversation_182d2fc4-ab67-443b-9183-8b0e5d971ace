<?php

namespace MatGyver\Entity\Mail\Queue;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Client\Client;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use MatGyver\Entity\Dossier\Dossier;

#[ORM\Table(name: 'mg_mail_queue')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mail\Queue\MailQueueRepository::class)]
class MailQueue
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(name: 'client_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(name: 'dossier_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Dossier\Dossier::class)]
    private $dossier = null;

    #[ORM\Column(type: 'integer')]
    private $typeId;

    #[ORM\Column(type: 'string', length: 250)]
    private $subject;

    #[ORM\Column(type: 'string')]
    private $message;

    #[ORM\Column(type: 'string', nullable: true)]
    private $attachments;

    #[ORM\Column(type: 'string', length: 250, nullable: true)]
    private $fromName;

    #[ORM\Column(type: 'string', length: 250)]
    private $fromEmail;

    #[ORM\Column(type: 'boolean')]
    private $sent = false;

    #[ORM\Column(type: 'boolean')]
    private $error = false;

    #[ORM\Column(type: 'string')]
    private $errorMessage = '';

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: 'datetime')]
    private $dateAdd;

    #[ORM\Column(type: 'datetime')]
    private $dateSent = null;

    #[ORM\OneToMany(mappedBy: 'mail', targetEntity: MailQueueUser::class)]
    private $users;

    public function __construct()
    {
        $this->users = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getTypeId(): int
    {
        return $this->typeId;
    }

    public function setTypeId(int $typeId): self
    {
        $this->typeId = $typeId;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getAttachments(): ?string
    {
        return $this->attachments;
    }

    public function setAttachments(?string $attachments): self
    {
        $this->attachments = $attachments;

        return $this;
    }

    public function getFromName(): ?string
    {
        return $this->fromName;
    }

    public function setFromName(?string $fromName): self
    {
        $this->fromName = $fromName;

        return $this;
    }

    public function getFromEmail(): ?string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(string $fromEmail): self
    {
        $this->fromEmail = $fromEmail;

        return $this;
    }

    public function getSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getError(): ?bool
    {
        return $this->error;
    }

    public function setError(bool $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function setErrorMessage(string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    public function getDateAdd(): ?\DateTimeInterface
    {
        return $this->dateAdd;
    }

    public function setDateAdd(\DateTimeInterface $dateAdd): self
    {
        $this->dateAdd = $dateAdd;

        return $this;
    }

    public function getDateSent(): ?\DateTimeInterface
    {
        return $this->dateSent;
    }

    public function setDateSent(\DateTimeInterface $dateSent): self
    {
        $this->dateSent = $dateSent;

        return $this;
    }

    /**
     * @return Collection|MailQueueUser[]
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(MailQueueUser $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->setMail($this);
        }

        return $this;
    }

    public function removeUser(MailQueueUser $user): self
    {
        if ($this->users->contains($user)) {
            $this->users->removeElement($user);
            // set the owning side to null (unless already changed)
            if ($user->getMail() === $this) {
                $user->setMail(null);
            }
        }

        return $this;
    }
}
