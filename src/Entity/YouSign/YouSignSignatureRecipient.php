<?php

namespace MatGyver\Entity\YouSign;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\User\User;

#[ORM\Table(name: 'yousign_signatures_recipients')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\YouSign\YouSignSignatureRecipientRepository::class)]
class YouSignSignatureRecipient
{
    const STATUS_SENT = 'sent';
    const STATUS_DONE = 'done';
    const STATUS_ERROR = 'error';
    const STATUS_DECLINED = 'declined';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\YouSign\YouSignSignature::class)]
    private $signature;

    #[ORM\Column(type: 'string')]
    private $recipientId;

    #[ORM\Column(type: 'string')]
    private $signatureLink = '';

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\User\User::class)]
    private $user = null;

    #[ORM\Column(type: 'string')]
    private $firstName = '';

    #[ORM\Column(type: 'string')]
    private $lastName = '';

    #[ORM\Column(type: 'string')]
    private $email = '';

    #[ORM\Column(type: 'string')]
    private $telephone = '';

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateSigned = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getSignature(): ?YouSignSignature
    {
        return $this->signature;
    }

    public function setSignature(?YouSignSignature $signature): self
    {
        $this->signature = $signature;

        return $this;
    }

    public function getRecipientId(): ?string
    {
        return $this->recipientId;
    }

    public function setRecipientId(string $recipientId): self
    {
        $this->recipientId = $recipientId;

        return $this;
    }

    public function getSignatureLink(): ?string
    {
        return $this->signatureLink;
    }

    public function setSignatureLink(string $signatureLink): self
    {
        $this->signatureLink = $signatureLink;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateSigned(): ?\DateTimeInterface
    {
        return $this->dateSigned;
    }

    public function setDateSigned(?\DateTimeInterface $dateSigned): self
    {
        $this->dateSigned = $dateSigned;

        return $this;
    }

    public function getStatusLabel(): ?string
    {
        return match ($this->getStatus()) {
            self::STATUS_ERROR, self::STATUS_DECLINED => 'danger',
            self::STATUS_DONE => 'success',
            default => 'info',
        };
    }

    public function getStatusName(): ?string
    {
        return match ($this->getStatus()) {
            self::STATUS_ERROR => __('Erreur'),
            self::STATUS_DECLINED => __('Décliné'),
            self::STATUS_DONE => __('Signé'),
            self::STATUS_SENT => __('Envoyé pour signature'),
            default => __('Inconnu'),
        };
    }
}
