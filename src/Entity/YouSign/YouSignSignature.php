<?php

namespace MatGyver\Entity\YouSign;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\Dossier\DossierDocument;

#[ORM\Table(name: 'yousign_signatures')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\YouSign\YouSignSignatureRepository::class)]
class YouSignSignature
{
    const STATUS_SENT = 'sent';
    const STATUS_DONE = 'done';
    const STATUS_ERROR = 'error';
    const STATUS_EXPIRED = 'expired';
    const STATUS_DECLINED = 'declined';
    const STATUS_CANCELED = 'canceled';
    const STATUS_DELETED = 'deleted';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Client\Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Dossier\Dossier::class)]
    private $dossier;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Dossier\DossierDocument::class)]
    private $document;

    #[ORM\Column(type: 'string')]
    private $documents = '';

    #[ORM\Column(type: 'string')]
    private $signatureRequestId;

    #[ORM\Column(type: 'string')]
    private $signatureDocumentId;

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateSigned = null;

    #[ORM\OneToMany(targetEntity: YouSignSignatureRecipient::class, mappedBy: 'signature')]
    private $recipients;

    public function __construct()
    {
        $this->recipients = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getDocument(): ?DossierDocument
    {
        return $this->document;
    }

    public function setDocument(?DossierDocument $document): self
    {
        $this->document = $document;

        return $this;
    }

    public function getDocuments(): array
    {
        return ($this->documents ? json_decode($this->documents, true) : []);
    }

    public function setDocuments(array $documents): self
    {
        $this->documents = json_encode($documents);

        return $this;
    }

    public function getSignatureRequestId(): ?string
    {
        return $this->signatureRequestId;
    }

    public function setSignatureRequestId(string $signatureRequestId): self
    {
        $this->signatureRequestId = $signatureRequestId;

        return $this;
    }

    public function getSignatureDocumentId(): ?string
    {
        return $this->signatureDocumentId;
    }

    public function setSignatureDocumentId(string $signatureDocumentId): self
    {
        $this->signatureDocumentId = $signatureDocumentId;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateSigned(): ?\DateTimeInterface
    {
        return $this->dateSigned;
    }

    public function setDateSigned(?\DateTimeInterface $dateSigned): self
    {
        $this->dateSigned = $dateSigned;

        return $this;
    }

    public function getStatusLabel(): ?string
    {
        return match ($this->getStatus()) {
            self::STATUS_ERROR, self::STATUS_DECLINED, self::STATUS_CANCELED, self::STATUS_EXPIRED, self::STATUS_DELETED => 'danger',
            self::STATUS_DONE => 'success',
            default => 'info',
        };
    }

    public function getStatusName(): ?string
    {
        return match ($this->getStatus()) {
            self::STATUS_EXPIRED => __('Expiré'),
            self::STATUS_DECLINED => __('Décliné'),
            self::STATUS_CANCELED => __('Annulé'),
            self::STATUS_DELETED => __('Supprimé'),
            self::STATUS_ERROR => __('Erreur'),
            self::STATUS_DONE => __('Signé'),
            self::STATUS_SENT => __('Envoyé pour signature'),
            default => __('Inconnu'),
        };
    }

    /**
     * @return Collection|YouSignSignatureRecipient[]
     */
    public function getRecipients(): Collection
    {
        return $this->recipients;
    }

    public function addRecipient(YouSignSignatureRecipient $recipient): self
    {
        if (!$this->recipients->contains($recipient)) {
            $this->recipients[] = $recipient;
            $recipient->setSignature($this);
        }

        return $this;
    }

    public function removeRecipient(YouSignSignatureRecipient $recipient): self
    {
        if ($this->recipients->removeElement($recipient)) {
            // set the owning side to null (unless already changed)
            if ($recipient->getSignature() === $this) {
                $recipient->setSignature(null);
            }
        }

        return $this;
    }
}
