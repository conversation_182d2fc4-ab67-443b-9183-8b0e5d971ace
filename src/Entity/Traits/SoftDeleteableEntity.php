<?php

namespace MatGyver\Entity\Traits;

use Doctrine\ORM\Mapping as ORM;

/**
 * Trait SoftDeleteableEntity
 * @package MatGyver\Entity\Traits
 */
trait SoftDeleteableEntity
{
    #[ORM\Column(type: 'datetime', nullable: true)]
    private $deletedAt;

    /**
     * @return \DateTimeInterface|null
     */
    public function getDeletedAt(): ?\DateTimeInterface
    {
        return $this->deletedAt;
    }

    /**
     * @param \DateTimeInterface|null $deletedAt
     * @return $this
     */
    public function setDeletedAt(?\DateTimeInterface $deletedAt): self
    {
        $this->deletedAt = $deletedAt;

        return $this;
    }
}
