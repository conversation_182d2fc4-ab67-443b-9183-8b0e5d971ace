<?php

namespace MatGyver\Entity\Mollie\Charge;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\PaymentMethod\PaymentMethodCharge;

#[ORM\Table(name: 'mg_mollie_charges')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Mollie\Charge\MollieChargeRepository::class)]
class MollieCharge extends PaymentMethodCharge
{
    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private $customerId;

    #[ORM\Column(type: 'string', length: 100)]
    private $paymentMethod;

    #[ORM\Column(type: 'string', length: 100)]
    private $secretKey;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private $idMollieCharge;

    #[ORM\Column(type: 'float')]
    private $amount;

    #[ORM\Column(type: 'string', length: 20)]
    private $status = '';

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function setCustomerId(?string $customerId): self
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(string $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getSecretKey(): ?string
    {
        return $this->secretKey;
    }

    public function setSecretKey(string $secretKey): self
    {
        $this->secretKey = $secretKey;

        return $this;
    }

    public function getIdMollieCharge(): ?string
    {
        return $this->idMollieCharge;
    }

    public function setIdMollieCharge(?string $idMollieCharge): self
    {
        $this->idMollieCharge = $idMollieCharge;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }
}
