<?php

namespace MatGyver\Entity\Powens;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;
use MatGyver\Repository\Powens\PowensUserRepository;

#[ORM\Table(name: 'powens_users')]
#[ORM\Entity(repositoryClass: PowensUserRepository::class)]
class PowensUser
{
    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $powensUserId;

    #[ORM\Column(type: 'text')]
    private $accessToken;

    #[ORM\Column(type: 'text', nullable: true)]
    private $refreshToken;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $tokenExpiresAt;

    #[ORM\Column(type: 'string', length: 255)]
    private $domain;

    #[ORM\Column(type: 'datetime')]
    private $createdAt;

    #[ORM\Column(type: 'datetime')]
    private $updatedAt;

    #[ORM\Column(type: 'boolean')]
    private $active = true;

    #[ORM\OneToMany(mappedBy: 'powensUser', targetEntity: PowensAccount::class, cascade: ['persist', 'remove'])]
    private $accounts;

    public function __construct()
    {
        $this->accounts = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPowensUserId(): ?string
    {
        return $this->powensUserId;
    }

    public function setPowensUserId(string $powensUserId): self
    {
        $this->powensUserId = $powensUserId;
        return $this;
    }

    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $accessToken): self
    {
        $this->accessToken = $accessToken;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getRefreshToken(): ?string
    {
        return $this->refreshToken;
    }

    public function setRefreshToken(?string $refreshToken): self
    {
        $this->refreshToken = $refreshToken;
        return $this;
    }

    public function getTokenExpiresAt(): ?\DateTime
    {
        return $this->tokenExpiresAt;
    }

    public function setTokenExpiresAt(?\DateTime $tokenExpiresAt): self
    {
        $this->tokenExpiresAt = $tokenExpiresAt;
        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain(string $domain): self
    {
        $this->domain = $domain;
        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }

    /**
     * @return Collection<int, PowensAccount>
     */
    public function getAccounts(): Collection
    {
        return $this->accounts;
    }

    public function addAccount(PowensAccount $account): self
    {
        if (!$this->accounts->contains($account)) {
            $this->accounts[] = $account;
            $account->setPowensUser($this);
        }

        return $this;
    }

    public function removeAccount(PowensAccount $account): self
    {
        if ($this->accounts->removeElement($account)) {
            if ($account->getPowensUser() === $this) {
                $account->setPowensUser(null);
            }
        }

        return $this;
    }

    public function isTokenExpired(): bool
    {
        if (!$this->tokenExpiresAt) {
            return false;
        }

        return $this->tokenExpiresAt < new \DateTime();
    }
}
