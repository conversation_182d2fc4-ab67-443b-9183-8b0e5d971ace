<?php

namespace MatGyver\Entity\Support\Rate;

use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use MatG<PERSON>ver\Entity\Support\Ticket\SupportTicket;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'hdk_rates')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Support\Rate\SupportRateRepository::class)]
class SupportRate
{
    const TYPE_HAPPY = 'happy';
    const TYPE_NEUTRAL = 'neutral';
    const TYPE_NOT_HAPPY = 'not_happy';

    const SMILEY_HAPPY = '&#128515;';
    const SMILEY_NEUTRAL = '&#128528;';
    const SMILEY_NOT_HAPPY = '&#128542;';

    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 20)]
    private $type;

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\JoinColumn(nullable: false, name: 'ticket_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \MatGyver\Entity\Support\Ticket\SupportTicket::class)]
    private $ticket;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getTicket(): ?SupportTicket
    {
        return $this->ticket;
    }

    public function setTicket(?SupportTicket $ticket): self
    {
        $this->ticket = $ticket;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
