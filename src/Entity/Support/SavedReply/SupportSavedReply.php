<?php

namespace MatGyver\Entity\Support\SavedReply;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Support\Department\SupportDepartment;

#[ORM\Table(name: 'hdk_saved_replies')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Support\SavedReply\SupportSavedReplyRepository::class)]
class SupportSavedReply
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: true, name: 'department_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Support\Department\SupportDepartment::class)]
    private $department;

    #[ORM\Column(type: 'string', length: 200)]
    private $question = '';

    #[ORM\Column(type: 'text')]
    private $reply;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDepartment(): ?SupportDepartment
    {
        return $this->department;
    }

    public function setDepartment(?SupportDepartment $department): self
    {
        $this->department = $department;

        return $this;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(string $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getReply(): ?string
    {
        return $this->reply;
    }

    public function setReply(string $reply): self
    {
        $this->reply = $reply;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

}
