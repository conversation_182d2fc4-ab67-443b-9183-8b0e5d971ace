<?php

namespace MatGyver\Entity\Project;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'project_templates')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Project\ProjectTemplateRepository::class)]
class ProjectTemplate
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'text', nullable: true)]
    private $description;

    #[ORM\Column(type: 'string', length: 50)]
    private $label;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Project\ProjectTemplateTask::class, mappedBy: 'projectTemplate', cascade: ['persist', 'remove'])]
    private $tasks;

    public function __construct()
    {
        $this->tasks = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    /**
     * @return Collection|ProjectTemplateTask[]
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(ProjectTemplateTask $task): self
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks[] = $task;
            $task->setProjectTemplate($this);
        }

        return $this;
    }

    public function removeTask(ProjectTemplateTask $task): self
    {
        if ($this->tasks->removeElement($task)) {
            // set the owning side to null (unless already changed)
            if ($task->getProjectTemplate() === $this) {
                $task->setProjectTemplate(null);
            }
        }

        return $this;
    }
}
