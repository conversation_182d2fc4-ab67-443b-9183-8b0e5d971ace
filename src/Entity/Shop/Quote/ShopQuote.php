<?php

namespace MatGyver\Entity\Shop\Quote;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Dossier\Dossier;
use Mat<PERSON>yver\Entity\Dossier\DossierDocument;
use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Entity\Traits\UserEntity;

#[ORM\Table(name: 'shop_quotes')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Quote\ShopQuoteRepository::class)]
class ShopQuote
{
    use ClientEntity;
    use UserEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: Dossier::class, inversedBy: 'quotes')]
    private $dossier = null;

    #[ORM\Column(type: 'string', length: 50)]
    private $prefix;

    #[ORM\Column(type: 'integer')]
    private $number;

    #[ORM\Column(type: 'text')]
    private $product;

    #[ORM\Column(type: 'float')]
    private $amountTaxExcl;

    #[ORM\Column(type: 'float')]
    private $amountTaxIncl;

    #[ORM\Column(type: 'string', length: 3)]
    private $currency = 'EUR';

    #[ORM\Column(type: 'text')]
    private $customer;

    #[ORM\Column(type: 'text')]
    private $settings;

    #[ORM\Column(type: 'text')]
    private $observations = '';

    #[ORM\Column(type: 'boolean')]
    private $signed = false;

    #[ORM\Column(type: 'string', length: 255)]
    private $signature = '';

    #[ORM\JoinColumn(nullable: true, name: 'document_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: DossierDocument::class)]
    private $document = null;

    #[ORM\Column(type: 'datetime')]
    private $dateAdd;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateUpdate = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateSigned = null;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Shop\Quote\ShopQuoteProduct::class, cascade: ['persist', 'remove'], mappedBy: 'quote')]
    private $quoteProducts;

    public function __construct()
    {
        $this->quoteProducts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    public function setPrefix(string $prefix): self
    {
        $this->prefix = $prefix;

        return $this;
    }

    public function getNumber(): ?int
    {
        return $this->number;
    }

    public function setNumber(int $number): self
    {
        $this->number = $number;

        return $this;
    }

    public function getProduct(): ?string
    {
        return $this->product;
    }

    public function setProduct(string $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getAmountTaxExcl(): ?float
    {
        return $this->amountTaxExcl;
    }

    public function setAmountTaxExcl(float $amountTaxExcl): self
    {
        $this->amountTaxExcl = $amountTaxExcl;

        return $this;
    }

    public function getAmountTaxIncl(): ?float
    {
        return $this->amountTaxIncl;
    }

    public function setAmountTaxIncl(float $amountTaxIncl): self
    {
        $this->amountTaxIncl = $amountTaxIncl;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getCustomer(): ?string
    {
        return $this->customer;
    }

    public function setCustomer(string $customer): self
    {
        $this->customer = $customer;

        return $this;
    }

    public function getSettings(): ?string
    {
        return $this->settings;
    }

    public function setSettings(string $settings): self
    {
        $this->settings = $settings;

        return $this;
    }

    public function getObservations(): ?string
    {
        return $this->observations;
    }

    public function setObservations(string $observations): self
    {
        $this->observations = $observations;

        return $this;
    }

    public function isSigned(): ?bool
    {
        return $this->signed;
    }

    public function setSigned(bool $signed): self
    {
        $this->signed = $signed;

        return $this;
    }

    public function getSignature(): ?string
    {
        return $this->signature;
    }

    public function setSignature(string $signature): self
    {
        $this->signature = $signature;

        return $this;
    }

    public function getDocument(): ?DossierDocument
    {
        return $this->document;
    }

    public function setDocument(?DossierDocument $document): self
    {
        $this->document = $document;

        return $this;
    }

    public function getDateAdd(): ?\DateTimeInterface
    {
        return $this->dateAdd;
    }

    public function setDateAdd(\DateTimeInterface $dateAdd): self
    {
        $this->dateAdd = $dateAdd;

        return $this;
    }

    public function getDateUpdate(): ?\DateTimeInterface
    {
        return $this->dateUpdate;
    }

    public function setDateUpdate(\DateTimeInterface $dateUpdate): self
    {
        $this->dateUpdate = $dateUpdate;

        return $this;
    }

    public function getDateSigned(): ?\DateTimeInterface
    {
        return $this->dateSigned;
    }

    public function setDateSigned(\DateTimeInterface $dateSigned): self
    {
        $this->dateSigned = $dateSigned;

        return $this;
    }

    /**
     * @return Collection|ShopQuoteProduct[]
     */
    public function getQuoteProducts(): Collection
    {
        return $this->quoteProducts;
    }

    public function addQuoteProduct(ShopQuoteProduct $quoteProduct): self
    {
        if (!$this->quoteProducts->contains($quoteProduct)) {
            $this->quoteProducts[] = $quoteProduct;
            $quoteProduct->setQuote($this);
        }

        return $this;
    }

    public function removeQuoteProduct(ShopQuoteProduct $quoteProduct): self
    {
        if ($this->quoteProducts->contains($quoteProduct)) {
            $this->quoteProducts->removeElement($quoteProduct);
            // set the owning side to null (unless already changed)
            if ($quoteProduct->getQuote() === $this) {
                $quoteProduct->setQuote(null);
            }
        }

        return $this;
    }
}
