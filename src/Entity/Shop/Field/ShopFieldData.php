<?php

namespace MatGyver\Entity\Shop\Field;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'shop_fields_data')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Shop\Field\ShopFieldDataRepository::class)]
class ShopFieldData
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false, name: 'field_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Field\ShopField::class, inversedBy: 'fieldData')]
    private $field;

    #[ORM\Column(type: 'string', length: 250)]
    private $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getField(): ?ShopField
    {
        return $this->field;
    }

    public function setField(?ShopField $field): self
    {
        $this->field = $field;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

}
