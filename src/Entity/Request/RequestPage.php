<?php

namespace MatGyver\Entity\Request;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Dossier\DossierAsset;
use MatGyver\Entity\Dossier\DossierPerson;
use MatGyver\Repository\Request\RequestPageRepository;

#[ORM\Table(name: 'rq_pages')]
#[ORM\Entity(repositoryClass: RequestPageRepository::class)]
class RequestPage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $slug;

    #[ORM\Column(type: 'string', length: 255)]
    private $title;

    #[ORM\Column(type: 'text')]
    private $introduction = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $type = '';

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\OneToMany(targetEntity: RequestStep::class, mappedBy: 'page')]
    #[ORM\OrderBy(['position' => 'ASC'])]
    private $steps;

    private ?DossierAsset $asset = null;

    private ?DossierPerson $person = null;

    public function __construct()
    {
        $this->steps = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getIntroduction(): ?string
    {
        return $this->introduction;
    }

    public function setIntroduction(string $introduction): self
    {
        $this->introduction = $introduction;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    /**
     * @return Collection<array, RequestStep>
     */
    public function getSteps(): Collection
    {
        return $this->steps;
    }

    public function addStep(RequestStep $step): self
    {
        if (!$this->steps->contains($step)) {
            $this->steps[] = $step;
            $step->setPage($this);
        }

        return $this;
    }

    public function removeStep(RequestStep $step): self
    {
        if ($this->steps->removeElement($step)) {
            // set the owning side to null (unless already changed)
            if ($step->getPage() === $this) {
                $step->setPage(null);
            }
        }

        return $this;
    }
}
