<?php

namespace MatGyver\Entity\Request\User;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON><PERSON><PERSON>ver\Entity\Dossier\Dossier;
use MatGyver\Entity\User\User;
use MatGyver\Repository\Request\User\RequestUserRepository;
use MatGyver\Services\RightsService;

#[ORM\Table(name: 'rq_users')]
#[ORM\Entity(repositoryClass: RequestUserRepository::class)]
class RequestUser
{
    const STATUS_TO_DO = 'to_do';
    const STATUS_COMPLETED = 'accepted';
    const STATUS_TO_REVIEW = 'to_review';
    const STATUS_TO_REVIEW_USER = 'to_review_user';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private $user;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Dossier::class)]
    private $dossier;

    #[ORM\Column(type: 'text')]
    private $introduction;

    #[ORM\Column(type: 'text')]
    private $options = '';

    #[ORM\Column(type: 'integer')]
    private $percentage = 0;

    #[ORM\Column(type: 'string', length: 50)]
    private $status;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'date', nullable: true)]
    private $dateLimit;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getIntroduction(): ?string
    {
        return $this->introduction;
    }

    public function setIntroduction(string $introduction): self
    {
        $this->introduction = $introduction;

        return $this;
    }

    public function getOptions(): ?string
    {
        return $this->options;
    }

    public function setOptions(string $options): self
    {
        $this->options = $options;

        return $this;
    }

    public function getPercentage(): ?int
    {
        return $this->percentage;
    }

    public function setPercentage(int $percentage): self
    {
        $this->percentage = $percentage;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateLimit(): ?\DateTimeInterface
    {
        return $this->dateLimit;
    }

    public function setDateLimit(?\DateTimeInterface $dateLimit): self
    {
        $this->dateLimit = $dateLimit;

        return $this;
    }

    public function getStatusLabel(): ?string
    {
        return match ($this->getStatus()) {
            self::STATUS_COMPLETED => 'success',
            self::STATUS_TO_REVIEW, self::STATUS_TO_REVIEW_USER => 'warning',
            default => 'info',
        };
    }

    public function getStatusName(?bool $isEditor = null): ?string
    {
        if ($isEditor === null) {
            $isEditor = RightsService::isEditor();
        }
        if ($isEditor) {
            return match ($this->getStatus()) {
                self::STATUS_TO_DO => __('A compléter par le client'),
                self::STATUS_COMPLETED => __('Finalisée'),
                self::STATUS_TO_REVIEW => __('A vérifier'),
                self::STATUS_TO_REVIEW_USER => __('En cours de correction par l\'utilisateur'),
                default => __('Inconnu'),
            };
        }

        return match ($this->getStatus()) {
            self::STATUS_TO_DO => __('A compléter'),
            self::STATUS_COMPLETED => __('Finalisée'),
            self::STATUS_TO_REVIEW => __('En cours de vérification'),
            self::STATUS_TO_REVIEW_USER => __('A corriger'),
            default => __('Inconnu'),
        };
    }
}
