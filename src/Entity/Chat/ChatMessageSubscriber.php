<?php

namespace MatGyver\Entity\Chat;

use MatGyver\Repository\Chat\ChatMessageSubscriberRepository;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

#[ORM\Table(name: 'mg_chat_messages_subscribers')]
#[ORM\Entity(repositoryClass: ChatMessageSubscriberRepository::class)]
class ChatMessageSubscriber
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: ChatRoom::class)]
    private $room;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: ChatMessage::class)]
    private $message;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: ChatUser::class)]
    private $chatUser;

    #[ORM\Column(type: 'boolean')]
    private $seen = false;

    #[ORM\Column(type: 'boolean')]
    private $notification = false;

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: 'datetime')]
    private $dateAdd;

    #[ORM\Column(type: 'datetime')]
    private $dateSeen = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRoom(): ChatRoom
    {
        return $this->room;
    }

    public function setRoom(ChatRoom $room): self
    {
        $this->room = $room;

        return $this;
    }

    public function getMessage(): ChatMessage
    {
        return $this->message;
    }

    public function setMessage(ChatMessage $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getChatUser(): ?ChatUser
    {
        return $this->chatUser;
    }

    public function setChatUser(?ChatUser $chatUser): self
    {
        $this->chatUser = $chatUser;

        return $this;
    }

    public function getSeen(): bool
    {
        return $this->seen;
    }

    public function setSeen(bool $seen): self
    {
        $this->seen = $seen;

        return $this;
    }

    public function getNotification(): ?bool
    {
        return $this->notification;
    }

    public function setNotification(bool $notification): self
    {
        $this->notification = $notification;

        return $this;
    }

    public function getDateAdd(): ?\DateTimeInterface
    {
        return $this->dateAdd;
    }

    public function setDateAdd(\DateTimeInterface $dateAdd): self
    {
        $this->dateAdd = $dateAdd;

        return $this;
    }

    public function getDateSeen(): ?\DateTimeInterface
    {
        return $this->dateSeen;
    }

    public function setDateSeen(\DateTimeInterface $dateSeen): self
    {
        $this->dateSeen = $dateSeen;

        return $this;
    }
}
