<?php

namespace MatGyver\Entity\Chat;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use MatGyver\Entity\Dossier\Dossier;
use MatGyver\Entity\User\User;
use MatGyver\Helpers\Assets;
use MatGyver\Repository\Chat\ChatRoomRepository;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\TwigService;
use MatGyver\Services\Users\UsersService;
use Gedmo\Mapping\Annotation as Gedmo;

#[ORM\Table(name: 'mg_chat_rooms')]
#[ORM\Entity(repositoryClass: ChatRoomRepository::class)]
class ChatRoom
{
    const TYPE_USER = 'user';
    const TYPE_GROUP = 'group';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string')]
    private $name = '';

    #[ORM\Column(type: 'string', length: 20)]
    private $type = self::TYPE_USER;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateLastMessage = null;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Chat\ChatUser::class, mappedBy: 'room', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private $users;

    #[ORM\OneToMany(targetEntity: \MatGyver\Entity\Chat\ChatMessage::class, mappedBy: 'room', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private $messages;

    #[ORM\JoinColumn(nullable: true, name: 'dossier_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: Dossier::class, inversedBy: 'chatRoom')]
    private $dossier = null;

    public function __construct()
    {
        $this->users = new ArrayCollection();
        $this->messages = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateLastMessage(): ?\DateTimeInterface
    {
        return $this->dateLastMessage;
    }

    public function setDateLastMessage(?\DateTimeInterface $dateLastMessage = null): self
    {
        $this->dateLastMessage = $dateLastMessage;

        return $this;
    }

    /**
     * @return Collection|ChatUser[]
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(ChatUser $user): self
    {
        if (!$this->users->contains($user)) {
            $this->users[] = $user;
            $user->setRoom($this);
        }

        return $this;
    }

    public function removeUser(ChatUser $user): self
    {
        if ($this->users->contains($user)) {
            $this->users->removeElement($user);
            // set the owning side to null (unless already changed)
            if ($user->getRoom() === $this) {
                $user->setRoom(null);
            }
        }

        return $this;
    }

    /**
     * @param User|null $user
     * @return ChatUser|null
     */
    public function findUser(?User $user = null): ?ChatUser
    {
        if (!$user) {
            $container = ContainerBuilderService::getInstance();
            $user = $container->get(UsersService::class)->getUser();
        }
        if (!$user) {
            return null;
        }
        foreach ($this->getUsers() as $chatUser) {
            if ($chatUser->getUser() and $chatUser->getUser() === $user) {
                return $chatUser;
            }
        }
        return null;
    }

    /**
     * @return Collection|ChatMessage[]
     */
    public function getMessages(): Collection
    {
        return $this->messages;
    }

    /**
     * @return ChatMessage|null
     */
    public function getLastMessage(): ?ChatMessage
    {
        if (!$this->messages or !$this->messages->count()) {
            return null;
        }
        return $this->messages->last();
    }

    /**
     * @return int
     */
    public function getNbUserUnseenMessages(): int
    {
        $nbUnseenMessages = 0;
        if (!$this->messages or !$this->messages->count()) {
            return $nbUnseenMessages;
        }

        $chatRoomUser = $this->findUser();
        foreach ($this->getMessages() as $message) {
            foreach ($message->getSubscribers() as $subscriber) {
                if (!$subscriber->getSeen() and $subscriber->getChatUser() === $chatRoomUser) {
                    $nbUnseenMessages++;
                }
            }
        }
        return $nbUnseenMessages;
    }

    public function addMessage(ChatMessage $message): self
    {
        if (!$this->messages->contains($message)) {
            $this->messages[] = $message;
            $message->setRoom($this);
        }

        return $this;
    }

    public function removeMessage(ChatMessage $message): self
    {
        if ($this->messages->contains($message)) {
            $this->messages->removeElement($message);
            // set the owning side to null (unless already changed)
            if ($message->getRoom() === $this) {
                $message->setRoom(null);
            }
        }

        return $this;
    }

    /**
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getRoomUsersInfos(): array
    {
        $roomUsers = [];
        $container = ContainerBuilderService::getInstance();
        foreach ($this->getUsers() as $chatUser) {
            $img = Assets::getImageUrl('user_blank.png');
            $name = '';
            if ($chatUser->getUser()) {
                try {
                    $chatUser->getUser()->getEmail();
                } catch (\Exception $e) {
                    continue;
                }

                $img = $container->get(UsersService::class)->getAvatar($chatUser->getUser()->getEmail(), 50, $chatUser->getUser()->getClient()->getId());
                $name = $chatUser->getUser()->getFirstName() . ' ' . $chatUser->getUser()->getLastName();
            }

            $roomUsers[$chatUser->getId()] = [
                'name' => $name,
                'img' => $img
            ];
        }

        return $roomUsers;
    }

    /**
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getTitle(): string
    {
        if ($this->type == self::TYPE_GROUP) {
            return '
                <div class="text-dark-75 font-weight-bold font-size-h5 room-title">' . ($this->getName() ?: __('Groupe')) . '</div>
                <div>
				    <span class="font-weight-bold text-muted font-size-sm room-subtitle">' . __('%d membres', count($this->getUsers())) . '</span>
				</div>';
        }

        $chatRoomUser = $this->findUser();
        $roomUsersInfos = $this->getRoomUsersInfos();
        foreach ($roomUsersInfos as $chatUserId => $roomUsersInfo) {
            if ($chatUserId != $chatRoomUser->getId()) {
                return '<div class="text-dark-75 font-weight-bold font-size-h5 room-title">' . $roomUsersInfo['name'] . '</div>';
            }
        }
        return '';
    }

    /**
     * @return bool
     */
    public function isGroup(): bool
    {
        return count($this->users) > 2;
    }

    /**
     * @return string
     */
    public function getActions(): string
    {
        if ($this->getDossier()) {
            return '';
        }

        $isGroup = $this->isGroup();
        $chatRoomUser = $this->findUser();
        return TwigService::getInstance()->set('isGroup', $isGroup)
            ->set('chatRoomUser', $chatRoomUser)
            ->render('common/chat/room-actions.php');
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier = null): self
    {
        $this->dossier = $dossier;

        return $this;
    }
}
