<?php

namespace MatGyver\Entity\Activity;

use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;
use MatGyver\Entity\Traits\ClientEntity;

#[ORM\Table(name: 'mg_activity')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Activity\ActivityRepository::class)]
#[ORM\InheritanceType('SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'type', type: 'string')]
#[ORM\DiscriminatorMap(['transaction' => 'ActivityTransaction', 'support_ticket' => 'ActivitySupportTicket', 'support_reply' => 'ActivitySupportReply'])]
abstract class Activity
{
    use TimestampableEntity;
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'boolean')]
    private $viewed = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getViewed(): ?bool
    {
        return $this->viewed;
    }

    public function setViewed(bool $viewed): self
    {
        $this->viewed = $viewed;

        return $this;
    }

    /**
     * @return array
     */
    static public function getDiscriminatorMap(): array
    {
        return [
            'transaction' => 'MatGyver\Entity\Activity\ActivityTransaction',
            'support_ticket' => 'MatGyver\Entity\Activity\ActivitySupportTicket',
            'support_reply' => 'MatGyver\Entity\Activity\ActivitySupportReply',
        ];
    }

}
