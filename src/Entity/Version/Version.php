<?php

namespace MatGyver\Entity\Version;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'mg_versions')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Version\VersionRepository::class)]
class Version
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $number;

    #[ORM\Column(type: 'text')]
    private $content;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $datePublication;

    #[ORM\OneToMany(targetEntity: VersionTag::class, mappedBy: 'version', cascade: ['persist', 'remove'])]
    private $tags;

    public function __construct()
    {
        $this->tags = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(string $number): self
    {
        $this->number = $number;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDatePublication(): ?\DateTimeInterface
    {
        return $this->datePublication;
    }

    public function setDatePublication(\DateTimeInterface $datePublication): self
    {
        $this->datePublication = $datePublication;

        return $this;
    }

    /**
     * @return Collection|VersionTag[]
     */
    public function getTags(): Collection
    {
        return $this->tags;
    }

    /**
     * @return VersionTag[]
     */
    public function sortTags(): array
    {
        $tags = $this->getTags()->toArray();
        if (!$tags) {
            return $tags;
        }

        usort($tags, function (VersionTag $tag, VersionTag $tag2) {
            if ($tag->getTag() == VersionTag::TAG_NEW) {
                if ($tag2->getTag() == VersionTag::TAG_NEW) {
                    return 0;
                }
                return -1;
            }
            if ($tag->getTag() == VersionTag::TAG_TWEAK) {
                if ($tag2->getTag() == VersionTag::TAG_NEW) {
                    return 1;
                }
                return 0;
            }
            if ($tag2->getTag() == VersionTag::TAG_FIX) {
                return 0;
            }
            return 1;
        });

        return $tags;
    }

    public function addTag(VersionTag $versionTag): self
    {
        if (!$this->tags->contains($versionTag)) {
            $this->tags[] = $versionTag;
            $versionTag->setVersion($this);
        }

        return $this;
    }

    public function removeTag(VersionTag $versionTag): self
    {
        if ($this->tags->removeElement($versionTag)) {
            // set the owning side to null (unless already changed)
            if ($versionTag->getVersion() === $this) {
                $versionTag->setVersion(null);
            }
        }

        return $this;
    }
}
