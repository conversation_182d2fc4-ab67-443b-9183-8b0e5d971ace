<?php

namespace MatGyver\Entity\Event;

use Doctrine\ORM\Mapping as ORM;
use MatGyver\Entity\Shop\Product\ShopProduct;

#[ORM\Table(name: 'mg_events_products')]
#[ORM\Entity(repositoryClass: \MatGyver\Repository\Event\EventProductRepository::class)]
class EventProduct
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(name: 'event_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Event\Event::class)]
    private $event;

    #[ORM\JoinColumn(name: 'product_id', referencedColumnName: 'id', nullable: false)]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getEvent(): ?Event
    {
        return $this->event;
    }

    public function setEvent(?Event $event): self
    {
        $this->event = $event;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }
}
