<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Game;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use Mat<PERSON><PERSON><PERSON>\Repository\Game\GameBagRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'game_bag')]
#[ORM\Entity(repositoryClass: GameBagRepository::class)]
class GameBag
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: GameItem::class)]
    private $item;

    #[ORM\Column(type: 'integer')]
    private $nb;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getItem(): ?GameItem
    {
        return $this->item;
    }

    public function setItem(?GameItem $item): self
    {
        $this->item = $item;

        return $this;
    }

    public function getNb(): ?int
    {
        return $this->nb;
    }

    public function setNb(int $nb): self
    {
        $this->nb = $nb;

        return $this;
    }
}
