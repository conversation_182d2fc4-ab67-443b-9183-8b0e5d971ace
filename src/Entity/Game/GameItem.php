<?php

namespace MatGyver\Entity\Game;

use Mat<PERSON><PERSON><PERSON>\Repository\Game\GameItemRepository;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Game\GameBagService;

#[ORM\Table(name: 'game_items')]
#[ORM\Entity(repositoryClass: GameItemRepository::class)]
class GameItem
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private $name;

    #[ORM\Column(type: 'string', length: 255)]
    private $image;

    #[ORM\Column(type: 'text')]
    private $description;

    #[ORM\Column(type: 'integer')]
    private $maxItems;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getMaxItems(): ?int
    {
        return $this->maxItems;
    }

    public function setMaxItems(int $maxItems): self
    {
        $this->maxItems = $maxItems;

        return $this;
    }

    /**
     * @return GameBag|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getClientItem(): ?GameBag
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(GameBagService::class)->getRepository()->findOneBy(['item' => $this]);
    }
}
