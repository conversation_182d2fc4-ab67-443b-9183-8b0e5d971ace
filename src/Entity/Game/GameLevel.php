<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Entity\Game;

use MatGy<PERSON>\Repository\Game\GameLevelRepository;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Game\GameLevelClientService;
use MatGyver\Services\Game\GameLevelService;

#[ORM\Table(name: 'game_levels')]
#[ORM\Entity(repositoryClass: GameLevelRepository::class)]
class GameLevel
{
    const STATUS_DONE = 'done';
    const STATUS_CURRENT = 'current';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer')]
    private $level;

    #[ORM\Column(type: 'integer')]
    private $points;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(int $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function setPoints(int $points): self
    {
        $this->points = $points;

        return $this;
    }

    /**
     * @return GameLevel|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getNext(): ?GameLevel
    {
        $container = ContainerBuilderService::getInstance();
        $levels = $container->get(GameLevelService::class)->getRepository()->findAll();
        $key = false;
        foreach ($levels as $id => $level) {
            if ($level->getId() == $this->getId()) {
                $key = $id;
                break;
            }
        }
        if ($key === false) {
            return null;
        }

        $key++;
        if (isset($levels[$key])) {
            return $levels[$key];
        }

        return null;
    }

    /**
     * @return GameLevelClient|null
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getClientLevel(): ?GameLevelClient
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(GameLevelClientService::class)->getRepository()->findOneBy(['level' => $this]);
    }
}
