<?php

namespace MatGyver\Entity\Game;

use MatGyver\Entity\Traits\ClientEntity;
use MatGyver\Repository\Game\GameBadgeClientRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'game_badges_clients')]
#[ORM\Entity(repositoryClass: GameBadgeClientRepository::class)]
class GameBadgeClient
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: GameBadge::class)]
    private $badge;

    #[ORM\Column(type: 'boolean')]
    private $claimed;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateClaimed;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getBadge(): ?GameBadge
    {
        return $this->badge;
    }

    public function setBadge(?GameBadge $badge): self
    {
        $this->badge = $badge;

        return $this;
    }

    public function getClaimed(): ?bool
    {
        return $this->claimed;
    }

    public function setClaimed(bool $claimed): self
    {
        $this->claimed = $claimed;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateClaimed(): ?\DateTimeInterface
    {
        return $this->dateClaimed;
    }

    public function setDateClaimed(?\DateTimeInterface $dateClaimed): self
    {
        $this->dateClaimed = $dateClaimed;

        return $this;
    }
}
