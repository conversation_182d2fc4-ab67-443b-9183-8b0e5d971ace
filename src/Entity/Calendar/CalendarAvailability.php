<?php

namespace MatGyver\Entity\Calendar;

use <PERSON><PERSON><PERSON><PERSON>\Entity\Client\Client;
use <PERSON>G<PERSON><PERSON>\Repository\Calendar\CalendarAvailabilityRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'calendars_availabilities')]
#[ORM\Entity(repositoryClass: CalendarAvailabilityRepository::class)]
class CalendarAvailability
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Calendar::class, inversedBy: 'availabilities')]
    private $calendar;

    #[ORM\Column(type: 'boolean')]
    private $recurring = false;

    #[ORM\Column(type: 'string', length: 20)]
    private $recurringDay = '';

    #[ORM\JoinColumn(nullable: true, name: 'parent', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: CalendarAvailability::class)]
    private $parent = null;

    #[ORM\Column(type: 'boolean')]
    private $hidden = false;

    #[ORM\Column(type: 'datetime')]
    private $dateStart;

    #[ORM\Column(type: 'datetime')]
    private $dateEnd;

    #[ORM\Column(type: 'date', nullable: true)]
    private $dateEndRecurring = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getCalendar(): ?Calendar
    {
        return $this->calendar;
    }

    public function setCalendar(?Calendar $calendar): self
    {
        $this->calendar = $calendar;

        return $this;
    }

    public function getRecurring(): ?bool
    {
        return $this->recurring;
    }

    public function setRecurring(bool $recurring): self
    {
        $this->recurring = $recurring;

        return $this;
    }

    public function getRecurringDay(): string
    {
        return $this->recurringDay;
    }

    public function setRecurringDay(string $recurringDay): self
    {
        $this->recurringDay = $recurringDay;

        return $this;
    }

    public function getParent(): ?CalendarAvailability
    {
        return $this->parent;
    }

    public function setParent(?CalendarAvailability $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getHidden(): bool
    {
        return $this->hidden;
    }

    public function setHidden(bool $hidden): self
    {
        $this->hidden = $hidden;

        return $this;
    }

    public function getDateStart(): ?\DateTimeInterface
    {
        return $this->dateStart;
    }

    public function setDateStart(\DateTimeInterface $dateStart): self
    {
        $this->dateStart = $dateStart;

        return $this;
    }

    public function getDateEnd(): ?\DateTimeInterface
    {
        return $this->dateEnd;
    }

    public function setDateEnd(\DateTimeInterface $dateEnd): self
    {
        $this->dateEnd = $dateEnd;

        return $this;
    }

    public function getDateEndRecurring(): ?\DateTimeInterface
    {
        return $this->dateEndRecurring;
    }

    public function setDateEndRecurring(?\DateTimeInterface $dateEndRecurring): self
    {
        $this->dateEndRecurring = $dateEndRecurring;

        return $this;
    }
}
