<?php

namespace MatGyver\Entity\Calendar;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\Conference\ConferenceRoom;
use MatGyver\Entity\Consulting\ConsultingReason;
use MatG<PERSON>ver\Entity\Dossier\Dossier;
use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Entity\User\User;
use MatGyver\Repository\Calendar\CalendarAppointmentRepository;
use Doctrine\ORM\Mapping as ORM;
use MatGyver\Services\Calendar\CalendarAppointmentService;
use MatGyver\Services\DI\ContainerBuilderService;

#[ORM\Table(name: 'calendars_appointments')]
#[ORM\Entity(repositoryClass: CalendarAppointmentRepository::class)]
class CalendarAppointment
{
    const STATUS_TO_DO = 'to_do';
    const STATUS_CANCELED = 'canceled';
    const STATUS_DONE = 'done';
    const STATUS_USER_ABSENT = 'user_absent';
    const STATUS_CLIENT_ABSENT = 'client_absent';
    const STATUS_WAITING_PAYMENT = 'waiting_payment';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Calendar::class, inversedBy: 'appointments')]
    private $calendar;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: ConsultingReason::class)]
    private $consultingReason = null;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'appointments')]
    private $user = null;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: CalendarAppointment::class)]
    private $parent = null;

    #[ORM\ManyToOne(targetEntity: Dossier::class, inversedBy: 'appointments')]
    private $dossier = null;

    #[ORM\Column(type: 'boolean')]
    private $newUser = false;

    #[ORM\Column(type: 'text')]
    private $comment = '';

    #[ORM\Column(type: 'string', length: 20)]
    private $status;

    #[ORM\Column(type: 'string')]
    private $googleCalendarEventId = '';

    #[ORM\JoinColumn(nullable: true, name: 'transaction_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Transaction\ShopTransaction::class)]
    private $transaction = null;

    #[ORM\JoinColumn(nullable: true, name: 'product_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Shop\Product\ShopProduct::class)]
    private $product = null;

    #[ORM\Column(type: 'string', length: 50)]
    private $paymentType = '';

    #[ORM\Column(type: 'text')]
    private $link = '';

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime')]
    private $dateStart;

    #[ORM\Column(type: 'datetime')]
    private $dateEnd;

    #[ORM\OneToMany(targetEntity: CalendarAppointmentHistory::class, mappedBy: 'calendarAppointment')]
    private $histories;

    #[ORM\JoinColumn(nullable: true, name: 'conference_room_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: ConferenceRoom::class, inversedBy: 'appointment')]
    private $conferenceRoom = null;

    public function __construct()
    {
        $this->histories = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id = null): self
    {
        $this->id = $id;

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getCalendar(): ?Calendar
    {
        return $this->calendar;
    }

    public function setCalendar(?Calendar $calendar): self
    {
        $this->calendar = $calendar;

        return $this;
    }

    public function getConsultingReason(): ?ConsultingReason
    {
        return $this->consultingReason;
    }

    public function setConsultingReason(?ConsultingReason $consultingReason): self
    {
        $this->consultingReason = $consultingReason;

        return $this;
    }

    public function getDossier(): ?Dossier
    {
        return $this->dossier;
    }

    public function setDossier(?Dossier $dossier): self
    {
        $this->dossier = $dossier;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getNewUser(): bool
    {
        return $this->newUser;
    }

    public function setNewUser(bool $newUser): self
    {
        $this->newUser = $newUser;

        return $this;
    }

    public function getParent(): ?CalendarAppointment
    {
        return $this->parent;
    }

    public function setParent(?CalendarAppointment $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getGoogleCalendarEventId(): string
    {
        return $this->googleCalendarEventId;
    }

    public function setGoogleCalendarEventId(string $googleCalendarEventId): self
    {
        $this->googleCalendarEventId = $googleCalendarEventId;

        return $this;
    }

    public function getTransaction(): ?ShopTransaction
    {
        return $this->transaction;
    }

    public function setTransaction(?ShopTransaction $transaction): self
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getProduct(): ?ShopProduct
    {
        return $this->product;
    }

    public function setProduct(?ShopProduct $product): self
    {
        $this->product = $product;

        return $this;
    }

    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    public function setPaymentType(string $paymentType): self
    {
        $this->paymentType = $paymentType;

        return $this;
    }

    public function getLink(): string
    {
        return $this->link;
    }

    public function setLink(string $link): self
    {
        $this->link = $link;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateStart(): ?\DateTimeInterface
    {
        return $this->dateStart;
    }

    public function setDateStart(\DateTimeInterface $dateStart): self
    {
        $this->dateStart = $dateStart;

        return $this;
    }

    public function getDateEnd(): ?\DateTimeInterface
    {
        return $this->dateEnd;
    }

    public function setDateEnd(\DateTimeInterface $dateEnd): self
    {
        $this->dateEnd = $dateEnd;

        return $this;
    }

    /**
     * @return Collection<int, CalendarAppointmentHistory>
     */
    public function getHistories(): Collection
    {
        return $this->histories;
    }

    public function addHistory(CalendarAppointmentHistory $history): self
    {
        if (!$this->histories->contains($history)) {
            $this->histories[] = $history;
            $history->setCalendarAppointment($this);
        }

        return $this;
    }

    public function removeHistory(CalendarAppointmentHistory $history): self
    {
        if ($this->histories->removeElement($history)) {
            // set the owning side to null (unless already changed)
            if ($history->getCalendarAppointment() === $this) {
                $history->setCalendarAppointment(null);
            }
        }

        return $this;
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        $label = 'success';
        switch ($this->getStatus()) {
            case self::STATUS_CANCELED:
            case self::STATUS_USER_ABSENT:
            case self::STATUS_CLIENT_ABSENT:
                $label = 'danger';
                break;
            case self::STATUS_TO_DO:
            case self::STATUS_WAITING_PAYMENT:
                $label = 'info';
                break;
        }
        return $label;
    }

    /**
     * @return string
     */
    public function getFriendlyDate(): string
    {
        return friendlyDates($this->getDateStart(), $this->getDateEnd());
    }

    /**
     * @return CalendarAppointment[]
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function getChildren(): array
    {
        $container = ContainerBuilderService::getInstance();
        return $container->get(CalendarAppointmentService::class)->getRepository()->findBy(['parent' => $this]);
    }

    public function getConferenceRoom(): ?ConferenceRoom
    {
        return $this->conferenceRoom;
    }

    public function setConferenceRoom(?ConferenceRoom $conferenceRoom = null): self
    {
        $this->conferenceRoom = $conferenceRoom;

        return $this;
    }
}
