<?php

namespace MatGyver\Entity\Calendar;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use LongitudeOne\Spatial\PHP\Types\Geography\Point;
use MatGyver\Entity\Client\Client;
use MatGyver\Entity\Consulting\ConsultingReason;
use MatGyver\Entity\Integration\Account\IntegrationAccount;
use MatGyver\Entity\User\User;
use MatGyver\Helpers\Tools;
use MatGyver\Repository\Calendar\CalendarRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: 'calendars')]
#[ORM\Entity(repositoryClass: CalendarRepository::class)]
class Calendar
{
    const TYPE_ONLINE = 'online';
    const TYPE_ONLINE_ASTRAEOS = 'online_astraeos';
    const TYPE_AT_HOME = 'at_home';
    const TYPE_ON_SITE = 'on_site';
    const TYPE_PHONE = 'phone';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Client::class)]
    private $client;

    #[ORM\JoinColumn(nullable: true)]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private $user = null;

    #[ORM\Column(type: 'string', length: 20)]
    private $type;

    #[ORM\Column(type: 'boolean')]
    private $main = false;

    #[ORM\Column(type: 'string', length: 255)]
    private $title;

    #[ORM\Column(type: 'string', length: 255)]
    private $address = '';

    #[ORM\Column(type: 'string', length: 50)]
    private $zip = '';

    #[ORM\Column(type: 'string', length: 255)]
    private $city = '';

    #[ORM\Column(type: 'point')]
    private $location;

    #[ORM\Column(type: 'string', length: 2)]
    private $country = '';

    #[ORM\Column(type: 'text')]
    private $conferenceLink = '';

    #[ORM\Column(type: 'integer')]
    private $timeBetweenAppointments = 0;

    #[ORM\Column(type: 'integer')]
    private $availabilitiesDelay = 0;

    #[ORM\Column(type: 'integer')]
    private $availabilitiesMaxDays = 0;

    #[ORM\Column(type: 'string', length: 10)]
    private $color;

    #[ORM\Column(type: 'boolean')]
    private $active;

    #[ORM\OneToMany(targetEntity: CalendarAvailability::class, mappedBy: 'calendar')]
    private $availabilities;

    #[ORM\OneToMany(targetEntity: CalendarAppointment::class, mappedBy: 'calendar')]
    private $appointments;

    #[ORM\JoinTable(name: 'consulting_reason_calendar')]
    #[ORM\JoinColumn(name: 'calendar_id', referencedColumnName: 'id')]
    #[ORM\InverseJoinColumn(name: 'consulting_reason_id', referencedColumnName: 'id')]
    #[ORM\ManyToMany(targetEntity: ConsultingReason::class, mappedBy: 'calendars')]
    private $consultingReasons;

    #[ORM\JoinColumn(nullable: false, name: 'account_id', referencedColumnName: 'id', onDelete: 'SET NULL')]
    #[ORM\ManyToOne(targetEntity: \MatGyver\Entity\Integration\Account\IntegrationAccount::class)]
    private $account = null;

    #[ORM\Column(type: 'string')]
    private $googleCalendarId = '';

    #[ORM\Column(type: 'text')]
    private $googleAvailabilitiesCalendarsIds = '';

    public function __construct()
    {
        $this->availabilities = new ArrayCollection();
        $this->appointments = new ArrayCollection();
        $this->consultingReasons = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isMain(): bool
    {
        return $this->main;
    }

    public function setMain(bool $main): self
    {
        $this->main = $main;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getFullTitle(): ?string
    {
        $title = $this->title;
        if ($this->getUser()) {
            $title .= ' (' . $this->getUser()->getFirstName() . ' ' . $this->getUser()->getLastName() . ')';
        }

        return $title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getZip(): ?string
    {
        return $this->zip;
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getLocation(): ?Point
    {
        return $this->location;
    }

    public function setLocation(Point $location): self
    {
        $this->location = $location;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getConferenceLink(): ?string
    {
        return $this->conferenceLink;
    }

    public function setConferenceLink(string $conferenceLink): self
    {
        $this->conferenceLink = $conferenceLink;

        return $this;
    }

    public function getTimeBetweenAppointments(): ?int
    {
        return $this->timeBetweenAppointments;
    }

    public function setTimeBetweenAppointments(int $timeBetweenAppointments): self
    {
        $this->timeBetweenAppointments = $timeBetweenAppointments;

        return $this;
    }

    public function getAvailabilitiesDelay(): ?int
    {
        return $this->availabilitiesDelay;
    }

    public function setAvailabilitiesDelay(int $availabilitiesDelay): self
    {
        $this->availabilitiesDelay = $availabilitiesDelay;

        return $this;
    }

    public function getAvailabilitiesMaxDays(): ?int
    {
        return $this->availabilitiesMaxDays;
    }

    public function setAvailabilitiesMaxDays(int $availabilitiesMaxDays): self
    {
        $this->availabilitiesMaxDays = $availabilitiesMaxDays;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getAccount(): ?IntegrationAccount
    {
        return $this->account;
    }

    public function setAccount(?IntegrationAccount $account = null): self
    {
        $this->account = $account;

        return $this;
    }

    public function getGoogleCalendarId(): string
    {
        return $this->googleCalendarId;
    }

    public function setGoogleCalendarId(string $googleCalendarId): self
    {
        $this->googleCalendarId = $googleCalendarId;

        return $this;
    }

    public function getGoogleAvailabilitiesCalendarsIds(): array
    {
        return ($this->googleAvailabilitiesCalendarsIds ? json_decode($this->googleAvailabilitiesCalendarsIds, true) : []);
    }

    public function setGoogleAvailabilitiesCalendarsIds(array $googleAvailabilitiesCalendarsIds): self
    {
        $this->googleAvailabilitiesCalendarsIds = json_encode($googleAvailabilitiesCalendarsIds);

        return $this;
    }

    /**
     * @return Collection<int, CalendarAvailability>
     */
    public function getAvailabilities(): Collection
    {
        return $this->availabilities;
    }

    public function addAvailability(CalendarAvailability $availability): self
    {
        if (!$this->availabilities->contains($availability)) {
            $this->availabilities[] = $availability;
            $availability->setCalendar($this);
        }

        return $this;
    }

    public function removeAvailability(CalendarAvailability $availability): self
    {
        if ($this->availabilities->removeElement($availability)) {
            // set the owning side to null (unless already changed)
            if ($availability->getCalendar() === $this) {
                $availability->setCalendar(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CalendarAppointment>
     */
    public function getAppointments(): Collection
    {
        return $this->appointments;
    }

    public function addAppointment(CalendarAppointment $appointment): self
    {
        if (!$this->appointments->contains($appointment)) {
            $this->appointments[] = $appointment;
            $appointment->setCalendar($this);
        }

        return $this;
    }

    public function removeAppointment(CalendarAppointment $appointment): self
    {
        if ($this->appointments->removeElement($appointment)) {
            // set the owning side to null (unless already changed)
            if ($appointment->getCalendar() === $this) {
                $appointment->setCalendar(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ConsultingReason>
     */
    public function getConsultingReasons(): Collection
    {
        return $this->consultingReasons;
    }

    public function addConsultingReason(ConsultingReason $consultingReason): self
    {
        if (!$this->consultingReasons->contains($consultingReason)) {
            $this->consultingReasons[] = $consultingReason;
            $consultingReason->addCalendar($this);
        }

        return $this;
    }

    public function removeConsultingReason(ConsultingReason $consultingReason): self
    {
        if ($this->consultingReasons->removeElement($consultingReason)) {
            $consultingReason->removeCalendar($this);
        }

        return $this;
    }

    /**
     * @param bool $addPrivateInfos
     * @return string
     */
    public function getPlace(bool $addPrivateInfos = false): string
    {
        if ($this->getType() == self::TYPE_AT_HOME) {
            return __('A domicile');
        }
        if ($this->getType() == self::TYPE_PHONE) {
            return __('Par téléphone');
        }
        if ($this->getType() == self::TYPE_ONLINE) {
            $content = __('En visioconférence');
            if ($addPrivateInfos and $this->getConferenceLink()) {
                $content .= ' (<a href="' . $this->getConferenceLink() . '">' . $this->getConferenceLink() . '</a>)';
            }
            return $content;
        }
        if ($this->getType() == self::TYPE_ONLINE_ASTRAEOS) {
            return __('En visioconférence via %s', APP_NAME);
        }
        return Tools::renderCalendarAddress($this, ', ');
    }
}
