<?php

namespace Mat<PERSON>yver\FormsFactory\Shop;

use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatG<PERSON>ver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopTransactionPaymentForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use Symfony\Component\Validator\Constraints\DateTime;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Positive;

class ShopTransactionPaymentFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopTransactionPaymentForm::class);

        $field = new BuilderFormField('amount');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Montant reçu'));
        $field->setData([
            'input_type' => 'float',
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY),
        ]);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer le montant reçu')]));
        $field->addValidation(new Positive(['message' => __('Veuillez indiquer le montant reçu')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date');
        $field->setType(FieldsEnum::TYPE_DATETIME);
        $field->setLabel(__('Date'));
        $field->setValue(date('Y-m-d H:i:s'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer la date')]));
        $field->addValidation(new DateTime(['message' => __('Veuillez indiquer une date valide')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Commentaire'));
        $field->setHelp(__('Visible uniquement par les administrateurs'));
        $this->builderFields->addField($field);


        $this->setTitle(__('Ajout d\'un paiement'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'transactions'));
    }

    /**
     * @param ShopTransaction|null $transaction
     * @throws \Exception
     */
    public function preRenderFields(?ShopTransaction $transaction)
    {
        if (!$transaction) {
            throw new \Exception(__('Impossible d\'ajouter un paiement à cette transaction'));
        }

        $this->setHeaderText(Transaction::getTransactionInfos($transaction));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'transaction/' . $transaction->getReference()));
        $this->addHiddenFields('redirect', Tools::makeLink('admin', 'shop', 'transaction/' . $transaction->getReference()));
        $this->addHiddenFields('reference', $transaction->getReference());

        $field = $this->builderFields->getField('amount');
        $field->addData('addon', Currency::displayCurrency($transaction->getCurrency()));
    }
}
