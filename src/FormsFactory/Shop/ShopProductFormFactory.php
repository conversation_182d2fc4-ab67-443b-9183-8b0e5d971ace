<?php

namespace MatGyver\FormsFactory\Shop;

use MatGyver\Entity\Shop\Product\ShopProduct;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopProductForm;
use Mat<PERSON>yver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormContentFunction;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Limit\LimitService;
use MatGyver\Services\Shop\Product\ShopProductsLimitsService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\Shop\ShopVatRulesService;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopProductFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopProductForm::class);

        $this->addInfos();
        $this->addOptions();
        $this->addAmount();
        $this->addType();
        $this->addAffiliation();
        $this->addGroup();

        $this->setTitle(__('Création d\'un produit'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'products'));
    }

    /**
     * @param ShopProduct|null $shopProduct
     */
    public function preRenderFields(?ShopProduct $shopProduct)
    {
        Assets::addJs('admin/shop/product.js');

        if ($shopProduct) {
            $this->setTitle(__('Modification d\'un produit'));

            $group = $this->builderFields->getField('group_amount');
            $select = $group->getField('vat_rule_id')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam($shopProduct->getVatRule()->getId());
            }

            $select = $group->getField('currency')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam($shopProduct->getCurrency());
            }

            $fieldData = $group->getField('price_tax_excl')->getData();
            $fieldData['addon'] = Currency::displayCurrency($shopProduct->getCurrency());
            $group->updateField('price_tax_excl', 'data', $fieldData);

            $fieldData = $group->getField('price_tax_incl')->getData();
            $fieldData['addon'] = Currency::displayCurrency($shopProduct->getCurrency());
            $group->updateField('price_tax_incl', 'data', $fieldData);


            $group = $this->builderFields->getField('group_type');
            $select = $group->getField('type')->getSelect();
            if ($select instanceof BuilderFormSelect) {
                $select->setParam($shopProduct->getType());
            }

            $renderLimits = $group->getField('limits')->getContentFunction();
            if ($renderLimits instanceof BuilderFormContentFunction) {
                $renderLimits->setClass(ShopProductsLimitsService::class);
                $renderLimits->setFunction('renderProductLimits');
                $renderLimits->setParam($shopProduct);
            }

            $productDuration = explode(' ', $shopProduct->getDuration());
            $group->updateField('duration_nb', 'value', $productDuration[0]);
            $select = $group->getField('duration_type')->getSelect();
            if ($select instanceof BuilderFormSelect and isset($productDuration[1])) {
                $select->setValue($productDuration[1]);
            }

            $quantity = json_decode($shopProduct->getQuantities(), true);
            $group = $this->builderFields->getField('group');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('quantity', 'value', (isset($quantity['active']) and $quantity['active']));
                $group->updateField('quantity_min', 'value', ($quantity['min'] ?? 0));
                $group->updateField('quantity_max', 'value', ($quantity['max'] ?? 0));
                $group->updateField('in_stock', 'value', ($quantity['in_stock'] ?? ''));
            }
        }
    }

    private function addInfos()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du produit'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom du produit')]));
        $field->setData(['target' => 'permalink']);
        $field->setStep(1);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('permalink');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Lien du produit'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le lien du produit')]));
        $field->setData([
            'input_type' => 'text',
            'pre-addon' => APP_URL . '/product/',
        ]);
        $field->setStep(1);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('image');
        $field->setType(FieldsEnum::TYPE_IMAGE);
        $field->setLabel(__('Image'));
        $field->setStep(1);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('description');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Description'));
        $field->setStep(1);
        $this->builderFields->addField($field);
    }

    private function addOptions()
    {
        $field = new BuilderFormField('active');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Activé'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('visible');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Visible'));
        $field->setHelp(__('Cochez cette case si ce produit doit être affiché sur la page "Changer d\'abonnement" des clients'));
        $field->setData(['label_active' => __('Visible'), 'label_desactive' => __('Invisible')]);
        $this->builderFields->addField($field);
    }

    private function addAmount()
    {
        $group = new BuilderFormGroup('group_amount');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Montant'));
        $group->setOpened(true);

        $field = new BuilderFormField('price_tax_excl');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Montant HT'));
        $field->setData([
            'input_type' => 'float',
            'min' => 0,
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY),
        ]);
        $field->setStep(2);
        $group->addField($field);

        $field = new BuilderFormField('vat_rule_id');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Règle de TVA'));
        $select = new BuilderFormSelect();
        $select->setClass(ShopVatRulesService::class);
        $select->setFunction('generateSelectVatRules');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner une règle de TVA')]));
        $field->setStep(2);
        $group->addField($field);

        $field = new BuilderFormField('price_tax_incl');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Montant TTC'));
        $field->setData([
            'input_type' => 'float',
            'min' => 0,
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY),
        ]);
        $field->setStep(2);
        $group->addField($field);

        $field = new BuilderFormField('currency');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Devise'));
        $select = new BuilderFormSelect();
        $select->setClass(Currency::class);
        $select->setFunction('generateSelectCurrency');
        $select->setParam(DEFAULT_CURRENCY);
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner une devise')]));
        $field->setStep(2);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addType()
    {
        $group = new BuilderFormGroup('group_type');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Type de produit'));

        $field = new BuilderFormField('type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type'));
        $select = new BuilderFormSelect();
        $select->setClass(ShopProductsService::class);
        $select->setFunction('generateSelectProductsTypes');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un type de produit')]));
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('recurring');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Cochez cette case si ce produit doit générer un abonnement récurrent lorsqu\'il est commandé par un client depuis son espace'));
        $group->addField($field);

        $field = new BuilderFormField('duration_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="row"><div class="col-lg-6">');
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('duration_nb');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Durée d\'accès à la plateforme'));
        $field->setValue(30);
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('duration_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div><div class="col-lg-6">');
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('duration_type');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Type'));
        $select = new BuilderFormSelect();
        $select->setOptions([
            ['value' => 'day', 'label' => __('Jours')],
            ['value' => 'month', 'label' => __('Mois')],
        ]);
        $field->setSelect($select);
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('duration_content3');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div></div>');
        $field->setStep(3);
        $group->addField($field);

        $field = new BuilderFormField('editable');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Cochez cette case si cet abonnement peut être modifié à tout moment par le client. Le client pourra ainsi modifier les limites de son abonnement.'));
        $group->addField($field);

        $field = new BuilderFormField('cancellable');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Cochez cette case si cet abonnement peut être annulé à tout moment par le client. Cela ne désactivera pas son compte, uniquement l\'accès aux fonctionnalités apportées par cet abonnement.'));
        $field->setHelp(__('Cette option ne doit pas être utilisée pour l\'abonnement principal du client.'));
        $group->addField($field);

        $field = new BuilderFormField('limits');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setLabel('<hr><h3>' . __('Limites') . '</h3>');
        $contentFunction = new BuilderFormContentFunction();
        $contentFunction->setClass(LimitService::class);
        $contentFunction->setFunction('renderLimits');
        $field->setContentFunction($contentFunction);
        $field->setStep(3);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addAffiliation()
    {
        $group = new BuilderFormGroup('group_affiliation');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Affiliation'));

        $field = new BuilderFormField('affiliation');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Cochez cette case si ce produit doit générer des commissions pour les affiliés'));
        $group->addField($field);

        $field = new BuilderFormField('affiliation_commission');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Commission'));
        $field->setData([
            'input_type' => 'float',
            'addon' => '%'
        ]);
        $group->addField($field);

        $field = new BuilderFormField('affiliation_commission2');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Commission (2ème niveau)'));
        $field->setData([
            'input_type' => 'float',
            'addon' => '%'
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addGroup()
    {
        $group = new BuilderFormGroup('group');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Options supplémentaires'));

        $field = new BuilderFormField('display_ht');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Afficher le montant du produit en HT au lieu de TTC'));
        $group->addField($field);

        $field = new BuilderFormField('quantity');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Activez cette option pour laisser la possibilité à vos visiteurs de choisir la quantité de produit désiré.'));
        $group->addField($field);

        $field = new BuilderFormField('quantity_min');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Quantité minimum'));
        $group->addField($field);

        $field = new BuilderFormField('quantity_max');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Quantité maximum'));
        $group->addField($field);

        $field = new BuilderFormField('in_stock');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Quantité en stock'));
        $field->setHelp(__('Laissez vide pour ne pas activer la gestion de stock.'));
        $group->addField($field);

        $field = new BuilderFormField('validate_url');
        $field->setType(FieldsEnum::TYPE_URL);
        $field->setLabel(__('Redirection après un paiement validé'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

}
