<?php

namespace MatGyver\FormsFactory\Shop;

use MatGyver\Entity\Shop\Transaction\ShopTransaction;
use MatGyver\Enums\ConfigEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopTransactionForm;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Tools;
use MatGyver\Helpers\Transaction;
use MatGyver\Services\Affiliation\AffiliationPartnersService;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\RightsService;
use MatGyver\Services\TwigService;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopTransactionFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopTransactionForm::class);

        $this->addTransactionInfos();
        $this->addProducts();
        $this->addSendInvoice();
        $this->addAddressGroup();
        $this->addPartner();

        $this->setTitle(__('Création d\'une transaction'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'transactions'));
    }

    /**
     * @param ShopTransaction|null $transaction
     */
    public function preRenderFields(?ShopTransaction $transaction)
    {
        Assets::addJs('admin/shop/transaction_add.js');

        $products = [];
        if (isset($_POST['products'])) {
            $products = filter_input(INPUT_POST, 'products', FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        }

        if ($transaction) {
            $this->setTitle(__('Modification d\'une transaction'));

            $customer = $transaction->getShopCustomer();
            $group = $this->builderFields->getField('infos');
            if ($customer and $group instanceof BuilderFormGroup) {
                $select = $group->getField('country')->getSelect();
                if ($select instanceof BuilderFormSelect) {
                    $select->setParam($customer->getCountry());
                }

                $group->updateField('first_name', 'value', $customer->getFirstName());
                $group->updateField('last_name', 'value', $customer->getLastName());
                $group->updateField('email', 'value', $customer->getEmail());
                $group->updateField('address', 'value', $customer->getAddress());
                $group->updateField('address2', 'value', $customer->getAddress2());
                $group->updateField('zip', 'value', $customer->getZip());
                $group->updateField('city', 'value', $customer->getCity());
                $group->updateField('telephone', 'value', $customer->getTelephone());
                $group->updateField('state', 'value', $customer->getState());

                $customerDatas = \GuzzleHttp\json_decode($customer->getDatas(), true);
                if (isset($customerDatas['company']) and $customerDatas['company']) {
                    $group->updateField('company', 'value', $customerDatas['company']);
                }
                if (isset($customerDatas['tva_intracom']) and $customerDatas['tva_intracom']) {
                    $group->updateField('tva_intracom', 'value', $customerDatas['tva_intracom']);
                }
            }

            if (!$products) {
                $transactionProducts = $transaction->getTransactionProducts();
                if ($transactionProducts) {
                    foreach ($transactionProducts as $transactionProduct) {
                        $products[] = [
                            'quantity' => $transactionProduct->getQuantity(),
                            'name' => $transactionProduct->getName(),
                            'price_tax_excl' => $transactionProduct->getPriceTaxExcl(),
                            'vat' => $transactionProduct->getVat(),
                            'price_tax_incl' => $transactionProduct->getPriceTaxIncl(),
                        ];
                    }
                }
            }

            $field = $this->builderFields->getField('send_email');
            if ($field) {
                $this->builderFields->removeField('send_email');
            }
        }

        if ($products) {
            $content = $this->getProductsContent($products);
            $group = $this->builderFields->getField('products_group');
            if ($group instanceof BuilderFormGroup) {
                $group->updateField('products', 'content', $content);
            }
        }
    }

    private function addTransactionInfos()
    {
        $field = new BuilderFormField('reference');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de transaction'));
        $field->setHelp(__('Indiquez ici le numéro de transaction (Paypal, Stripe, etc.), numéro du chèque ou du virement, etc.'));
        $field->setValue(Transaction::generateTransactionReference());
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date');
        $field->setType(FieldsEnum::TYPE_DATETIME);
        $field->setLabel(__('Date'));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('payment_method');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Mode de règlement'));
        $select = new BuilderFormSelect();
        $select->setClass(Transaction::class);
        $select->setFunction('generateSelectPaymentMethod');
        $field->setSelect($select);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Statut'));
        $select = new BuilderFormSelect();
        $select->setClass(Transaction::class);
        $select->setFunction('generateSelectStatus');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le statut de la transaction')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('currency');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Devise'));
        $select = new BuilderFormSelect();
        $select->setClass(Currency::class);
        $select->setFunction('generateSelectCurrency');
        $select->setParam(DEFAULT_CURRENCY);
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner une devise')]));
        $this->builderFields->addField($field);
    }

    private function addProducts()
    {
        $group = new BuilderFormGroup('products_group');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Produits'));
        $group->setOpened(true);

        $field = new BuilderFormField('products');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($this->getProductsContent());
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez ajouter un ou plusieurs produits')]));
        $group->addField($field);
        $this->builderFields->addField($field);
    }

    private function addSendInvoice()
    {
        $container = ContainerBuilderService::getInstance();
        $settings = [];
        $settingsInvoice = $container->get(ConfigService::class)->findByName(ConfigEnum::INVOICE);
        if ($settingsInvoice) {
            $settings = json_decode($settingsInvoice->getValue(), true);
        }

        if ($settings and isset($settings['invoice_email']) and $settings['invoice_email']) {
            $field = new BuilderFormField('send_email');
            $field->setType(FieldsEnum::TYPE_SWITCH);
            $field->setLabel(__('Envoyer la facture par email au client.'));
            $this->builderFields->addField($field);
        }
    }

    /**
     * @param array $products
     * @return string
     */
    private function getProductsContent(array $products = []): string
    {
        return TwigService::getInstance()->set('products', $products)
            ->render('forms/admin/shop/products-table.php');
    }

    private function addAddressGroup()
    {
        $group = new BuilderFormGroup('infos');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Informations du client'));
        $group->setOpened(true);

        if ($_SESSION['controller'] == ADMIN_URL and RightsService::hasAccess(UNIVERSE_ADMIN_SAV)) {
            $content = '<div class="client_select_content">';
            $field = new BuilderFormField('client_select_content1');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);

            $options = [];
            $options[] = ['value' => '', 'label' => __('Sélectionnez un client')];
            $container = ContainerBuilderService::getInstance();
            $clients = $container->get(ClientsService::class)->getRepository()->findBy([], ['name' => 'ASC']);
            foreach ($clients as $client) {
                if ($client->getId() === CLIENT_MASTER) {
                    continue;
                }
                $options[] = ['value' => $client->getId(), 'label' => $client->getName()];
            }
            $field = new BuilderFormField('client_id');
            $field->setType(FieldsEnum::TYPE_SELECT);
            $field->setLabel(__('Sélectionnez un client'));
            $select = new BuilderFormSelect();
            $select->setOptions($options);
            $field->setSelect($select);
            $group->addField($field);

            $field = new BuilderFormField('client_select_content2');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('</div>');
            $group->addField($field);
        }

        $userFields = $this->getUserInfosFields();
        foreach ($userFields as $userField) {
            $group->addField($userField);
        }

        $addressFields = $this->getAddressFields();
        foreach ($addressFields as $field) {
            $group->addField($field);
        }

        $field = new BuilderFormField('company');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Société'));
        $group->addField($field);

        $field = new BuilderFormField('tva_intracom');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro de TVA intracommunautaire'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addPartner()
    {
        $group = new BuilderFormGroup('affiliation');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Affiliation'));
        $group->setOpened(false);

        $field = new BuilderFormField('id_partner');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Affilié'));
        $select = new BuilderFormSelect();
        $select->setClass(AffiliationPartnersService::class);
        $select->setFunction('generateSelectPartners');
        $field->setSelect($select);
        $group->addField($field);

        $field = new BuilderFormField('commission');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Commission'));
        $field->setData([
            'input_type' => 'float',
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY)
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
