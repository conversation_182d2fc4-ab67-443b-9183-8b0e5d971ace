<?php

namespace MatGyver\FormsFactory\Shop;

use MatG<PERSON>ver\Entity\Country\Country;
use MatGyver\Entity\Shop\VatRule\ShopVatRule;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Shop\ShopVatRuleForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\CountriesService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\ShopVatRulesService;
use Symfony\Component\Validator\Constraints\NotBlank;

class ShopVatRuleFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(ShopVatRuleForm::class);

        $container = ContainerBuilderService::getInstance();
        $countries = $container->get(CountriesService::class)->getCountries();
        $vatRatesEU = $container->get(ShopVatRulesService::class)->getVatRates();

        $defaultVatScript = '
        <script type="text/javascript">
            let defaultVAT = ' . json_encode($vatRatesEU) . ';
        </script>';
        Assets::addInlineJs($defaultVatScript);

        $this->addName();
        $this->addEUCountries($countries, $vatRatesEU);
        $this->addOtherCountries($countries, $vatRatesEU);

        $this->setTitle(__('Création d\'une règle de TVA'));
        $this->setCancelUrl(Tools::makeLink('admin', 'shop', 'vat_rules'));
    }

    /**
     * @param ShopVatRule|null $rule
     */
    public function preRenderFields(?ShopVatRule $rule)
    {
        if ($rule) {
            $this->setTitle(__('Modification d\'une règle de TVA'));

            $vatRuleCountries = json_decode($rule->getCountries(), true);

            $container = ContainerBuilderService::getInstance();
            $countries = $container->get(CountriesService::class)->getCountries();
            $vatRatesEU = $container->get(ShopVatRulesService::class)->getVatRates();
            $this->addEUCountries($countries, $vatRatesEU, $vatRuleCountries);
            $this->addOtherCountries($countries, $vatRatesEU, $vatRuleCountries);
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer un nom')]));
        $this->builderFields->addField($field);
    }

    /**
     * @param Country[] $countries
     * @param array $vatRatesEU
     * @param array $vatRuleCountries
     * @return void
     */
    private function addEUCountries(array $countries, array $vatRatesEU, array $vatRuleCountries = [])
    {
        $group = new BuilderFormGroup('group_eu_countries');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Pays de l\'Union Européenne'));
        $group->setOpened(true);

        $content = '
        <div id="setDefaultVATUE">
            <label class="control-label" for="update_tva_ue">' . __('Définir les taux de TVA par défaut pour tous les pays de l\'Union Européenne') . '</label>
            <div class="form-group">
                <a class="btn btn-secondary" onclick="setDefaultTVAUE(); return false;">' . __('Cliquez ici pour définir les taux de TVA par défaut') . '</a>
            </div>
        </div>

        <div id="setVATUE">
            <label class="control-label" for="update_tva_ue">' . __('Définir un taux de TVA identique pour tous les pays de l\'Union Européenne') . '</label>
            <div class="form-group">
                <div class="input-group" style="width: 200px">
                    <div class="input-group-prepend">
                        <span class="input-group-text">%</span>
                    </div>
                    <input class="form-control input-small" type="number" step="any" min="0" max="100" name="update_tva_ue" id="update_tva_ue" value="20">
                    <div class="input-group-append">
                        <button class="btn btn-secondary" type="button" onclick="setTVAUE(); return false;">' . __('Valider') . '</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="setVATUE-confirmation" style="display: none; margin-top:20px">
            <div class="alert alert-custom alert-light-info">' . __('Les taux de TVA ont bien été modifiés, cliquez maintenant sur "Etape suivante" pour enregistrer les modifications.') . '</div>
        </div>';

        $field = new BuilderFormField('eu_countries_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);


        $content = '
        <table class="table table-separate table-head-custom" style="margin-top:20px">
            <thead>
                <tr>
                    <th>' . __('Pays') . '</th>
                    <th>' . __('Taux de TVA') . '</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($countries as $country) {
            if (!isset($vatRatesEU[$country->getCode()])) {
                continue;
            }

            $vatRate = $vatRatesEU[$country->getCode()];
            if (isset($vatRuleCountries[$country->getCode()])) {
                $vatRate = $vatRuleCountries[$country->getCode()];
            }

            if (is_float($vatRate)) {
                $vatRate = number_format($vatRate, 2, '.', '');
            }

            $content .= '
                <tr>
                    <td><label class="control-label" for="pays-' . $country->getCode() . '">' . $country->getName() . ' (' . $country->getEnglishName() . ')</label></td>
                    <td>
                        <div class="form-group mb-0">
                            <div class="input-group">
                                <input class="form-control input-small tva_ue" type="number" step="any" min="0" max="100" name="countries[' . $country->getCode() . ']" id="pays-' . $country->getCode() . '" value="' . $vatRate . '">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>';
        }
        $content .= '
            </tbody>
        </table>';

        $field = new BuilderFormField('eu_countries_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    /**
     * @param Country[] $countries
     * @param array $vatRatesEU
     * @param array $vatRuleCountries
     * @return void
     */
    private function addOtherCountries(array $countries, array $vatRatesEU, array $vatRuleCountries = [])
    {
        $group = new BuilderFormGroup('group_other_countries');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Autres pays'));
        $group->setOpened(false);

        $content = '
        <div id="setVAT">
            <label class="control-label" for="update_tva_hors_ue">' . __('Définir un taux de TVA identique pour tous les pays hors Union Européenne') . '</label>
            <div class="form-group">
                <div class="input-group" style="width: 200px">
                    <div class="input-group-prepend">
                        <span class="input-group-text">%</span>
                    </div>
                    <input class="form-control input-small" type="number" step="any" min="0" max="100" name="update_tva_hors_ue" id="update_tva_hors_ue" value="20">
                    <div class="input-group-append">
                        <button class="btn btn-secondary" type="button" onclick="setTVAHorsUE(); return false;">' . __('Valider') . '</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="setVAT-confirmation" style="display: none; margin-top:20px">
            <div class="alert alert-custom alert-light-info">' . __('Les taux de TVA ont bien été modifiés, cliquez maintenant sur "Valider" pour enregistrer les modifications.') . '</div>
        </div>';

        $field = new BuilderFormField('other_countries_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);


        $content = '
        <table class="table table-separate table-head-custom" style="margin-top:20px">
            <thead>
                <tr>
                    <th>' . __('Pays') . '</th>
                    <th>' . __('Taux de TVA') . '</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($countries as $country) {
            if (isset($vatRatesEU[$country->getCode()])) {
                continue;
            }

            $vatRate = 0;
            if (isset($vatRuleCountries[$country->getCode()])) {
                $vatRate = $vatRuleCountries[$country->getCode()];
            }

            if (is_float($vatRate)) {
                $vatRate = number_format($vatRate, 2, '.', '');
            }

            $content .= '
            <tr>
                <td><label class="control-label" for="pays-' . $country->getCode() . '">' . $country->getName() . ' (' . $country->getEnglishName() . ')</label></td>
                <td>
                    <div class="form-group mb-0">
                        <div class="input-group">
                            <input class="form-control input-small tva_hors_ue" type="number" step="any" min="0" max="100" name="countries[' . $country->getCode() . ']" id="pays-' . $country->getCode() . '" value="' . $vatRate . '">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>';
        }
        $content .= '
            </tbody>
        </table>';

        $field = new BuilderFormField('other_countries_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
