<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Enums\FieldsEnum;
use MatGyver\Entity\Dossier\DossierBudget;
use MatGyver\Forms\Dossier\DossierBudgetForm;
use Mat<PERSON>yver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Currency;
use MatGyver\Helpers\Iconify;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierBudgetService;

class DossierBudgetFormFactory extends AbstractFormFactory
{
    public function __construct(?string $type = null, ?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierBudgetForm::class);

        $this->addNature($type);
        $this->addAnnualAmount($type);
        if ($type == DossierBudget::TYPE_REVENUS) {
            $this->addBeneficiary();
        }
        $this->addComment();

        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'budgets/' . $type . '/' . $dossier->getId()));
            if ($_SESSION['controller'] == 'site') {
                $this->setCancelUrl(Tools::makeLink('site', 'dossier', 'budgets/' . $type . '/' . $dossier->getReference()));
            }

            $this->setTitle(DossierBudgetService::getTexts($type)['create']);
        }
        if ($type) {
            $this->addHiddenFields('type', $type);
        }
    }

    /**
     * @param DossierBudget|null $dossierBudget
     */
    public function preRenderFields(?DossierBudget $dossierBudget)
    {
        if ($dossierBudget) {
            $this->setTitle(DossierBudgetService::getTexts($dossierBudget->getType())['edit']);
        } else {
            $this->builderFields->updateField('beneficiary', 'value', DossierBudget::YOU);
        }
    }

    private function addNature(?string $type = null)
    {
        $grid = new BuilderFormGroup('grid_nature', true);

        $options = [];
        if ($type) {
            $natures = DossierBudgetService::getNatures($type);
            foreach ($natures as $value => $name) {
                $options[] = ['value' => $value, 'label' => $name, 'label_class' => 'radio-bordered'];
            }
        }
        $field = new BuilderFormField('nature');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Nature'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $grid->addField($field);

        $field = new BuilderFormField('designation');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Désignation'));
        $grid->addField($field);

        $this->builderFields->addField($grid);
    }

    private function addAnnualAmount(?string $type = null)
    {
        $group = new BuilderFormGroup('group_amount');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Montant annuel'));
        $group->setIcon(Iconify::getIconMoney(true));
        $group->setOpened(true);

        $field = new BuilderFormField('annual_amount');
        $field->setType(FieldsEnum::TYPE_INPUT_GROUP);
        $field->setLabel(__('Montant annuel'));
        if ($type == DossierBudget::TYPE_REVENUS) {
            $field->setHelp(__('Montant net avant impôts'));
        }
        $field->setData([
            'input_type' => 'float',
            'addon' => Currency::displayCurrency(DEFAULT_CURRENCY),
            'size' => 'input-group-small'
        ]);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addBeneficiary()
    {
        $group = new BuilderFormGroup('group_beneficiary');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Bénéficiaire'));
        $group->setIcon(Iconify::getIcon('solar:users-group-two-rounded-bold'));
        $group->setOpened(true);

        $options = [];
        $beneficiaries = DossierBudgetService::getBeneficiaries();
        foreach ($beneficiaries as $value => $name) {
            $options[] = ['value' => $value, 'label' => $name, 'label_class' => 'radio-bordered'];
        }
        $field = new BuilderFormField('beneficiary');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Bénéficiaire'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addComment()
    {
        $group = new BuilderFormGroup('group_comment');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Commentaire'));
        $group->setIcon(Iconify::getIcon('solar:clipboard-text-bold'));
        $group->setOpened(true);

        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Commentaire'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
