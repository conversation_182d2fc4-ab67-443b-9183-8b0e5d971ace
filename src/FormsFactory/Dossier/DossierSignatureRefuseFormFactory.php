<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierPerson;
use MatGyver\Forms\Dossier\DossierSignatureRefuseForm;
use Mat<PERSON>yver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;

class DossierSignatureRefuseFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierSignatureRefuseForm::class);

        $this->addSignatureRefusedReason();

        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'signatures/' . $dossier->getId()));
        }
    }

    /**
     * @param DossierPerson|null $dossierPerson
     */
    public function preRenderFields(?DossierPerson $dossierPerson)
    {
        if (!$dossierPerson) {
            throw new \Exception('DossierPerson is null');
        }

        $this->setTitle(__('Refus de signature de %s', $dossierPerson->getRepresentativeName()));

        $this->builderFields->updateField('signature_refused_reason', 'value', $dossierPerson->getSignatureRefusedReason());
    }

    private function addSignatureRefusedReason()
    {
        $field = new BuilderFormField('signature_refused_reason');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Raison du refus de signature'));
        $field->setHelp(__('Indiquez une raison ci-dessous (facultatif)'));
        $this->builderFields->addField($field);
    }
}
