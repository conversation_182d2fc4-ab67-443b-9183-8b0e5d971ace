<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Entity\Dossier\DossierProject;
use MatGyver\Enums\DossierProjectEnum;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\DossierProjectForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Document\DocumentTemplateService;
use MatGyver\Services\Users\UsersService;

class DossierProjectFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierProjectForm::class);

        $this->addName();
        $this->addDescription();
        $this->addUsers();
        $this->addLabel();
        $this->addAttachments();

        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'projects/' . $dossier->getId()));
        }

        $this->setTitle(__('Création d\'un projet'));
    }

    public function preRenderFields(?DossierProject $dossierProject)
    {
        if ($dossierProject) {
            $this->setTitle(__('Modifier un projet'));
            $this->addStatus($dossierProject);

            if (count($dossierProject->getUsers()) > 0) {
                $users = [];
                foreach ($dossierProject->getUsers() as $user) {
                    $users[] = $user->getUser()->getId();
                }
                $select = $this->builderFields->getField('users__')->getSelect();
                $select->setValue($users);
            }
        }
    }

    private function addName()
    {
        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du projet'));
        $this->builderFields->addField($field);
    }

    private function addDescription()
    {
        $field = new BuilderFormField('description');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Description'));
        $this->builderFields->addField($field);
    }

    private function addUsers()
    {
        $container = ContainerBuilderService::getInstance();
        $users = $container->get(UsersService::class)->getRepository()->getAllSuperUsers();
        $options = [];
        foreach ($users as $user) {
            $options[] = ['value' => $user->getId(), 'label' => $user->getFirstName() . ' ' . $user->getLastName()];
        }

        $field = new BuilderFormField('users[]');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Administrateurs'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setData(['multiple' => true]);
        $this->builderFields->addField($field);
    }

    private function addLabel()
    {
        $field = new BuilderFormField('label');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Label'));
        $this->builderFields->addField($field);
    }

    private function addAttachments()
    {
        $field = new BuilderFormField('attachments');
        $field->setType(FieldsEnum::TYPE_DROPZONE);
        $field->setLabel(__('Pièces jointes'));
        $this->builderFields->addField($field);
    }

    private function addStatus(?DossierProject $dossierProject = null)
    {
        $options = [];
        $statuses = DossierProjectEnum::getAll();
        foreach ($statuses as $status => $name) {
            $options[] = ['value' => $status, 'label' => $name];
        }
        $field = new BuilderFormField('status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('État du projet'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setPlaceholder(__('Choisissez un modèle'));
        if ($dossierProject) {
            $field->setValue($dossierProject->getStatus());
        }
        $this->builderFields->addField($field);
    }
}
