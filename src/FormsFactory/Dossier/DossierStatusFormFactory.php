<?php
namespace Mat<PERSON>yver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use Mat<PERSON><PERSON>ver\Entity\Dossier\Dossier;
use MatGyver\Forms\Dossier\DossierStatusForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierStatusFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(DossierStatusForm::class);

        $this->addStatus();
        $this->addStatusClosed();
        $this->addStatusMessage();
        $this->addDateAlert();

        $this->setTitle(__('État du dossier'));
        $this->setCancelUrl(Tools::makeLink('app', 'dossiers'));
    }

    /**
     * @param Dossier|null $dossier
     */
    public function preRenderFields(?Dossier $dossier)
    {
        if (!$dossier) {
            throw new \Exception('Dossier is required');
        }

        Assets::addJs('app/dossier/status.js');

        $this->setCancelUrl(Tools::makeLink('app', 'dossier', $dossier->getId()));

        $select = $this->builderFields->getField('status')->getSelect();
        $select->setValue($dossier->getStatus());

        //if dossier is in alert, setting select_date_alert to '' will remove alert
        if ($dossier->getDateAlert() and !$dossier->getAlert()) {
            $select = $this->builderFields->getField('select_date_alert')->getSelect();
            $select->setValue('custom');
        }

        if (isset($_GET['status'])) {
            $status = filter_input(INPUT_GET, 'status', FILTER_UNSAFE_RAW);
            $select = $this->builderFields->getField('status')->getSelect();
            $select->setValue($status);
        }
    }

    private function addStatus()
    {
        $statuses = DossierService::getStatuses();
        $options = [];
        foreach ($statuses as $status => $name) {
            $options[] = ['value' => $status, 'label' => $name];
        }

        $field = new BuilderFormField('status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('État du dossier'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner l\'état du dossier')]));
        $field->setRequired(true);
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);
    }

    private function addStatusClosed()
    {
        $field = new BuilderFormField('status_closed');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div class="status_closed mb-8 d-none"><p class="text-muted mb-0">' . __('Si le dossier est archivé, il n\'apparaitra plus dans les dossiers en cours. Le client pourra toujours le consulter et accéder à ses documents pendant 15 jours. Au-delà de cette période, les documents seront compressés et ne seront plus accessibles pour le client.') . '</p></div>');
        $this->builderFields->addField($field);
    }

    private function addStatusMessage()
    {
        $field = new BuilderFormField('status_message');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Informations'));
        $field->setHelp(__('Facultatif'));
        $this->builderFields->addField($field);
    }

    private function addDateAlert()
    {
        $options = [
            ['value' => '', 'label' => __('Ne pas prévoir de rappel')],
            ['value' => 'custom', 'label' => __('Date personnalisée')],
            ['value' => '1day', 'label' => __('Demain')],
            ['value' => '2days', 'label' => __('Dans 2 jours')],
            ['value' => '3days', 'label' => __('Dans 3 jours')],
            ['value' => '5days', 'label' => __('Dans 5 jours')],
            ['value' => '1week', 'label' => __('Dans 1 semaine')],
            ['value' => '2weeks', 'label' => __('Dans 2 semaines')],
            ['value' => 'next_monday', 'label' => __('Lundi prochain')],
        ];
        $field = new BuilderFormField('select_date_alert');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Prévoir un rappel'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('date_alert');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date de rappel'));
        $this->builderFields->addField($field);
    }
}
