<?php
namespace <PERSON><PERSON>yver\FormsFactory\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierPerson;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\Dossier\DossierPersonForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\FormsFactory\Builder\BuilderFormSelect;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Country;
use MatGyver\Helpers\Iconify;
use MatGyver\Helpers\Nationality;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Dossier\DossierContactService;
use MatGyver\Services\Dossier\DossierPersonService;
use Symfony\Component\Validator\Constraints\NotBlank;

class DossierPersonFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(DossierPersonForm::class);

        $this->addType();
        $this->builderFields->addSeparator();
        $this->addName();
        $this->addChild();
        $this->addAddress();
        $this->addBirth();
        $this->addOther();

        $this->setTitle(__('Ajout d\'une personne'));
        $this->setIcon(Iconify::getIcon('solar:user-id-bold'));
        if ($dossier) {
            $this->addHiddenFields('dossier_id', $dossier->getId());
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'persons/' . $dossier->getId()));
            if ($_SESSION['controller'] == 'site') {
                $this->setCancelUrl(Tools::makeLink('site', 'dossier', 'persons/' . $dossier->getReference()));
            }
        }
    }

    /**
     * @param DossierPerson|null $dossierPerson
     */
    public function preRenderFields(?DossierPerson $dossierPerson)
    {
        Assets::addJs('app/dossier/person.js');
        if ($dossierPerson) {
            $this->setTitle(__('Modification d\'une personne'));

            if ($dossierPerson->getType() == DossierPerson::TYPE_CONTACT) {
                $this->builderFields->removeField('type');
                $this->addHiddenFields('type', DossierPerson::TYPE_CONTACT);
            }

            $group = $this->builderFields->getField('group_birth');
            if ($dossierPerson->getBirthDate()) {
                $group->updateField('birth_date', 'value', $dossierPerson->getBirthDate()->format('d/m/Y'));
            }
            if ($dossierPerson->getCapable()) {
                $group->updateField('capable', 'value', 'valid');
            } else {
                $group->updateField('capable', 'value', 'invalid');
            }

            $group = $this->builderFields->getField('group_child');
            if ($dossierPerson->getDependentChild()) {
                $group->updateField('dependent_child', 'value', 'yes');
            } else {
                $group->updateField('dependent_child', 'value', 'no');
            }

            $group = $this->builderFields->getField('group_other');
            if ($dossierPerson->getAdditionalProvisions()) {
                $group->updateField('additional_provisions', 'value', 'valid');
            } else {
                $group->updateField('additional_provisions', 'value', 'invalid');
            }
        }
    }

    private function addType()
    {
        $types = DossierPersonService::getTypes();
        $options = [];
        foreach ($types as $type => $name) {
            if ($type == DossierPerson::TYPE_CONTACT) {
                continue;
            }
            $options[] = ['value' => $type, 'label' => $name, 'label_class' => 'radio-bordered'];
        }

        $field = new BuilderFormField('type');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Type de personne'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner le type de personne')]));
        $field->setRequired(true);
        $field->setOptions($options);
        $field->setData(['display' => 'group']);
        $this->builderFields->addField($field);
    }

    private function addChild()
    {
        $group = new BuilderFormGroup('group_child');
        $group->setLabel(__('Informations de l\'enfant'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setClass('d-none');
        $group->setIcon(Iconify::getIcon('solar:user-hand-up-bold'));
        $group->setOpened(true);

        $options = [
            ['value' => 'yes', 'label' => __('Oui'), 'label_class' => 'radio-bordered'],
            ['value' => 'no', 'label' => __('Non'), 'label_class' => 'radio-bordered'],
        ];
        $field = new BuilderFormField('dependent_child');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Enfant à charge'));
        $field->setOptions($options);
        $field->setData(['display' => 'group']);
        $group->addField($field);

        $options = [];
        $childrenOf = DossierPersonService::getChildrenOf();
        foreach ($childrenOf as $childOf => $name) {
            $options[] = ['value' => $childOf, 'label' => $name];
        }
        $field = new BuilderFormField('child_of');
        $field->setType(FieldsEnum::TYPE_RADIO);
        $field->setLabel(__('Enfant de'));
        $field->setOptions($options);
        $field->setData(['display' => 'group']);
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addName()
    {
        $options = [];
        $civilities = DossierPersonService::getCivilities();
        foreach ($civilities as $civility => $name) {
            $options[] = ['value' => $civility, 'label' => $name, 'label_class' => 'radio-bordered'];
        }
        $field = new BuilderFormField('civility');
        $field->setType(FieldsEnum::TYPE_RADIO);
        //$field->setLabel(__('Civilité'));
        $field->setOptions($options);
        $field->setData(['display' => 'group']);
        $this->builderFields->addField($field);

        $this->builderFields->addSeparator();

        $group = new BuilderFormGroup('group_names', true);

        $field = new BuilderFormField('last_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom'));
        $group->addField($field);

        $field = new BuilderFormField('first_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Prénom'));
        $group->addField($field);

        $field = new BuilderFormField('email');
        $field->setType(FieldsEnum::TYPE_EMAIL);
        $field->setLabel(__('Adresse email'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }

    private function addAddress()
    {
        $group = new BuilderFormGroup('group_address');
        $group->setLabel(__('Adresse postale'));
        $group->setIcon(Iconify::getIcon('solar:home-angle-bold'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened(true);

        $grid = new BuilderFormGroup('group_address', true);

        $field = new BuilderFormField('address');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Adresse postale'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer l\'adresse postale')]));
        $field->setData([
            'targets' => [
                'zip' => 'zip',
                'city' => 'city'
            ],
        ]);
        $field->setClass('input-group-address');
        $grid->addField($field);

        $field = new BuilderFormField('address2');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Adresse postale (ligne 2)'));
        $grid->addField($field);

        $group->addField($grid);

        $grid = new BuilderFormGroup('group_city', true);

        $field = new BuilderFormField('zip');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Code postal'));
        $grid->addField($field);

        $field = new BuilderFormField('city');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Ville'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer la ville')]));
        $grid->addField($field);

        $field = new BuilderFormField('country');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Pays'));
        $select = new BuilderFormSelect();
        $select->setClass(Country::class);
        $select->setFunction('generateSelectCountry');
        $field->setSelect($select);
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un pays')]));
        $grid->addField($field);

        $group->addField($grid);

        $group->addSeparator();

        $grid = new BuilderFormGroup('group_company', true);

        $field = new BuilderFormField('company');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Société'));
        $grid->addField($field);

        $field = new BuilderFormField('siren');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Numéro SIREN'));
        $grid->addField($field);

        $field = new BuilderFormField('telephone');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Téléphone'));
        $grid->addField($field);

        $group->addField($grid);

        $this->builderFields->addField($group);
    }

    private function addBirth()
    {
        $group = new BuilderFormGroup('group_birth');
        $group->setLabel(__('Naissance'));
        $group->setIcon(Iconify::getIconCalendar(true));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setOpened(true);

        $grid = new BuilderFormGroup('group_birth1', true);
        $grid->setClass('grid-auto');

        $field = new BuilderFormField('birth_date');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date de naissance'));
        $grid->addField($field);

        $field = new BuilderFormField('birth_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom de naissance'));
        $grid->addField($field);

        $group->addField($grid);

        $grid = new BuilderFormGroup('group_birth2', true);

        $field = new BuilderFormField('birth_place');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Lieu'));
        $grid->addField($field);

        $field = new BuilderFormField('birth_zip');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Département'));
        $grid->addField($field);

        $field = new BuilderFormField('nationality');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Nationalité'));
        $select = new BuilderFormSelect();
        $select->setClass(Nationality::class);
        $select->setFunction('generateSelectNationality');
        $field->setSelect($select);
        $field->setData(['width' => '100%']);
        $grid->addField($field);

        $group->addField($grid);

        $group->addSeparator();

        $grid = new BuilderFormGroup('group_capable', true);
        $grid->setNbFields(2);

        $field = new BuilderFormField('capable');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Majeur capable juridiquement'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setData([
            'label_active' => __('Majeur capable juridiquement'),
            'label_desactive' => __('Majeur non capable juridiquement'),
        ]);
        $field->setClass('switch-xs');
        $field->setValue('valid');
        $grid->addField($field);

        $field = new BuilderFormField('capable_content1');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('<div id="capable_content" class="d-none">');
        $grid->addField($field);

        $options = [];
        $options[] = ['value' => '', 'label' => ''];
        $capableStatuses = DossierContactService::getCapableStatuses();
        foreach ($capableStatuses as $capableStatus => $name) {
            $options[] = ['value' => $capableStatus, 'label' => $name];
        }
        $field = new BuilderFormField('capable_status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Si non, précisez'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setPlaceholder(__('Sélectionnez une option'));
        $grid->addField($field);

        $field = new BuilderFormField('capable_content2');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent('</div>');
        $grid->addField($field);


        $group->addField($grid);

        $this->builderFields->addField($group);
    }

    private function addOther()
    {
        $group = new BuilderFormGroup('group_other');
        $group->setLabel(__('Autres informations'));
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setIcon(Iconify::getIcon('solar:suitcase-bold'));
        $group->setOpened(true);

        $grid = new BuilderFormGroup('group_professional1', true);

        $options = [];
        $options[] = ['value' => '', 'label' => ''];
        $professionalStatuses = DossierContactService::getProfessionalStatuses();
        foreach ($professionalStatuses as $professionalStatus => $name) {
            $options[] = ['value' => $professionalStatus, 'label' => $name, 'label_class' => 'radio-bordered'];
        }
        $field = new BuilderFormField('professional_status');
        $field->setType(FieldsEnum::TYPE_SELECT);
        $field->setLabel(__('Statut'));
        $select = new BuilderFormSelect();
        $select->setOptions($options);
        $field->setSelect($select);
        $field->setPlaceholder(__('Sélectionnez une option'));
        $grid->addField($field);

        $field = new BuilderFormField('current_occupation');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Profession actuelle'));
        $grid->addField($field);

        $field = new BuilderFormField('company_name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Société, employeur'));
        $grid->addField($field);

        $group->addField($grid);

        $group->addSeparator();

        $grid = new BuilderFormGroup('group_professional2', true);
        $grid->setClass('grid-auto');

        $field = new BuilderFormField('company_date_entry');
        $field->setType(FieldsEnum::TYPE_DATE_MASK);
        $field->setLabel(__('Date d\'entrée dans la société'));
        $field->setClass('w-300px');
        $grid->addField($field);

        $field = new BuilderFormField('expected_retirement_age');
        $field->setType(FieldsEnum::TYPE_INT);
        $field->setLabel(__('Age prévu de départ en retraite'));
        $grid->addField($field);

        $group->addField($grid);

        $group->addSeparator();

        $field = new BuilderFormField('additional_provisions');
        $field->setType(FieldsEnum::TYPE_SWITCH);
        $field->setLabel(__('Des dispositions supplémentaires ont été prises'));
        $field->setHelp(__('Testament, clause de préciput, etc.'));
        $field->setOptions([
            'on' => 'valid',
            'off' => 'invalid',
        ]);
        $field->setClass('switch-xs');
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
