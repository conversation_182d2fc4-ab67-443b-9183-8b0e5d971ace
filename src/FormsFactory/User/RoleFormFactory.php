<?php

namespace MatGyver\FormsFactory\User;

use MatGyver\Entity\User\Role\UserRole;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\User\UserRoleForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Shop\Product\ShopProductsService;
use MatGyver\Services\TwigService;
use MatGyver\Services\UniversesService;
use Symfony\Component\Validator\Constraints\NotBlank;

class RoleFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(UserRoleForm::class);

        $field = new BuilderFormField('name');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Nom du rôle'));
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le nom du rôle')]));
        $field->setRequired(true);
        $this->builderFields->addField($field);

        $field = new BuilderFormField('universes');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setLabel(__('Applications'));
        $field->setContent($this->getUniversesContents());
        $field->addValidation(new NotBlank(['message' => __('Veuillez sélectionner un univers')]));
        $field->setRequired(true);
        $this->builderFields->addField($field);

        $this->setTitle(__('Création d\'un rôle'));
        $this->setCancelUrl(Tools::makeLink('app', 'roles'));
        if ($_SESSION['controller'] == ADMIN_URL) {
            $this->setCancelUrl(Tools::makeLink('admin', 'roles'));
        }
    }

    /**
     * @param UserRole|null $userRole
     */
    public function preRenderFields(?UserRole $userRole)
    {
        if ($userRole) {
            $this->setTitle(__('Modification d\'un rôle'));

            $this->builderFields->updateField('universes', 'content', $this->getUniversesContents($userRole));

            $field = new BuilderFormField('id');
            $field->setType(FieldsEnum::TYPE_HIDDEN);
            $field->setValue($userRole->getUserRoleId());
            $this->builderFields->addField($field);
        }
    }

    private function getUniversesContents(?UserRole $userRole = null): string
    {
        $content = $this->getUniversesContent(APPLICATION_APP, $userRole);
        if ($_SESSION['client']['id'] == CLIENT_MASTER) {
            $content .= '<hr><h3>' . __('Admin') . '</h3>';
            $content .= $this->getUniversesContent(APPLICATION_ADMIN, $userRole);
        }

        return $content;
    }

    private function getUniversesContent(int $applicationId, ?UserRole $userRole = null): string
    {
        $container = ContainerBuilderService::getInstance();

        if ($applicationId == APPLICATION_APP) {
            $universes = $container->get(UniversesService::class)->getAppUniverses();
        } else {
            $universes = $container->get(UniversesService::class)->getAdminUniverses();
        }
        $universes = array_change_key($universes, 'universe_id');


        foreach ($universes as $universeId => $universe) {
            if ($universeId == UNIVERSE_ADMIN_PRODUCTS) {
                $options = [];
                $products = $container->get(ShopProductsService::class)->getAllProducts();
                if ($products) {
                    foreach ($products as $product) {
                        $options[$product->getId()] = $product->getName();
                    }
                }
                $universes[$universeId]['objects'] = $options;
            }
        }

        if ($userRole and $userRole->getRoleUniverses()) {
            $roleUniverses = $userRole->getRoleUniverses();
            foreach ($roleUniverses as $roleUniverse) {
                if (isset($universes[$roleUniverse->getUniverse()->getUniverseId()])) {
                    $universes[$roleUniverse->getUniverse()->getUniverseId()]['active'] = true;
                    $universes[$roleUniverse->getUniverse()->getUniverseId()]['user_attributes'] = $roleUniverse->getAttributes();
                    $universes[$roleUniverse->getUniverse()->getUniverseId()]['user_objects'] = $roleUniverse->getObjects();
                }
            }
        }

        return TwigService::getInstance()->set('universes', $universes)
            ->render('forms/app/role-table.php');
    }
}
