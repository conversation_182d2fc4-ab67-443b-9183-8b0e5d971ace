<?php

namespace MatGyver\FormsFactory\Builder;

use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\TwigService;

class BuilderFormSelect
{
    /**
     * @var string
     */
    private $class;

    /**
     * @var string
     */
    private $function;

    /**
     * @var string
     */
    private $param;

    /**
     * @var string
     */
    private $param2;

    /**
     * @var array
     */
    private $options;

    private $value;

    /**
     * @var array
     */
    private $data;

    /**
     * @return string|null
     */
    public function getClass(): ?string
    {
        return $this->class;
    }

    /**
     * @param string|null $class
     */
    public function setClass(?string $class): void
    {
        $this->class = $class;
    }

    /**
     * @return string
     */
    public function getFunction(): string
    {
        return $this->function;
    }

    /**
     * @param string $function
     */
    public function setFunction(string $function): void
    {
        $this->function = $function;
    }

    /**
     * @return mixed|null
     */
    public function getParam()
    {
        return $this->param;
    }

    /**
     * @param mixed $param
     */
    public function setParam($param): void
    {
        $this->param = $param;
    }

    /**
     * @return mixed|null
     */
    public function getParam2()
    {
        return $this->param2;
    }

    /**
     * @param mixed $param2
     */
    public function setParam2($param2): void
    {
        $this->param2 = $param2;
    }

    /**
     * @return array|null
     */
    public function getOptions(): ?array
    {
        return $this->options;
    }

    /**
     * @param array $options
     */
    public function setOptions(array $options): void
    {
        $this->options = $options;
    }

    /**
     * @return mixed
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * @param mixed $value
     */
    public function setValue($value): void
    {
        $this->value = $value;
    }

    /**
     * @return array|null
     */
    public function getData(): ?array
    {
        return $this->data;
    }

    /**
     * @param array|null $data
     */
    public function setData(?array $data): void
    {
        $this->data = $data;
    }

    /**
     * @return string|null
     */
    public function getFunctionContent(): ?string
    {
        $container = ContainerBuilderService::getInstance();
        try {
            $service = $container->get($this->getClass());
            if ($this->getParam2()) {
                $content = $service->{$this->getFunction()}($this->getParam(), $this->getParam2());
            } elseif ($this->getParam()) {
                $content = $service->{$this->getFunction()}($this->getParam());
            } else {
                $content = $service->{$this->getFunction()}();
            }
            return $content;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * @return string|null
     */
    public function getContent(): ?string
    {
        if ($this->getClass()) {
            return $this->getFunctionContent();
        }

        if ($this->getOptions()) {
            return TwigService::getInstance()->set('options', $this->getOptions())
                ->set('data', $this->getData())
                ->set('value', $this->getValue())
                ->set('placeholder', '')
                ->render('forms/partials/select_options.php');
        }

        return null;
    }
}
