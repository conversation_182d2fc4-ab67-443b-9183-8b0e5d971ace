<?php

namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Builder;

use Knp\Menu\ItemInterface;
use Knp\Menu\Matcher\Matcher;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Factories\BlankStates\ErrorBlankState;
use Mat<PERSON><PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\Helpers\Assets;
use MatGyver\Menus\Voter\UriPatternVoter;
use MatGyver\Services\CsrfService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\TwigService;

class BuilderForm
{
    private bool $inPanel = false;

    /**
     * @var BuilderFormFieldFactory
     */
    private $builderFormFieldFactory;

    /**
     * BuilderForm constructor.
     * @param BuilderFormFieldFactory $builderFormFieldFactory
     */
    public function __construct(
        BuilderFormFieldFactory $builderFormFieldFactory
    ) {
        $this->builderFormFieldFactory = $builderFormFieldFactory;
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return string
     */
    public function render(AbstractFormFactory $objectForm, $entity = null): string
    {
        $error = $this->prepare($objectForm, $entity);
        if ($error) {
            $errorBlankState = new ErrorBlankState(
                $error,
                '',
                '',
                __('Une erreur est survenue')
            );
            return $errorBlankState->render();
        }

        $content = $this->renderHeader($objectForm);
        $content .= $this->getFieldsContent($objectForm, $entity);
        $content .= $this->renderFooter($objectForm, $entity);
        return $content;
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @param string $title
     * @return void
     */
    public function renderAsModal(AbstractFormFactory $objectForm, $entity = null, string $title = ''): void
    {
        $this->prepare($objectForm, $entity);

        $formId = $objectForm->getFormId();
        $id = $objectForm->getId();

        $form = $this->getFieldsContent($objectForm, $entity);
        $form = str_replace('card card-custom ', 'card ', $form);

        $hiddenFields = $objectForm->getHiddenFields();
        if ($entity and is_object($entity) and !isset($hiddenFields['id']) and method_exists($entity, 'getId')) {
            $hiddenFields['id'] = $entity->getId();
        }

        $content = TwigService::getInstance()->set('title', ($title ?: $objectForm->getTitle()))
            ->set('form', $form)
            ->set('id', $id)
            ->set('formId', $formId)
            ->set('hiddenFields', $hiddenFields)
            ->render(FORMS_PATH . '/partials/modal/modal-form.php');
        $container = ContainerBuilderService::getInstance();
        $content = $container->get(CsrfService::class)->replaceForm($content);

        Assets::addJs('common/modal-form.js');
        Assets::addInlineJs($content);
    }

    /**
     * @param string $title
     * @param string $id
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return void
     */
    public function renderAsPanel(string $title, string $id, AbstractFormFactory $objectForm, $entity = null): void
    {
        Assets::addCss('pages/wizard-quickpanel.css');
        Assets::addJs('common/offcanvas.js');

        $formId = $objectForm->getFormId();

        $this->inPanel = true;

        $error = $this->prepare($objectForm, $entity);
        if ($error) {
            $errorBlankState = new ErrorBlankState(
                $error,
                '',
                '',
                __('Une erreur est survenue')
            );
            $formContent = $errorBlankState->render();
        } else {
            $form = $this->getFieldsContent($objectForm, $entity);
            //$form = str_replace('card card-custom ', 'card ', $form);

            $hiddenFields = $objectForm->getHiddenFields();
            if ($entity and is_object($entity) and !isset($hiddenFields['id']) and method_exists($entity, 'getId')) {
                $hiddenFields['id'] = $entity->getId();
            }

            $formContent = TwigService::getInstance()->set('form', $form)
                ->set('formId', $formId)
                ->set('hiddenFields', $hiddenFields)
                ->render(FORMS_PATH . '/partials/tutorials/quickpanel-form-content.php');
        }

        $content = TwigService::getInstance()->set('title', $title)
            ->set('formContent', $formContent)
            ->set('id', $id)
            ->render(FORMS_PATH . '/partials/tutorials/quickpanel-form.php');
        $container = ContainerBuilderService::getInstance();
        $content = $container->get(CsrfService::class)->replaceForm($content);
        Assets::addInlineJs($content);
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return string
     */
    public function renderAsAjaxPanel(AbstractFormFactory $objectForm, $entity = null): string
    {
        $formId = $objectForm->getFormId();

        $this->inPanel = true;

        $error = $this->prepare($objectForm, $entity);
        if ($error) {
            $errorBlankState = new ErrorBlankState(
                $error,
                '',
                '',
                __('Une erreur est survenue')
            );
            $formContent = $errorBlankState->render();
        } else {
            $form = $this->getFieldsContent($objectForm, $entity);

            $hiddenFields = $objectForm->getHiddenFields();
            if ($entity and is_object($entity) and !isset($hiddenFields['id']) and method_exists($entity, 'getId')) {
                $hiddenFields['id'] = $entity->getId();
            }

            $formContent = TwigService::getInstance()->set('form', $form)
                ->set('formId', $formId)
                ->set('hiddenFields', $hiddenFields)
                ->render(FORMS_PATH . '/partials/tutorials/quickpanel-form-content.php');
        }

        $container = ContainerBuilderService::getInstance();
        return $container->get(CsrfService::class)->replaceForm($formContent);
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return string|null
     */
    private function prepare(AbstractFormFactory $objectForm, $entity = null): ?string
    {
        if (method_exists($objectForm, 'preRenderFields')) {
            try {
                $objectForm->preRenderFields($entity);
            } catch (\Exception $e) {
                LoggerService::logError($e->getMessage());
                return $e->getMessage();
            }
        }
        return '';
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return string
     */
    private function getFieldsContent(AbstractFormFactory $objectForm, $entity = null): string
    {
        $content = '';
        $formFields = $objectForm->getBuilderFields();
        $fields = $formFields->getFields();
        foreach ($fields as $field) {
            if ($field->getType() == FieldsEnum::TYPE_GROUP and $field instanceof BuilderFormGroup) {
                $group = '';
                $groupFields = $field->getFields();
                foreach ($groupFields as $groupField) {
                    if ($groupField->getType() == FieldsEnum::TYPE_GROUP and $groupField instanceof BuilderFormGroup) {
                        $group2 = '';
                        $group2Fields = $groupField->getFields();
                        foreach ($group2Fields as $group2Field) {
                            $formField = $this->makeField($group2Field, $entity);
                            $group2 .= $formField->render();
                        }
                        $group .= $this->renderGroup($groupField, $group2);
                    } else {
                        $formField = $this->makeField($groupField, $entity);
                        $group .= $formField->render();
                    }
                }
                $content .= $this->renderGroup($field, $group);
            } else {
                $formField = $this->makeField($field, $entity);
                $content .= $formField->render();
            }
        }

        return $content;
    }

    /**
     * @param BuilderFormField $field
     * @param $entity
     * @return BuilderFormField
     */
    public function makeField(BuilderFormField $field, $entity): BuilderFormField
    {
        return $this->builderFormFieldFactory->make($field, $entity);
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @return string
     */
    private function renderHeader(AbstractFormFactory $objectForm): string
    {
        $navigation = '';
        if ($objectForm->getNavigationMenu()) {
            $navigation = $this->getNavigationMenu($objectForm->getNavigationMenu());
        }
        return TwigService::getInstance()->set('title', $objectForm->getTitle())
            ->set('icon', $objectForm->getIcon())
            ->set('headerText', $objectForm->getHeaderText())
            ->set('formClass', $objectForm->getFormClass())
            ->set('enctype', $objectForm->getEncType())
            ->set('btnValidateText', $objectForm->getBtnValidateText())
            ->set('btnValidateClass', $objectForm->getBtnValidateClass())
            ->set('cancelUrl', $objectForm->getCancelUrl())
            ->set('btnCancelText', $objectForm->getBtnCancelText())
            ->set('navigation', $navigation)
            ->set('inPanel', $this->inPanel)
            ->render('forms/partials/layout/header.php');
    }

    public function getNavigationMenu(ItemInterface $menu): string
    {
        $matcher = new Matcher([
            new UriPatternVoter($_SERVER['REQUEST_URI'])
        ]);
        return TwigService::getInstance()->set('menu', $menu)
            ->set('matcher', $matcher)
            ->render('layouts/admin/headers/navigation_menu.php');
    }

    /**
     * @param AbstractFormFactory $objectForm
     * @param $entity
     * @return string
     */
    private function renderFooter(AbstractFormFactory $objectForm, $entity): string
    {
        $hiddenFields = $objectForm->getHiddenFields();
        if ($entity and is_object($entity) and !isset($hiddenFields['id']) and method_exists($entity, 'getId')) {
            $hiddenFields['id'] = $entity->getId();
        }

        return TwigService::getInstance()->set('hiddenFields', $hiddenFields)
            ->set('inPanel', $this->inPanel)
            ->set('btnValidateText', $objectForm->getBtnValidateText())
            ->set('btnValidateClass', $objectForm->getBtnValidateClass())
            ->render('forms/partials/layout/footer.php');
    }

    /**
     * @param BuilderFormGroup $group
     * @param string $content
     * @return string
     */
    public function renderGroup(BuilderFormGroup $group, string $content): string
    {
        return TwigService::getInstance()->set('content', $content)
            ->set('group', $group)
            ->render('forms/partials/layout/group.php');
    }
}
