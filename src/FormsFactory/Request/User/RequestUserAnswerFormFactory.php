<?php
namespace MatGyver\FormsFactory\Request\User;

use MatG<PERSON>ver\Enums\FieldsEnum;
use MatGyver\Entity\Request\User\RequestUserAnswer;
use MatGyver\Forms\Request\User\RequestUserAnswerForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use Symfony\Component\Validator\Constraints\NotBlank;

class RequestUserAnswerFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(RequestUserAnswerForm::class);

        $this->addContent();
        $this->addStatus();
        $this->addComment();

        $this->setTitle(__('Création d\'une réponse'));
        $this->setCancelUrl(Tools::makeLink('admin', 'requestuseranswers'));
    }

    /**
     * @param RequestUserAnswer|null $requestUserAnswer
     */
    public function preRenderFields(?RequestUserAnswer $requestUserAnswer)
    {
        if ($requestUserAnswer) {
            $this->setTitle(__('Modification d\'une réponse'));
        }
    }

    private function addContent()
    {
        $field = new BuilderFormField('content');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Contenu'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer le contenu')]));
        $this->builderFields->addField($field);
    }

    private function addStatus()
    {
        $field = new BuilderFormField('status');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Status'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer status')]));
        $this->builderFields->addField($field);
    }

    private function addComment()
    {
        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Comment'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer comment')]));
        $this->builderFields->addField($field);
    }


}
