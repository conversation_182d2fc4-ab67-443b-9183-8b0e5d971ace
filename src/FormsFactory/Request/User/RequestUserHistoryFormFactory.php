<?php
namespace MatGyver\FormsFactory\Request\User;

use MatG<PERSON>ver\Enums\FieldsEnum;
use MatGyver\Entity\Request\User\RequestUserHistory;
use MatGyver\Forms\Request\User\RequestUserHistoryForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use Symfony\Component\Validator\Constraints\NotBlank;

class RequestUserHistoryFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(RequestUserHistoryForm::class);

        $this->addAction();
        $this->addParam();

        $this->setTitle(__('Création d\'un historique'));
        $this->setCancelUrl(Tools::makeLink('admin', 'requestuserhistorys'));
    }

    /**
     * @param RequestUserHistory|null $requestUserHistory
     */
    public function preRenderFields(?RequestUserHistory $requestUserHistory)
    {
        if ($requestUserHistory) {
            $this->setTitle(__('Modification d\'un historique'));
        }
    }

    private function addAction()
    {
        $field = new BuilderFormField('action');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Action'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer action')]));
        $this->builderFields->addField($field);
    }

    private function addParam()
    {
        $field = new BuilderFormField('param');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Param'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez entrer param')]));
        $this->builderFields->addField($field);
    }


}
