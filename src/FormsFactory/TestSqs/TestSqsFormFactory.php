<?php

namespace MatGyver\FormsFactory\TestSqs;

use MatG<PERSON>ver\Entity\Sqs\Log\SqsLog;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Forms\TestSqs\TestSqsForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;
use Symfony\Component\Validator\Constraints\NotBlank;

class TestSqsFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(TestSqsForm::class);

        $field = new BuilderFormField('sqs_action');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Action'));
        $field->setHelp(__('Indiquez le nom de l\'action dans %s', 'src/Services/Aws/Sqs/ActionsService'));
        $field->setRequired(true);
        $field->addValidation(new NotBlank(['message' => __('Veuillez indiquer le nom de l\'action')]));
        $this->builderFields->addField($field);

        $field = new BuilderFormField('sqs_param');
        $field->setType(FieldsEnum::TYPE_TEXT);
        $field->setLabel(__('Param'));
        $this->builderFields->addField($field);

        $this->setTitle(__('Test SQS'));
        $this->setCancelUrl(Tools::makeLink('admin', 'index'));
    }

    public function preRenderFields(?SqsLog $sqsLog = null)
    {
        if ($sqsLog) {
            $this->builderFields->updateField('sqs_action', 'value', $sqsLog->getAction());
            $this->builderFields->updateField('sqs_param', 'value', $sqsLog->getParam());
        }
    }
}
