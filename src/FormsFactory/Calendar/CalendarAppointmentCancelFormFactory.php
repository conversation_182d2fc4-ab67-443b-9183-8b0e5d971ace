<?php
namespace <PERSON><PERSON><PERSON>ver\FormsFactory\Calendar;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\Dossier;
use Mat<PERSON><PERSON>ver\Enums\FieldsEnum;
use Mat<PERSON><PERSON>ver\Entity\Calendar\CalendarAppointment;
use MatGyver\Forms\Calendar\CalendarAppointmentCancelForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\Helpers\Tools;

class CalendarAppointmentCancelFormFactory extends AbstractFormFactory
{
    public function __construct(?Dossier $dossier = null)
    {
        parent::__construct();
        $this->setFormService(CalendarAppointmentCancelForm::class);

        $this->addComment();

        $this->setTitle(__('Annulation d\'un rendez-vous'));
        $this->setBtnValidateText(__('Annuler le rendez-vous'));
        $this->setBtnValidateClass('btn-danger');
        if ($dossier) {
            $this->setCancelUrl(Tools::makeLink('app', 'dossier', 'appointments/' . $dossier->getId()));
        }
    }

    /**
     * @param CalendarAppointment|null $calendarAppointment
     */
    public function preRenderFields(?CalendarAppointment $calendarAppointment)
    {
        if (!$calendarAppointment) {
            throw new \Exception(__('Rendez-vous inconnu'));
        }
    }

    private function addComment()
    {
        $field = new BuilderFormField('reason');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Indiquez la raison d\'annulation de ce rendez-vous'));
        $field->setHelp(__('Facultatif'));
        $this->builderFields->addField($field);
    }
}
