<?php
namespace MatGyver\FormsFactory\Calendar;

use Mat<PERSON><PERSON><PERSON>\Entity\Calendar\Calendar;
use Mat<PERSON><PERSON><PERSON>\Entity\Consulting\ConsultingReason;
use MatGyver\Enums\FieldsEnum;
use MatGyver\Entity\Calendar\CalendarAppointment;
use MatGyver\Forms\Calendar\SiteCalendarAppointmentForm;
use MatGyver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\Helpers\Assets;
use MatGyver\Helpers\Tools;
use MatGyver\Services\Calendar\CalendarService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Tos\TosService;

class SiteCalendarAppointmentFormFactory extends AbstractFormFactory
{
    /**
     * @param ConsultingReason|null $consultingReason
     * @param Calendar|null $calendar
     */
    public function __construct(?ConsultingReason $consultingReason = null, ?Calendar $calendar = null)
    {
        parent::__construct();
        $this->setFormService(SiteCalendarAppointmentForm::class);

        if ($consultingReason and $calendar) {
            $this->addHiddenFields('consulting_reason_id', $consultingReason->getId());
            $this->addHiddenFields('calendar_id', $calendar->getId());
        }

        $this->addSelectDate($calendar, $consultingReason);
        $this->addUser();
        $this->addComment($consultingReason);
        $this->addHiddenFields('form_time', time());

        $this->setTitle(__('Prise de rendez-vous'));
    }

    /**
     * @param CalendarAppointment|null $calendarAppointment
     */
    public function preRenderFields(?CalendarAppointment $calendarAppointment)
    {
        if ($calendarAppointment) {
            $this->setTitle(__('Modification d\'un rendez-vous'));
        }
    }

    /**
     * @param Calendar|null $calendar
     * @param ConsultingReason|null $consultingReason
     * @return void
     */
    private function addSelectDate(?Calendar $calendar = null, ?ConsultingReason $consultingReason = null)
    {
        $content = '';
        if ($calendar and $consultingReason) {
            $container = ContainerBuilderService::getInstance();
            $content = $container->get(CalendarService::class)->renderCalendar($calendar, $consultingReason, 'fc-calendar-availabilities');
        }

        $field = new BuilderFormField('date_content');
        $field->setType(FieldsEnum::TYPE_CONTENT);
        $field->setContent($content);
        $this->builderFields->addField($field);

        $this->addHiddenFields('date_start', '');
    }

    private function addUser()
    {
        $group = new BuilderFormGroup('group_user');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Vos informations'));
        $group->setOpened(true);

        if (RightsService::isUser() or RightsService::isEditor()) {
            $content = '<div class="user"><p>' . __('Connecté avec %s', $_SESSION['user']['email']) . '<br><a href="' . Tools::makeLink('site', 'logout') . '">' . __('Déconnexion') . '</a></p></div>';
            $field = new BuilderFormField('infos_user');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);
        } else {
            $content = '<div class="row"><div class="col-md-6" id="kt_login_signin_form"><h3>' . __('Connexion') . '</h3>';
            $field = new BuilderFormField('infos_left_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);

            $field = new BuilderFormField('login_email');
            $field->setType(FieldsEnum::TYPE_EMAIL);
            $field->setLabel(__('Adresse email'));
            $group->addField($field);

            $field = new BuilderFormField('login_password');
            $field->setType(FieldsEnum::TYPE_PASSWORD);
            $field->setLabel(__('Mot de passe'));
            $group->addField($field);

            $content = '[[CSRF]]<a class="btn btn-primary mb-8" onclick="login()" id="kt_login_signin_submit">' . __('Connexion') . '</a>';
            $field = new BuilderFormField('login_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);

            $content = '</div><div class="col-md-6"><h3>' . __('Création d\'un compte') . '</h3>';
            $field = new BuilderFormField('infos_right_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);

            $userFields = $this->getUserInfosFields();
            foreach ($userFields as $userField) {
                $group->addField($userField);
            }

            $field = new BuilderFormField('password');
            $field->setType(FieldsEnum::TYPE_PASSWORD);
            $field->setLabel(__('Mot de passe'));
            $field->setHelp(__('Veuillez indiquer un mot de passe de 12 caractères minimum et comportant au moins une lettre majuscule et un chiffre.'));
            $group->addField($field);

            $field = new BuilderFormField('password2');
            $field->setType(FieldsEnum::TYPE_PASSWORD);
            $field->setLabel(__('Mot de passe (confirmation)'));
            $group->addField($field);

            $addressFields = $this->getAddressFields();
            foreach ($addressFields as $addressField) {
                $group->addField($addressField);
            }

            $container = ContainerBuilderService::getInstance();
            $tos = $container->get(TosService::class)->getRepository()->getLast();
            if ($tos) {
                $this->addHiddenFields('cgv', '1');
                $this->addHiddenFields('cgv_txt', __('J\'ai lu les conditions générales de vente et j\'y adhère sans réserve.'));
                $this->addHiddenFields('cgv_id', $tos->getId());

                $options = [
                    [
                        'value' => true,
                        'label' => '<div>' . __('J\'ai lu les conditions générales de vente et j\'y adhère sans réserve.') . '<br><a href="' . Tools::makeLink('site', 'tos') . '" target="_blank">' . __('Lire les conditions générales de vente.') . '</a></div>',
                        'checked' => false
                    ]
                ];
                $field = new BuilderFormField('cgv-accept');
                $field->setType(FieldsEnum::TYPE_CHECKBOX);
                //$field->setLabel(__('Conditions Générales de Vente'));
                $field->setOptions($options);
                $group->addField($field);
            }

            Assets::addJs('https://www.google.com/recaptcha/api.js');
            $field = new BuilderFormField('captcha');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent('
            <div style="width:304px; display:block; margin-top:20px;">
                <div class="g-recaptcha" data-sitekey="' . GOOGLE_RECAPTCHA_PUBLIC_KEY . '" style="margin: 0 auto; display:block;"></div>
            </div>');
            $group->addField($field);

            $content = '</div></div>';
            $field = new BuilderFormField('infos_end_content');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);
        }

        $this->builderFields->addField($group);
    }

    private function addComment(?ConsultingReason $consultingReason = null)
    {
        $group = new BuilderFormGroup('group_comment');
        $group->setType(FieldsEnum::TYPE_GROUP);
        $group->setLabel(__('Commentaire'));
        $group->setOpened(true);

        if ($consultingReason and $consultingReason->getMessage()) {
            $content = '<div class="alert alert-custom alert-light-info d-flex flex-column mb-8">' . $consultingReason->getMessage() . '</div>';
            $field = new BuilderFormField('consulting_reason_message');
            $field->setType(FieldsEnum::TYPE_CONTENT);
            $field->setContent($content);
            $group->addField($field);
        }

        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_TEXTAREA);
        $field->setLabel(__('Indiquez ici tout commentaire utile pour préparer au mieux votre rendez-vous.'));
        $group->addField($field);

        $this->builderFields->addField($group);
    }
}
