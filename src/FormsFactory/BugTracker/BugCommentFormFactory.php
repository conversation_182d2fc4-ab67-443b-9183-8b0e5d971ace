<?php
namespace <PERSON><PERSON><PERSON><PERSON>\FormsFactory\BugTracker;

use <PERSON><PERSON><PERSON><PERSON>\Enums\FieldsEnum;
use <PERSON><PERSON><PERSON><PERSON>\Entity\BugTracker\Bug;
use MatGyver\Forms\BugTracker\BugCommentForm;
use MatG<PERSON>ver\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormField;

class BugCommentFormFactory extends AbstractFormFactory
{
    public function __construct()
    {
        parent::__construct();
        $this->setFormService(BugCommentForm::class);

        $this->addComment();
        $this->addClose();
    }

    /**
     * @param Bug|null $bug
     * @throws \Exception
     */
    public function preRenderFields(?Bug $bug): void
    {
        if (!$bug) {
            throw new \Exception('No bug specified.');
        }

        $this->setTitle(__('Ajouter un commentaire'));
    }

    private function addComment(): void
    {
        $field = new BuilderFormField('comment');
        $field->setType(FieldsEnum::TYPE_CKEDITOR_SMALL);
        $field->setLabel(__('Commentaire'));
        $this->builderFields->addField($field);
    }

    private function addClose(): void
    {
        $options = [
            [
                'value' => true,
                'label' => __('Marquer le bug comme "résolu". Un email sera envoyé à l\'utilisateur si il a choisi de recevoir des notifications.'),
                'checked' => false
            ]
        ];
        $field = new BuilderFormField('close');
        $field->setType(FieldsEnum::TYPE_CHECKBOX);
        $field->setOptions($options);
        $this->builderFields->addField($field);
    }
}
