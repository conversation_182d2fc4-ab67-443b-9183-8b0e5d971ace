<?php
namespace MatGyver\Forms\Integration;

use MatGyver\Services\Autoresponders\AutorespondersAffiliationService;
use MatGyver\Services\Autoresponders\AutorespondersProductPostService;
use MatGyver\Services\Autoresponders\AutorespondersProductService;
use MatGyver\Services\DI\ContainerBuilderService;
use MatGyver\Services\Integration\IntegrationAccountsService;
use MatGyver\Services\Integration\IntegrationsService;
use MatGyver\Services\Shop\ShopPaymentsService;

/**
 * Class IntegrationAccountsForm
 * @package MatGyver\Forms\Integration
 */
class IntegrationAccountsForm
{
    /**
     * @var IntegrationAccountsService
     */
    private $integrationAccountsService;

    /**
     * @var IntegrationsService
     */
    private $integrationsService;

    /**
     * IntegrationAccountsForm constructor.
     * @param IntegrationAccountsService $integrationAccountsService
     * @param IntegrationsService $integrationsService
     */
    public function __construct(
        IntegrationAccountsService $integrationAccountsService,
        IntegrationsService $integrationsService
    ) {
        $this->integrationAccountsService = $integrationAccountsService;
        $this->integrationsService = $integrationsService;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePost(array $submittedData): array
    {
        if (!isset($submittedData['param']) or !$submittedData['param']) {
            return ['valid' => false, 'message' => __("Type d'autorépondeur non reçu.")];
        }

        $integrationName = filter_var($submittedData['param'], FILTER_UNSAFE_RAW);
        $integration = $this->integrationsService->getIntegration($integrationName);
        if (!$integration) {
            return ['valid' => false, 'message' => __('Aucune intégration définie')];
        }

        $service = $this->integrationAccountsService->getService($integration['service']);
        if (!$service) {
            return ['valid' => false, 'message' => __("Ce service n'existe pas.")];
        }

        $checkFields = $this->checkAccountFields($service, $submittedData);
        if (!$checkFields['valid']) {
            return $checkFields;
        }

        $validatePostService = $this->validatePostService($service, $submittedData);
        if (!$validatePostService['valid']) {
            return $validatePostService;
        }

        return ['valid' => true];
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $name = filter_var($submittedData['name'], FILTER_UNSAFE_RAW);
        $integrationName = filter_var($submittedData['param'], FILTER_UNSAFE_RAW);
        $integration = $this->integrationsService->getIntegration($integrationName);
        if (!$name) {
            $name = $integration['name'];
        }

        $service = $this->integrationAccountsService->getService($integration['service']);
        if (!$service) {
            return ['valid' => false, 'message' => __("Ce service n'existe pas.")];
        }

        $fields = $this->integrationAccountsService->getAccountFields($service, $submittedData);
        if (!$fields) {
            return ['valid' => false, 'message' => __('Aucun paramètre sauvegardé')];
        }

        $preInsertAccount = $this->preInsertAccount($service, $submittedData);
        if (!$preInsertAccount['valid']) {
            return $preInsertAccount;
        }
        if (isset($preInsertAccount['fields']) and $preInsertAccount['fields']) {
            $fields = array_merge($fields, $preInsertAccount['fields']);
        }

        return $this->integrationAccountsService->insert($name, $integrationName, $fields);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $idAccount = filter_var($submittedData['id_account'], FILTER_VALIDATE_INT);
        if (!$idAccount) {
            return ['valid' => false, 'message' => __('Ce compte n\'existe pas.')];
        }

        $account = $this->integrationAccountsService->getAccount($idAccount);
        if (!$account) {
            return ['valid' => false, 'message' => __('Ce compte n\'existe pas.')];
        }

        $integration = $this->integrationsService->getIntegration($account->getType());

        $service = $this->integrationAccountsService->getService($integration['service']);
        if (!$service) {
            return ['valid' => false, 'message' => __("Ce service n'existe pas.")];
        }

        $fields = $this->integrationAccountsService->getAccountFields($service, $submittedData, json_decode($account->getDatas(), true));
        if (!$fields) {
            return ['valid' => false, 'message' => __('Aucun paramètre sauvegardé')];
        }

        $preInsertAccount = $this->preInsertAccount($service, $submittedData);
        if (!$preInsertAccount['valid']) {
            return $preInsertAccount;
        }
        if (isset($preInsertAccount['fields']) and $preInsertAccount['fields']) {
            $fields = array_merge($fields, $preInsertAccount['fields']);
        }

        $name = filter_var($submittedData['name'], FILTER_UNSAFE_RAW);
        $account->setName($name);

        return $this->integrationAccountsService->update($account, $fields);
    }

    /**
     * @param int $idAccount
     * @return array
     */
    public function removeIntegration(int $idAccount): array
    {
        $account = $this->integrationAccountsService->getAccount($idAccount);
        if (!$account) {
            return ['valid' => false, 'message' => __('Intégration introuvable')];
        }

        $integration = $this->integrationsService->getIntegration($account->getType());

        $service = $this->integrationAccountsService->getService($integration['service']);
        if (!$service) {
            return ['valid' => false, 'message' => __("Ce service n'existe pas.")];
        }
        if (method_exists($service, 'removeAccount')) {
            $service->removeAccount($account);
        }

        $preRemoveAccount = $this->preRemoveAccount($integration, $idAccount);
        if (!$preRemoveAccount['valid']) {
            return $preRemoveAccount;
        }

        return $this->integrationAccountsService->deleteAccount($account);
    }

    /**
     * @param $service
     * @param array $params
     * @return array
     */
    protected function checkAccountFields($service, array $params): array
    {
        if (!$service->getAccountFields()) {
            return ['valid' => true];
        }

        foreach ($service->getAccountFields() as $field => $infos) {
            if (!$infos['required']) {
                continue;
            }
            if (!isset($params[$field])) {
                return ['valid' => false, 'message' => $infos['error']];
            }

            $param = filter_var($params[$field], $infos['filter']);
            if (!$param) {
                return ['valid' => false, 'message' => $infos['error']];
            }
        }

        return ['valid' => true];
    }

    /**
     * @param $service
     * @param array $submittedData
     * @return array
     */
    private function validatePostService($service, array $submittedData): array
    {
        if (!method_exists($service, 'validatePostAccount')) {
            return ['valid' => true];
        }

        return $service->validatePostAccount($submittedData);
    }

    /**
     * @param $service
     * @param array $submittedData
     * @return array
     */
    private function preInsertAccount($service, array $submittedData): array
    {
        if (!method_exists($service, 'preInsertAccount')) {
            return ['valid' => true];
        }

        return $service->preInsertAccount($submittedData);
    }

    /**
     * @param array $integration
     * @param int $idAccount
     * @return array
     */
    private function preRemoveAccount(array $integration, int $idAccount): array
    {
        $container = ContainerBuilderService::getInstance();
        if ($integration['type'] == 'autoresponder') {
            $affiliationAutoresponder = $container->get(AutorespondersAffiliationService::class)->getAutoresponderByAccount($idAccount);
            if ($affiliationAutoresponder) {
                return ['valid' => false, 'message' => __('Impossible de supprimer ce compte : celui-ci est configuré dans le programme d\'affiliation.')];
            }

            $productAutoresponder = $container->get(AutorespondersProductService::class)->getAutoresponderByAccount($idAccount);
            if ($productAutoresponder) {
                return ['valid' => false, 'message' => __('Impossible de supprimer ce compte : celui-ci est configuré dans le produit %s.', $productAutoresponder['product_id'])];
            }

            $productAutoresponder = $container->get(AutorespondersProductPostService::class)->getAutoresponderByAccount($idAccount);
            if ($productAutoresponder) {
                return ['valid' => false, 'message' => __('Impossible de supprimer ce compte : celui-ci est configuré dans le produit %s.', $productAutoresponder['product_id'])];
            }
        }

        if ($integration['type'] == 'payment') {
            $payment = $container->get(ShopPaymentsService::class)->getPaymentByAccount($idAccount);
            if ($payment) {
                return ['valid' => false, 'message' => __('Impossible de supprimer ce compte : celui-ci est configuré comme moyen de paiement pour le produit %s.', $payment->getProduct()->getName())];
            }
        }

        return ['valid' => true];
    }
}
