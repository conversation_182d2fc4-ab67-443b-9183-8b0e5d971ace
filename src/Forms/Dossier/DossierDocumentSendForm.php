<?php
namespace Mat<PERSON><PERSON>ver\Forms\Dossier;

use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierDocument;
use Mat<PERSON><PERSON><PERSON>\Entity\Dossier\DossierHistory;
use MatGyver\Entity\Dossier\DossierPerson;
use MatGyver\Forms\AbstractForm;
use MatGyver\FormsFactory\Dossier\DossierDocumentSendFormFactory;
use MatGyver\Services\Dossier\DossierDocumentService;
use MatGyver\Services\Dossier\DossierHistoryService;
use MatGyver\Services\Dossier\DossierPersonService;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\Dossier\Pdf\DossierPdfSummaryService;
use MatGyver\Services\Logger\LoggerService;
use MatGyver\Services\Users\UsersService;

/**
 * Class DossierDocumentSendForm
 * @package MatGyver\Forms\Dossier
 */
class DossierDocumentSendForm extends AbstractForm
{
    /**
     * @var DossierService
     */
    private $dossierService;

    /**
     * @var DossierDocumentService
     */
    private $dossierDocumentService;

    /**
     * @var DossierPersonService
     */
    private $dossierPersonService;

    /**
     * @var DossierPdfSummaryService
     */
    private $dossierPdfSummaryService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * DossierDocumentSendForm constructor.
     * @param DossierService $dossierService
     * @param DossierDocumentService $dossierDocumentService
     * @param DossierPersonService $dossierPersonService
     * @param DossierPdfSummaryService $dossierPdfSummaryService
     * @param UsersService $usersService
     * @param DossierDocumentSendFormFactory $formFactory
     */
    public function __construct(
        DossierService $dossierService,
        DossierDocumentService $dossierDocumentService,
        DossierPersonService $dossierPersonService,
        DossierPdfSummaryService $dossierPdfSummaryService,
        UsersService $usersService,
        DossierDocumentSendFormFactory $formFactory
    ) {
        $this->dossierService = $dossierService;
        $this->dossierDocumentService = $dossierDocumentService;
        $this->dossierPersonService = $dossierPersonService;
        $this->dossierPdfSummaryService = $dossierPdfSummaryService;
        $this->usersService = $usersService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        $dossierDocumentId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $dossierDocument = $this->dossierDocumentService->getRepository()->find($dossierDocumentId);
        if (!$dossierDocument) {
            return $this->sendErrorResponse([__('Ce document n\'existe pas')]);
        }

        if (!isset($submittedData['dossier_id'])) {
            return $this->sendErrorResponse([__('Aucun dossier sélectionné')]);
        }
        $dossierId = filter_var($submittedData['dossier_id'], FILTER_VALIDATE_INT);
        $dossier = $this->dossierService->getRepository()->find($dossierId);
        if (!$dossier) {
            return $this->sendErrorResponse([__('Ce dossier n\'existe pas')]);
        }

        if (!in_array($dossierDocument->getType(), [DossierDocument::TYPE_SUMMARY])) {
            return $this->sendErrorResponse([__('Type de document incorrect')]);
        }

        $persons = [];
        $emails = [];
        if (isset($submittedData['persons'])) {
            $persons = filter_var($submittedData['persons'], FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
        }
        if (isset($submittedData['emails'])) {
            $emails = filter_var($submittedData['emails'], FILTER_UNSAFE_RAW);
            $emails = extractEmails($emails);
        }
        if (!$persons and !$emails) {
            return $this->sendErrorResponse([__('Veuillez sélectionner au moins un destinataire')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $dossierDocumentId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $dossierDocument = $this->dossierDocumentService->getRepository()->find($dossierDocumentId);
        if (!$dossierDocument) {
            return $this->sendErrorResponse([__('Ce document n\'existe pas.')]);
        }

        $dossierId = filter_var($submittedData['dossier_id'], FILTER_VALIDATE_INT);
        $dossier = $this->dossierService->getRepository()->find($dossierId);

        $message = filter_input(INPUT_POST, 'message', FILTER_UNSAFE_RAW);
        $message = nl2br($message);

        if (isset($submittedData['persons']) and $submittedData['persons']) {
            $personsIds = filter_var($submittedData['persons'], FILTER_UNSAFE_RAW, FILTER_REQUIRE_ARRAY);
            foreach ($personsIds as $personId) {
                $person = null;
                $email = '';
                if ($personId == 'me') {
                    $user = $this->usersService->getUser();
                    $email = $user->getEmail();
                } else {
                    $person = $this->dossierPersonService->getRepository()->findOneBy(['id' => $personId, 'dossier' => $dossier]);
                    if (!$person) {
                        continue;
                    }

                    if (!$person->getEmail()) {
                        $email = filter_var($submittedData['email-' . $personId], FILTER_VALIDATE_EMAIL);
                        if ($email) {
                            $person->setEmail($email);
                            try {
                                $this->dossierPersonService->persistAndFlush($person);
                            } catch (\Exception $e) {
                                LoggerService::logError('Unable to update person email : ' . $e->getMessage());
                                continue;
                            }
                        }
                    }
                }

                $sendMail = $this->send($dossierDocument, $person, $email, $message);
                if (!$sendMail['valid']) {
                    return $this->sendErrorResponse([$sendMail['message']]);
                }
            }
        }

        $emails = filter_var($submittedData['emails'], FILTER_UNSAFE_RAW);
        $emails = extractEmails($emails);
        if ($emails) {
            foreach ($emails as $email) {
                $sendMail = $this->send($dossierDocument, null, $email, $message);
                if (!$sendMail['valid']) {
                    return $this->sendErrorResponse([$sendMail['message']]);
                }
            }
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param DossierDocument $dossierDocument
     * @param DossierPerson|null $person
     * @param string $email
     * @param string $message
     * @return array
     */
    public function send(DossierDocument $dossierDocument, DossierPerson $person = null, string $email = '', string $message = ''): array
    {
        if ($dossierDocument->getType() == DossierDocument::TYPE_SUMMARY) {
            DossierHistoryService::add($dossierDocument->getDossier(), DossierHistory::ACTION_DOCUMENT_SENT, $person ? $person->getId() : $email);
            return $this->dossierPdfSummaryService->send($dossierDocument, $person, $email, $message);
        }

        return ['valid' => false, 'message' => __('Type de document incorrect.')];
    }
}
