<?php
namespace Mat<PERSON><PERSON>ver\Forms\Affiliation;

use <PERSON><PERSON><PERSON><PERSON>\Forms\AbstractForm;
use Mat<PERSON><PERSON>ver\FormsFactory\Affiliation\AffiliationSubPartnerFormFactory;
use MatGyver\Services\Affiliation\AffiliationSubPartnersService;
use MatGyver\Services\RightsService;

/**
 * Class AffiliationSubPartnersForm
 * @package MatGyver\Forms\Affiliation
 */
class AffiliationSubPartnersForm extends AbstractForm
{
    /**
     * @var AffiliationSubPartnersService
     */
    private $affiliationSubPartnersService;

    /**
     * AffiliationSubPartnersForm constructor.
     * @param AffiliationSubPartnersService $affiliationSubPartnersService
     * @param AffiliationSubPartnerFormFactory $formFactory
     */
    public function __construct(
        AffiliationSubPartnersService $affiliationSubPartnersService,
        AffiliationSubPartnerFormFactory $formFactory
    ) {
        $this->affiliationSubPartnersService = $affiliationSubPartnersService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_AFFILIATION)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $insert = $this->affiliationSubPartnersService->insert($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     * @throws \Exception
     */
    public function update(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_AFFILIATION)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $update = $this->affiliationSubPartnersService->update($submittedData);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        return $this->sendSuccessResponse();
    }
}
