<?php
namespace MatG<PERSON>ver\Forms\User;

use Mat<PERSON>yver\Forms\AbstractForm;
use Mat<PERSON>yver\FormsFactory\User\UserMfaConfigFormFactory;
use MatGyver\Services\Users\UserMfaService;
use MatGyver\Services\Users\UsersService;

/**
 * Class UserMfaConfigForm
 * @package MatGyver\Forms\User
 */
class UserMfaConfigForm extends AbstractForm
{
    private UsersService $usersService;
    private UserMfaService $userMfaService;

    /**
     * UserMfaConfigForm constructor.
     * @param UsersService $usersService
     * @param UserMfaService $userMfaService
     * @param UserMfaConfigFormFactory $formFactory
     */
    public function __construct(
        UsersService $usersService,
        UserMfaService $userMfaService,
        UserMfaConfigFormFactory $formFactory
    ) {
        $this->usersService = $usersService;
        $this->userMfaService = $userMfaService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        $code = filter_var($submittedData['code'], FILTER_UNSAFE_RAW);
        if (!$code) {
            return $this->sendErrorResponse([__('Veuillez entrer le code généré par votre application d\'authentification')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     * @throws \Exception
     */
    public function update(array $submittedData): array
    {
        $code = filter_var($submittedData['code'], FILTER_UNSAFE_RAW);
        $user = $this->usersService->getUser();

        $checkMfaConfig = $this->userMfaService->checkMfaConfig($user, $code);
        if (!$checkMfaConfig['valid']) {
            return $this->sendErrorResponse([$checkMfaConfig['message']]);
        }

        return $this->sendSuccessResponse(__('Configuration enregistrée.'));
    }
}
