<?php

namespace MatGyver\Forms\Support;

use MatGyver\Entity\Support\User\SupportUser;
use MatGyver\Entity\Support\User\SupportUserDepartment;
use MatGyver\Forms\AbstractForm;
use MatGyver\Services\RightsService;
use MatGyver\Services\Support\SupportDepartmentService;
use MatGyver\Services\Support\SupportUserDepartmentService;
use MatGyver\Services\Support\SupportUserService;
use MatGyver\Services\Users\UsersService;

/**
 * Class SupportUserPermissionForm
 * @package MatGyver\Forms\Support
 */
class SupportUserPermissionForm extends AbstractForm
{
    /**
     * @var SupportDepartmentService
     */
    private $supportDepartmentService;

    /**
     * @var SupportUserService
     */
    private $supportUserService;

    /**
     * @var SupportUserDepartmentService
     */
    private $supportUserDepartmentService;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * SupportUserForm constructor.
     * @param SupportDepartmentService $supportDepartmentService
     * @param SupportUserService $supportUserService
     * @param SupportUserDepartmentService $supportUserDepartmentService
     * @param UsersService $usersService
     */
    public function __construct(
        SupportDepartmentService $supportDepartmentService,
        SupportUserService $supportUserService,
        SupportUserDepartmentService $supportUserDepartmentService,
        UsersService $usersService
    ) {
        $this->supportDepartmentService = $supportDepartmentService;
        $this->supportUserService = $supportUserService;
        $this->supportUserDepartmentService = $supportUserDepartmentService;
        $this->usersService = $usersService;
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostInsert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SAV)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $userId = filter_var($submittedData['id_user'], FILTER_VALIDATE_INT);
        $idDepartment = filter_var($submittedData['id_department'], FILTER_VALIDATE_INT);

        if (!$userId) {
            return $this->sendErrorResponse([__("Cet administrateur n'existe pas")]);
        }
        if (!$idDepartment) {
            return $this->sendErrorResponse([__("Veuillez sélectionner un département")]);
        }

        $supportUser = $this->supportUserService->getRepository()->find($userId);
        if (!$supportUser) {
            return $this->sendErrorResponse([__("Cet administrateur n'existe pas")]);
        }

        //verification de duplication
        $result = $this->supportUserDepartmentService->getRepository()->getUserByDepartment($supportUser, $idDepartment);
        if ($result) {
            return $this->sendErrorResponse([__("Cet administrateur a déjà une permission pour ce service et ce département")]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $idPermission = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $userId = filter_var($submittedData['id_user'], FILTER_VALIDATE_INT);
        $idDepartment = filter_var($submittedData['id_department'], FILTER_VALIDATE_INT);

        if (!$idPermission) {
            return $this->sendErrorResponse([__("Impossible de continuer")]);
        }
        if (!$userId) {
            return $this->sendErrorResponse([__("Cet administrateur n'existe pas")]);
        }
        if (!$idDepartment) {
            return $this->sendErrorResponse([__("Veuillez sélectionner un département")]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $userId = filter_var($submittedData['id_user'], FILTER_VALIDATE_INT);
        $idDepartment = filter_var($submittedData['id_department'], FILTER_VALIDATE_INT);

        $signature = filter_var($_POST['signature'], FILTER_UNSAFE_RAW);
        if (trim(strip_tags($signature)) == '') {
            $signature = '';
        }

        $notify = false;
        if (isset($submittedData['notify']) and $submittedData['notify']) {
            $notify = true;
        }

        $supportUser = $this->supportUserService->getRepository()->find($userId);

        $supportUserDepartment = new SupportUserDepartment();
        $supportUserDepartment->setUser($supportUser);
        $supportUserDepartment->setSignature($signature);
        $supportUserDepartment->setNotify($notify);
        $supportUserDepartment->setDepartment($this->supportDepartmentService->getDepartmentById($idDepartment));
        try {
            $this->supportUserDepartmentService->persistAndFlush($supportUserDepartment);
        } catch (\Exception $e) {
            return $this->sendErrorResponse([__("Erreur lors de l'enregistrement des permissions de l'administrateur.")]);
        }

        return $this->sendSuccessResponse(__("Les permissions de cet administrateur ont bien été enregistrées."));
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $idPermission = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $idDepartment = filter_var($submittedData['id_department'], FILTER_VALIDATE_INT);
        $signature = filter_var($_POST['signature'], FILTER_UNSAFE_RAW);

        if (trim(strip_tags($signature)) == '') {
            $signature = '';
        }

        $notify = false;
        if (isset($submittedData['notify']) and $submittedData['notify']) {
            $notify = true;
        }

        $supportUserDepartment = $this->supportUserDepartmentService->getRepository()->find($idPermission);
        if (!$supportUserDepartment) {
            return $this->sendErrorResponse([__("Les permissions de cet opérateur n'existent pas.")]);
        }

        $supportUserDepartment->setSignature($signature);
        $supportUserDepartment->setNotify($notify);
        $supportUserDepartment->setDepartment($this->supportDepartmentService->getDepartmentById($idDepartment));
        try {
            $this->supportUserDepartmentService->persistAndFlush($supportUserDepartment);
        } catch (\Exception $e) {
            return $this->sendErrorResponse([__("Erreur lors de la mise à jour des permissions de l'opérateur.")]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param int $permissionId
     * @return array
     */
    public function deleteUserPermission(int $permissionId): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SAV)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        if (!$permissionId) {
            return ["valid" => false, "message" => __("Identifiant de permission non reçu")];
        }

        $delete = $this->supportUserDepartmentService->deleteUserPermission($permissionId);
        if (!$delete['valid']) {
            return $this->sendErrorResponse([__("Erreur lors de la suppression des permissions de l'administrateur.")]);
        }
        return $this->sendSuccessResponse();
    }
}
