<?php

namespace MatGyver\Forms\Shop;

use Mat<PERSON>yver\Forms\AbstractForm;
use MatGyver\FormsFactory\Shop\ShopTransactionRefundFormFactory;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Transaction\ShopTransactionRefundsService;

/**
 * Class ShopTransactionRefundForm
 * @package MatGyver\Forms\Shop
 */
class ShopTransactionRefundForm extends AbstractForm
{
    /**
     * @var ShopTransactionRefundsService
     */
    private $shopTransactionRefundsService;

    /**
     * ShopTransactionRefundForm constructor.
     * @param ShopTransactionRefundsService $shopTransactionRefundsService
     * @param ShopTransactionRefundFormFactory $formFactory
     */
    public function __construct(
        ShopTransactionRefundsService $shopTransactionRefundsService,
        ShopTransactionRefundFormFactory $formFactory
    ) {
        $this->shopTransactionRefundsService = $shopTransactionRefundsService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostInsert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $validate = $this->shopTransactionRefundsService->validatepost_refund($submittedData);
        if (!$validate['valid']) {
            return $this->sendErrorResponse([$validate['message']]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $insert = $this->shopTransactionRefundsService->insertRefund($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        return $this->sendSuccessResponse(__('Le remboursement a bien été enregistré.'));
    }
}
