<?php

namespace MatG<PERSON>ver\Forms\Shop;

use Mat<PERSON>yver\Entity\Config\Config;
use Mat<PERSON><PERSON>ver\Enums\ConfigEnum;
use Mat<PERSON><PERSON>ver\Forms\AbstractForm;
use Mat<PERSON>yver\FormsFactory\Shop\ShopInvoiceEmailsSettingsFactory;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\RightsService;

/**
 * Class ShopInvoicesEmailsSettingsForm
 * @package MatGyver\Forms\Shop
 */
class ShopInvoicesEmailsSettingsForm extends AbstractForm
{
    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * ShopInvoicesEmailsSettingsForm constructor.
     * @param ConfigService $configService
     * @param ClientsService $clientsService
     * @param ShopInvoiceEmailsSettingsFactory $formFactory
     */
    public function __construct(
        ConfigService $configService,
        ClientsService $clientsService,
        ShopInvoiceEmailsSettingsFactory $formFactory
    ) {
        $this->configService = $configService;
        $this->clientsService = $clientsService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $settings = $this->getFieldsParamsFromPost();
        if (!isset($settings['invoice_email'])) {
            $settings['invoice_email'] = false;
        }
        if (!isset($settings['invoice_email_subscription'])) {
            $settings['invoice_email_subscription'] = false;
        }
        if (!isset($settings['invoice_email_error'])) {
            $settings['invoice_email_error'] = false;
        }

        $this->saveInvoiceSettings($settings);
        return $this->sendSuccessResponse(__('Vos réglages de facturation ont bien été mis à jour.'));
    }

    /**
     * @param array $settings
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    private function saveInvoiceSettings(array $settings)
    {
        $config = $this->configService->getRepository()->findOneBy(['name' => ConfigEnum::INVOICE, 'client' => $_SESSION['client']['id']]);
        if (!$config) {
            $client = $this->clientsService->getClientById($_SESSION['client']['id']);
            $config = new Config();
            $config->setClient($client);
            $config->setName(ConfigEnum::INVOICE);
        } else {
            $oldSettings = json_decode($config->getValue(), true);
            if (!is_array($oldSettings)) {
                $oldSettings = [];
            }
            $settings = array_merge($oldSettings, $settings);
        }

        $config->setValue(json_encode($settings));
        $this->configService->persistAndFlush($config);
    }
}
