<?php

namespace MatG<PERSON>ver\Forms\Shop;

use Mat<PERSON>yver\Entity\Config\Config;
use Mat<PERSON><PERSON>ver\Enums\ConfigEnum;
use MatGyver\Forms\AbstractForm;
use MatGyver\FormsFactory\Shop\ShopInvoiceContactFactory;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\ConfigService;
use MatGyver\Services\RightsService;

/**
 * Class ShopInvoicesContactForm
 * @package MatGyver\Forms
 */
class ShopInvoicesContactForm extends AbstractForm
{
    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * ShopInvoicesContactForm constructor.
     * @param ConfigService $configService
     * @param ClientsService $clientsService
     * @param ShopInvoiceContactFactory $formFactory
     */
    public function __construct(
        ConfigService $configService,
        ClientsService $clientsService,
        ShopInvoiceContactFactory $formFactory
    ) {
        $this->configService = $configService;
        $this->clientsService = $clientsService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $settings = [];
        $settings['logo'] = filter_var($submittedData['logo'], FILTER_UNSAFE_RAW);
        $settings['code_siret'] = filter_var($submittedData['code_siret'], FILTER_UNSAFE_RAW);
        $settings['tva_intra'] = filter_var($submittedData['tva_intra'], FILTER_UNSAFE_RAW);
        $settings['code_naf'] = filter_var($submittedData['code_naf'], FILTER_UNSAFE_RAW);

        $settings['infos1'] = filter_input(INPUT_POST, 'infos1', FILTER_UNSAFE_RAW);
        $settings['infos2'] = filter_input(INPUT_POST, 'infos2', FILTER_UNSAFE_RAW);
        $settings['infos_new_page'] = filter_input(INPUT_POST, 'infos_new_page', FILTER_UNSAFE_RAW);
        $settings['infos1'] = $this->replaceFonts($settings['infos1']);
        $settings['infos2'] = $this->replaceFonts($settings['infos2']);
        $settings['infos_new_page'] = $this->replaceFonts($settings['infos_new_page']);

        $settings['infos_sale_ue_intracom'] = filter_input(INPUT_POST, 'infos_sale_ue_intracom', FILTER_UNSAFE_RAW);
        $settings['infos_sale_outside_ue'] = filter_input(INPUT_POST, 'infos_sale_outside_ue', FILTER_UNSAFE_RAW);

        //test export PDF
        $testExport = $this->testExportPdf($settings);
        if (!$testExport['valid']) {
            return $this->sendErrorResponse([$testExport['message']]);
        }

        $this->saveInvoiceSettings($settings);
        return $this->sendSuccessResponse();
    }

    /**
     * @param array $settings
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    private function saveInvoiceSettings(array $settings)
    {
        $config = $this->configService->getRepository()->findOneBy(['name' => ConfigEnum::INVOICE, 'client' => $_SESSION['client']['id']]);
        if (!$config) {
            $client = $this->clientsService->getClientById($_SESSION['client']['id']);
            $config = new Config();
            $config->setClient($client);
            $config->setName(ConfigEnum::INVOICE);
        } else {
            $oldSettings = json_decode($config->getValue(), true);
            if (!is_array($oldSettings)) {
                $oldSettings = [];
            }
            $settings = array_merge($oldSettings, $settings);
        }

        $config->setValue(json_encode($settings));
        $this->configService->persistAndFlush($config);
    }

    /**
     * @param string $text
     * @return string
     */
    private function replaceFonts(string $text): string
    {
        //replace fonts not included in TCPDF
        $fonts = [
            'Arial,Helvetica,sans-serif' => 'Helvetica',
            'Book Antiqua' => '',
            'Calibri' => '',
            'Candara' => '',
            'Cambria' => '',
            'Century' => '',
            'Century Gothic' => '',
            'Comic Sans MS,cursive' => '',
            'Consolas' => '',
            'Constantia' => '',
            'Courier New,Courier,monospace' => 'Courier',
            'Franklin Gothic Medium' => '',
            'Garamond' => '',
            'garamond' => '',
            'Georgia,serif' => '',
            'Helvetica Neue' => 'Helvetica',
            'Impact' => '',
            'Lucida Sans Unicode,Lucida Grande,sans-serif' => 'Helvetica',
            'Segoe Print' => '',
            'Tahoma,Geneva,sans-serif' => '',
            'Times New Roman,Times,serif' => 'times',
            'Trebuchet MS,Helvetica,sans-serif' => 'Helvetica',
            'Verdana,Geneva,sans-serif' => 'Helvetica',
            'tt165o00' => '',
        ];
        foreach ($fonts as $input_font => $replace_font) {
            if (!$replace_font) {
                $text = str_replace('font-family:' . $input_font . ';', '', $text);
                $text = str_replace('style="font-family:' . $input_font . '"', '', $text);
            } else {
                $text = str_replace('font-family:' . $input_font . ';', 'font-family:' . $replace_font . ';', $text);
                $text = str_replace('style="font-family:' . $input_font . '"', 'style="font-family:' . $replace_font . ';"', $text);
            }
        }

        //remove unauthorized fonts
        $originalFonts = array_keys($fonts);
        $replaceFonts = array_values($fonts);
        $replaceFonts = array_unique($replaceFonts);
        $replaceFonts = array_filter($replaceFonts);

        preg_match_all('/font-family\:(.+?);/', $text, $matches);
        if ($matches) {
            foreach ($matches[1] as $id => $match) {
                $match = trim($match);
                if (!in_array($match, $originalFonts) and !in_array($match, $replaceFonts)) {
                    $text = str_replace($matches[0][$id], '', $text);
                }
            }
        }

        return $text;
    }

    /**
     * @param array $settings
     * @return array
     * @throws \Spipu\Html2Pdf\Exception\Html2PdfException
     */
    private function testExportPdf(array $settings): array
    {
        $logo = APP_LOGO;
        if ($settings['logo']) {
            $logo = $settings['logo'];
        }

        $sellerCountryName = '';
        if (DEFAULT_COUNTRY) {
            $getCountry = \MatGyver\Helpers\Country::getCountryByCode(DEFAULT_COUNTRY);
            if ($getCountry) {
                $sellerCountryName = $getCountry->getName();
            }
        }

        $clientSettings = $this->configService->getConfig(CLIENT_MASTER);
        $address = '';
        if (isset($clientSettings['address']) and $clientSettings['address']) {
            $address = $clientSettings['address'] . '<br>';
            if (isset($clientSettings['address2']) and $clientSettings['address2']) {
                $address .= $clientSettings['address2'] . '<br>';
            }
        }
        if (isset($clientSettings['zip']) and $clientSettings['zip']) {
            $address .= $clientSettings['zip'];
            if (isset($clientSettings['city']) and $clientSettings['city']) {
                $address .= ' ' . $clientSettings['city'];
            }
            $address .= '<br>';
        } elseif (isset($clientSettings['city']) and $clientSettings['city']) {
            $address .= $clientSettings['city'] . '<br>';
        }

        $output = '
            <page backtop="10mm" backbottom="10mm" backleft="0mm" backright="0mm">
                <table>
                    <tr>
                        <td style="width:350px; vertical-align:top;"><img src="' . $logo . '" alt="Logo" style="max-height:200px; max-width:350px; vertical-align:top;" /></td>
                        <td style="text-align:right; width:350px">';

        if (isset($clientSettings['company']) and $clientSettings['company']) {
            $output .= '<h1 style="margin-bottom:0; margin-top:0">' . $clientSettings['company'] . '</h1>';
        } else {
            $output .= '<h1 style="margin-bottom:0; margin-top:0">' . APP_NAME . '</h1>';
        }

        $output .= '<h3 style="margin-bottom:0">' . __('Facture') . '<br>' . dateFr(date('Y-m-d H:i:s')) . '</h3>';

        $output .= '<p style="margin-bottom:0">';
        if ($address) {
            $output .= $address . '<br>';
        }
        if ($sellerCountryName) {
            $output .= $sellerCountryName . '<br>';
        }
        if ($settings['code_siret']) {
            $output .= __('SIRET : ') . $settings['code_siret'] . '<br>';
        }
        if ($settings['tva_intra']) {
            $output .= __('TVA : ') . $settings['tva_intra'];
        }

        $output .= '
                        </p>
                    </td>
                </tr>
            </table>';


        if ($settings['infos_sale_ue_intracom']) {
            $output .= '
            <table>
                <tr>
                    <td>' . $settings['infos_sale_ue_intracom'] . '</td>
                </tr>
            </table>';
        }

        //vente hors UE
        if ($settings['infos_sale_outside_ue']) {
            $output .= '
            <table>
                <tr>
                    <td>' . $settings['infos_sale_outside_ue'] . '</td>
                </tr>
            </table>';
        }

        $output .= '        
                <table>
			        <tr>
			            <td style="height:100px"> </td>
			        </tr>
			    </table>';

        if ($settings['infos1']) {
            $output .= $settings['infos1'];
        }

        $page_footer = '
        <page_footer>
            <hr style="height:1px; color:#AAA">
            <p style="text-align:center; font-size:14px;">';

        if (isset($clientSettings['company']) and $clientSettings['company']) {
            $page_footer .= '<strong>' . $clientSettings['company'] . '</strong><br>';
        } else {
            $page_footer .= '<strong>' . APP_NAME . '</strong><br>';
        }

        $infos = '';
        if ($settings['code_siret']) {
            $infos .= __('N° SIRET') . ' : ' . $settings['code_siret'] . ' - ';
        }
        if ($settings['tva_intra']) {
            $infos .= __('N° TVA') . ' : ' . $settings['tva_intra'] . ' - ';
        }
        if ($settings['code_naf']) {
            $infos .= __('NAF') . ' : ' . $settings['code_naf'] . ' - ';
        }

        $infos = rtrim($infos, ' - ');

        $page_footer .= $infos . '</p>';

        if ($settings['infos2']) {
            $page_footer .= $settings['infos2'];
        }

        $page_footer .= '</page_footer>';
        $output .= $page_footer;
        $output .= '</page>';

        if ($settings['infos_new_page']) {
            $output .= '<page backtop="10mm" backbottom="10mm" backleft="0mm" backright="0mm">';
            $output .= $settings['infos_new_page'];
            $output .= $page_footer;
            $output .= '</page>';
        }

        //export
        $html2pdf = new \Spipu\Html2Pdf\Html2Pdf('P', 'A4', 'fr');
        $html2pdf->pdf->SetTitle(__('Facture'));
        $html2pdf->pdf->SetSubject(__('Facture'));
        $html2pdf->setTestIsImage(false);
        try {
            $html2pdf->WriteHTML($output);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Impossible de générer une facture avec ces paramètres : ') . $e->getMessage()];
        }

        $output_file = UPLOAD_PATH . '/invoices/settings_invoices_' . $_SESSION['client']['id'] . '.pdf';
        $html2pdf->Output($output_file, 'F');
        @unlink($output_file);

        return ['valid' => true];
    }
}
