<?php

namespace MatGyver\Forms\Shop;

use Mat<PERSON><PERSON>ver\Forms\AbstractForm;
use Mat<PERSON><PERSON>ver\FormsFactory\Shop\ShopDiscountFormFactory;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\ShopDiscountsService;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Class ShopDiscountForm
 * @package MatGyver\Forms\Shop
 */
class ShopDiscountForm extends AbstractForm
{
    /**
     * @var ShopDiscountsService
     */
    private $shopDiscountsService;

    /**
     * ShopDiscountForm constructor.
     * @param ShopDiscountsService $shopDiscountsService
     * @param ShopDiscountFormFactory $formFactory
     */
    public function __construct(
        ShopDiscountsService $shopDiscountsService,
        ShopDiscountFormFactory $formFactory
    ) {
        $this->shopDiscountsService = $shopDiscountsService;
        $this->setFactory($formFactory);
    }

    public function validatePostInsert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $rules = [
            'name' => [new NotBlank(['message' => __('Veuillez indiquer le nom du bon de réduction.')])],
            'discount_amount' => [new NotBlank(['message' => __('Veuillez indiquer le montant de la réduction.')])],
            'code' => [new NotBlank(['message' => __('Veuillez indiquer le code du bon de réduction.')])],
            'products' => [new NotBlank(['message' => __('Veuillez sélectionner un ou plusieurs tarifs.')])],
        ];

        $errors = $this->validateForm($rules, $submittedData);
        if ($errors) {
            return $this->sendResponse($errors);
        }

        $discountType = filter_var($submittedData['discount_type'], FILTER_UNSAFE_RAW);
        if ($discountType == 'pourcentage') {
            $discountAmount = filter_var($submittedData['discount_amount'], FILTER_VALIDATE_FLOAT);
            if ($discountAmount > 100) {
                return $this->sendResponse([__('Le montant de la réduction ne peut être supérieur à 100%.')]);
            }
        }

        if (!isset($submittedData['id'])) {
            $code = filter_var($submittedData['code'], FILTER_UNSAFE_RAW);
            $codes = explode("\n", $code);
            $codes = array_map('trim', $codes);
            $codes = array_filter($codes);
            $codes = array_unique($codes);
            foreach ($codes as $code) {
                $discount = $this->shopDiscountsService->getDiscountByCode($code);
                if ($discount) {
                    return $this->sendResponse([__('Un bon de réduction existe déjà avec le code %s', $code)]);
                }
            }
        }

        return $this->sendSuccessResponse();
    }

    public function validatePostUpdate(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $idDiscount = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $discount = $this->shopDiscountsService->getDiscountById($idDiscount);
        if (!$discount) {
            return $this->sendErrorResponse([__('Ce bon de réduction n\'existe pas.')]);
        }

        $code = filter_var($submittedData['code'], FILTER_UNSAFE_RAW);
        if ($code != $discount->getCode()) {
            $otherDiscount = $this->shopDiscountsService->getDiscountByCode($code);
            if ($otherDiscount) {
                return $this->sendResponse([__('Un bon de réduction existe déjà avec le code %s', $code)]);
            }
        }

        return $this->validatePostInsert($submittedData);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $insert = $this->shopDiscountsService->insertDiscount($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        return $this->sendSuccessResponse(__('Le bon de réduction a bien été enregistré.'));
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $update = $this->shopDiscountsService->updateDiscount($submittedData);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        return $this->sendSuccessResponse(__('Le bon de réduction a bien été modifié.'));
    }

    /**
     * @param int $idDiscount
     * @return array
     */
    public function delete(int $idDiscount): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $delete = $this->shopDiscountsService->deleteDiscount($idDiscount);
        if (!$delete['valid']) {
            return $this->sendErrorResponse([$delete['message']]);
        }

        return $this->sendSuccessResponse();
    }
}
