<?php

namespace Mat<PERSON><PERSON>ver\Forms\Shop;

use Mat<PERSON><PERSON>ver\Forms\AbstractForm;
use Mat<PERSON><PERSON>ver\FormsFactory\Shop\ShopCustomerFormFactory;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\ShopCustomersService;

/**
 * Class ShopCustomerForm
 * @package MatGyver\Forms\Shop
 */
class ShopCustomerForm extends AbstractForm
{
    /**
     * @var ShopCustomersService
     */
    private $shopCustomersService;

    /**
     * ShopCustomerForm constructor.
     * @param ShopCustomersService $shopCustomersService
     * @param ShopCustomerFormFactory $formFactory
     */
    public function __construct(
        ShopCustomersService $shopCustomersService,
        ShopCustomerFormFactory $formFactory
    ) {
        $this->shopCustomersService = $shopCustomersService;
        $this->setFactory($formFactory);
    }

    public function validatePostInsert(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        return $this->sendSuccessResponse();
    }

    public function validatePostUpdate(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $idCustomer = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $customer = $this->shopCustomersService->getCustomerById($idCustomer);
        if (!$customer) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas.')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $insert = $this->shopCustomersService->appInsertCustomer($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        return $this->sendSuccessResponse(__('Le client a bien été enregistré.'));
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $update = $this->shopCustomersService->appUpdateCustomer($submittedData);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        return $this->sendSuccessResponse(__('Le client a bien été modifié.'));
    }

    /**
     * @param int $idCustomer
     * @return array
     */
    public function delete(int $idCustomer): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_PRODUCTS)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $delete = $this->shopCustomersService->delete_customer($idCustomer);
        if (!$delete['valid']) {
            return $this->sendErrorResponse([$delete['message']]);
        }

        return $this->sendSuccessResponse();
    }
}
