<?php

namespace MatGyver\Forms\Shop;

use Mat<PERSON>yver\Forms\AbstractForm;
use Mat<PERSON>yver\FormsFactory\Shop\ShopTransactionErrorFormFactory;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Transaction\Error\ShopTransactionErrorService;

/**
 * Class ShopTransactionErrorForm
 * @package MatGyver\Forms\Shop
 */
class ShopTransactionErrorForm extends AbstractForm
{
    /**
     * @var ShopTransactionErrorService
     */
    private $shopTransactionErrorService;

    /**
     * ShopTransactionErrorForm constructor.
     * @param ShopTransactionErrorService $shopTransactionErrorService
     * @param ShopTransactionErrorFormFactory $formFactory
     */
    public function __construct(
        ShopTransactionErrorService $shopTransactionErrorService,
        ShopTransactionErrorFormFactory $formFactory
    ) {
        $this->shopTransactionErrorService = $shopTransactionErrorService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP)) {
            return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
        }

        $update = $this->shopTransactionErrorService->updateStatus($submittedData);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        return $this->sendSuccessResponse(__('L\'impayé a bien été modifié.'));
    }
}
