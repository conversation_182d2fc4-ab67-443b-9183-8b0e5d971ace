<?php

namespace MatGyver\Forms\Shop;

use Mat<PERSON>yver\Forms\AbstractForm;
use Mat<PERSON>yver\FormsFactory\Shop\ShopQuoteFormFactory;
use MatGyver\Services\Dossier\DossierService;
use MatGyver\Services\RightsService;
use MatGyver\Services\Shop\Quotes\ShopQuotesService;

/**
 * Class ShopQuoteForm
 * @package MatGyver\Forms\Shop
 */
class ShopQuoteForm extends AbstractForm
{
    /**
     * @var DossierService
     */
    private $dossierService;

    /**
     * @var ShopQuotesService
     */
    private $shopQuotesService;

    /**
     * ShopQuoteForm constructor.
     * @param DossierService $dossierService
     * @param ShopQuotesService $shopQuotesService
     * @param ShopQuoteFormFactory $formFactory
     */
    public function __construct(
        DossierService $dossierService,
        ShopQuotesService $shopQuotesService,
        ShopQuoteFormFactory $formFactory
    ) {
        $this->dossierService = $dossierService;
        $this->shopQuotesService = $shopQuotesService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostInsert(array $submittedData): array
    {
        if ($_SESSION['controller'] == ADMIN_URL) {
            if (!RightsService::isGranted(ATTRIBUTE_CREATE, UNIVERSE_ADMIN_SHOP)) {
                return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
            }
        }

        if (!isset($submittedData['products'])) {
            return $this->sendErrorResponse([__('Vous devez ajouter au moins un produit.')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        if ($_SESSION['controller'] == ADMIN_URL) {
            if (!RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SHOP)) {
                return $this->sendErrorResponse([__('Vous n\'avez pas les autorisations nécessaires pour effectuer cette opération.')]);
            }
        }

        if (!isset($submittedData['id'])) {
            return $this->sendErrorResponse([__('Aucun numéro de devis défini.')]);
        }
        $quoteId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $quote = $this->shopQuotesService->getRepository()->find($quoteId);
        if (!$quote) {
            return $this->sendErrorResponse([__('Ce devis n\'existe pas.')]);
        }
        if ($quote->isSigned()) {
            return $this->sendErrorResponse([__('Ce devis est signé et ne peut donc être modifié.')]);
        }

        if (!isset($submittedData['products'])) {
            return $this->sendErrorResponse([__('Vous devez ajouter au moins un produit.')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $submittedData['id_client'] = $_SESSION['client']['id'];

        if (isset($submittedData['dossier_id'])) {
            $dossierId = filter_var($submittedData['dossier_id'], FILTER_VALIDATE_INT);
            $dossier = $this->dossierService->getRepository()->find($dossierId);
            if ($dossier) {
                $submittedData['dossier'] = $dossier;
            }
        }

        $insert = $this->shopQuotesService->insertQuote($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        $quote = $this->shopQuotesService->getRepository()->find($insert['quote_id']);

        if (isset($submittedData['observations']) and $submittedData['observations']) {
            $observations = filter_var($submittedData['observations'], FILTER_UNSAFE_RAW);
            $quote->setObservations($observations);
            try {
                $this->shopQuotesService->persistAndFlush($quote);
            } catch (\Exception $e) {
                return $this->sendErrorResponse([__('Impossible de mettre à jour le devis')]);
            }
        }

        return $this->sendSuccessResponse(__('Le devis a bien été enregistré.'), false, ['id' => $insert['quote_id']]);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $quoteId = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $quote = $this->shopQuotesService->getRepository()->find($quoteId);

        $number = filter_var($submittedData['number'], FILTER_UNSAFE_RAW);
        $prefix = filter_var($submittedData['prefix'], FILTER_UNSAFE_RAW);
        $checkQuoteNumber = $this->shopQuotesService->getQuoteByNumberAndPrefix($number, $prefix);
        if ($checkQuoteNumber and $checkQuoteNumber !== $quote) {
            return $this->sendErrorResponse([__('Un devis existe déjà avec ce numéro (%s)', $prefix . $number)]);
        }

        $update = $this->shopQuotesService->insertQuote($submittedData, $quote);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        if (isset($submittedData['observations']) and $submittedData['observations']) {
            $observations = filter_var($submittedData['observations'], FILTER_UNSAFE_RAW);
            $quote->setObservations($observations);
            try {
                $this->shopQuotesService->persistAndFlush($quote);
            } catch (\Exception $e) {
                return $this->sendErrorResponse([__('Impossible de mettre à jour le devis')]);
            }
        }

        return $this->sendSuccessResponse(__('Le devis a bien été modifié.'));
    }
}
