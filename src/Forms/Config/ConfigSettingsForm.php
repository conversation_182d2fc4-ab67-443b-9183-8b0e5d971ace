<?php
namespace Mat<PERSON><PERSON>ver\Forms\Config;

use Mat<PERSON><PERSON><PERSON>\Enums\ConfigEnum;
use MatGyver\Forms\AbstractForm;
use MatGyver\FormsFactory\Config\ConfigSettingsFormFactory;
use MatGyver\Services\ConfigService;

/**
 * Class ConfigSettingsForm
 * @package MatGyver\Forms\Config
 */
class ConfigSettingsForm extends AbstractForm
{
    /**
     * @var ConfigService
     */
    private $configService;

    /**
     * ConfigSettingsForm constructor.
     * @param ConfigService $configService
     * @param ConfigSettingsFormFactory $formFactory
     */
    public function __construct(
        ConfigService $configService,
        ConfigSettingsFormFactory $formFactory
    ) {
        $this->configService = $configService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $data = $this->getData($submittedData);

        $insert = $this->configService->saveConfig($data);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        return $this->sendSuccessResponse(__('Les paramètres ont bien été enregistrés.'));
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function getData(array $submittedData): array
    {
        $data = [
            ConfigEnum::LOGO => ''
        ];
        if (isset($submittedData[ConfigEnum::LOGO]) and $submittedData[ConfigEnum::LOGO]) {
            $data[ConfigEnum::LOGO] = filter_var($submittedData[ConfigEnum::LOGO], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::APP_LANGUAGE]) and $submittedData[ConfigEnum::APP_LANGUAGE]) {
            $data[ConfigEnum::APP_LANGUAGE] = filter_var($submittedData[ConfigEnum::APP_LANGUAGE], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::DEFAULT_TIMEZONE]) and $submittedData[ConfigEnum::DEFAULT_TIMEZONE]) {
            $data[ConfigEnum::DEFAULT_TIMEZONE] = filter_var($submittedData[ConfigEnum::DEFAULT_TIMEZONE], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::DEFAULT_CURRENCY]) and $submittedData[ConfigEnum::DEFAULT_CURRENCY]) {
            $data[ConfigEnum::DEFAULT_CURRENCY] = filter_var($submittedData[ConfigEnum::DEFAULT_CURRENCY], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::FISCAL_YEAR_START_DATE]) and $submittedData[ConfigEnum::FISCAL_YEAR_START_DATE]) {
            $data[ConfigEnum::FISCAL_YEAR_START_DATE] = filter_var($submittedData[ConfigEnum::FISCAL_YEAR_START_DATE], FILTER_UNSAFE_RAW);
        }
        if (isset($submittedData[ConfigEnum::FISCAL_YEAR_GOAL]) and $submittedData[ConfigEnum::FISCAL_YEAR_GOAL]) {
            $data[ConfigEnum::FISCAL_YEAR_GOAL] = filter_var($submittedData[ConfigEnum::FISCAL_YEAR_GOAL], FILTER_VALIDATE_INT);
        }

        return $data;
    }
}
