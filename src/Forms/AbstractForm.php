<?php

namespace MatGyver\Forms;

use DI\Attribute\Inject;
use MatGyver\Enums\FieldsEnum;
use Mat<PERSON><PERSON><PERSON>\FormsFactory\AbstractFormFactory;
use MatGyver\FormsFactory\Builder\BuilderFormGroup;
use MatGyver\Services\FlashBagService;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Validator\Validation;

class AbstractForm
{
    #[Inject]
    private ObjectHydrator $objectHydrator;

    #[Inject]
    private FlashBagService $flashBagService;

    private AbstractFormFactory $factory;

    /**
     * @return ObjectHydrator
     */
    public function getObjectHydrator(): ObjectHydrator
    {
        return $this->objectHydrator;
    }

    /**
     * @return FlashBagService
     */
    public function getFlashBagService(): FlashBagService
    {
        return $this->flashBagService;
    }

    /**
     * @return AbstractFormFactory
     */
    public function getFactory(): AbstractFormFactory
    {
        return $this->factory;
    }

    /**
     * @param AbstractFormFactory $factory
     */
    public function setFactory(AbstractFormFactory $factory): void
    {
        $this->factory = $factory;
    }

    /**
     * @return array
     */
    public function getFieldsParamsFromPost() :array
    {
        $factory = $this->getFactory();
        if (!$factory) {
            return [];
        }

        $fields = $factory->getBuilderFields()->getFields();
        $params = [];
        foreach ($fields as $field) {
            if ($field->getType() == FieldsEnum::TYPE_GROUP and $field instanceof BuilderFormGroup) {
                $groupFields = $field->getFields();
                foreach ($groupFields as $groupField) {
                    $params[$groupField->getName()] = $this->getPostValue($groupField->getName(), $groupField->getType());
                }
                continue;
            }
            $params[$field->getName()] = $this->getPostValue($field->getName(), $field->getType());
        }

        return $params;
    }

    /**
     * @param string $name
     * @param string $type
     * @return mixed
     */
    public function getPostValue(string $name, string $type)
    {
        $value = ($type == FieldsEnum::TYPE_INT ? 0 : '');
        if (!isset($_POST[$name])) {
            return $value;
        }

        switch ($type) {
            case FieldsEnum::TYPE_DATE:
            case FieldsEnum::TYPE_DATETIME:
                $value = filter_input(INPUT_POST, $name, FILTER_UNSAFE_RAW);
                try {
                    $value = new \DateTime($value);
                } catch (\Exception $e) {}
                break;
            case FieldsEnum::TYPE_EMAIL:
                $value = filter_input(INPUT_POST, $name, FILTER_VALIDATE_EMAIL);
                break;
            case FieldsEnum::TYPE_FLOAT:
                $value = filter_input(INPUT_POST, $name, FILTER_VALIDATE_FLOAT);
                break;
            case FieldsEnum::TYPE_INT:
                $value = filter_input(INPUT_POST, $name, FILTER_VALIDATE_INT);
                break;
            case FieldsEnum::TYPE_CKEDITOR:
            case FieldsEnum::TYPE_CKEDITOR_SMALL:
            case FieldsEnum::TYPE_TEXTAREA:
                $value = filter_input(INPUT_POST, $name, FILTER_UNSAFE_RAW);
                break;
            case FieldsEnum::TYPE_IMAGE:
            case FieldsEnum::TYPE_URL:
            case FieldsEnum::TYPE_VIDEO:
                $value = filter_input(INPUT_POST, $name, FILTER_VALIDATE_URL);
                break;
            default:
                $value = filter_input(INPUT_POST, $name, FILTER_UNSAFE_RAW);
                break;
        }

        return $value;
    }

    /**
     * @param array $errors
     * @param bool  $json
     * @param array $data
     * @return array|false|string
     */
    protected function sendResponse(array $errors = [], bool $json = false, array $data = [])
    {
        if (!empty($errors)) {
            return $this->sendErrorResponse($errors, $json, $data);
        }

        return $this->sendSuccessResponse(null, $json, $data);
    }

    /**
     * @param array $errors
     * @param bool $json
     * @param array $data
     * @return array|false|string
     */
    protected function sendErrorResponse(array $errors, bool $json = false, array $data = [])
    {
        foreach ($errors as $errorMessage) {
            $this->flashBagService->addError($errorMessage);
        }

        return $this->getResponse(false, $json, $data);
    }

    /**
     * @param string|null $message
     * @param bool $json
     * @param array $data
     * @return array|false|string
     */
    protected function sendSuccessResponse(string $message = null, bool $json = false, array $data = [])
    {
        if ($message !== null) {
            $this->flashBagService->addSuccess($message);
        }

        return $this->getResponse(true, $json, $data);
    }

    /**
     * @param bool  $valid
     * @param bool  $json
     * @param array $data
     * @return array|string
     */
    private function getResponse(bool $valid = true, bool $json = false, array $data = [])
    {
        $response = new ResponseForm();
        $response->setValid($valid);

        if (!empty($data)) {
            $response->setData($data);
        }

        if (!$valid) {
            $response->setMessage(implode('<br>', $this->flashBagService->get($this->flashBagService::ERROR_TYPE)));
        } else {
            $response->setMessage(implode('<br>', $this->flashBagService->get($this->flashBagService::SUCCESS_TYPE)));
        }

        if ($json) {
            $response->setIsJson(true);
        }

        return $response->getResponse();
    }

    /**
     * @param array $params
     * @param array $submittedData
     * @return array
     */
    protected function filterParamsFromPost(array $params, array $submittedData): array
    {
        return array_intersect_key($params, $submittedData);
    }

    /**
     * @param array $rules
     * @param array $postData
     * @return array
     */
    public function validateForm(array $rules, array $postData): array
    {
        $errors = [];

        $validator = Validation::createValidator();
        foreach ($rules as $field => $constraints) {
            if ($field === 'all') {
                $violations = $validator->validate($postData, $constraints);
            } elseif (isset($postData[$field])) {
                $violations = $validator->validate($postData[$field], $constraints);
            } else {
                $violations = $validator->validate(null, $constraints);
            }

            if (0 !== count($violations)) {
                foreach ($violations as $violation) {
                    $errors[] = sprintf($violation->getMessage(), $field);
                }
            }
        }

        return $errors;
    }

    /**
     * @param File $file
     * @param array $rules
     * @return array
     */
    public function validateFile(File $file, array $rules): array
    {
        $errors = [];
        $validator = Validation::createValidator();
        $violations = $validator->validate($file, $rules);
        if (0 !== count($violations)) {
            foreach ($violations as $violation) {
                $errors[] = $violation->getMessage();
            }
        }

        return $errors;
    }
}
