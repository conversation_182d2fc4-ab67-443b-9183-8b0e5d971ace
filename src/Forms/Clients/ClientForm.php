<?php
namespace Mat<PERSON><PERSON>ver\Forms\Clients;

use Mat<PERSON><PERSON><PERSON>\Entity\Client\Subscription\ClientSubscription;
use MatG<PERSON><PERSON>\Enums\ProductsEnum;
use MatG<PERSON>ver\Forms\AbstractForm;
use Mat<PERSON><PERSON>ver\FormsFactory\Client\ClientFormFactory;
use MatGyver\Helpers\Chars;
use MatGyver\Services\Clients\ClientsService;
use MatGyver\Services\Clients\ClientsSubscriptionsService;
use MatGyver\Services\Shop\Product\ShopProductsService;

/**
 * Class ClientForm
 * @package MatGyver\Forms\Clients
 */
class ClientForm extends AbstractForm
{
    /**
     * @var ClientsService
     */
    private $clientsService;

    /**
     * @var ClientsSubscriptionsService
     */
    private $clientsSubscriptionsService;

    /**
     * @var ShopProductsService
     */
    private $shopProductsService;

    /**
     * ClientForm constructor.
     * @param ClientsService $clientsService
     * @param ClientsSubscriptionsService $clientsSubscriptionsService
     * @param ShopProductsService $shopProductsService
     * @param ClientFormFactory $formFactory
     */
    public function __construct(
        ClientsService $clientsService,
        ClientsSubscriptionsService $clientsSubscriptionsService,
        ShopProductsService $shopProductsService,
        ClientFormFactory $formFactory
    ) {
        $this->clientsService = $clientsService;
        $this->clientsSubscriptionsService = $clientsSubscriptionsService;
        $this->shopProductsService = $shopProductsService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostInsert(array $submittedData): array
    {
        if (SUBDOMAIN_ENABLED) {
            $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
            if (!$uniqId) {
                return $this->sendErrorResponse([__('Veuillez entrer un lien')]);
            }

            $uniqId = permalink($uniqId);
            $client = $this->clientsService->getClientByUniqId($uniqId);
            if ($client) {
                return $this->sendErrorResponse([__('Un client existe déjà avec ce lien')]);
            }
            if (!$this->clientsService->isValidUniqId($uniqId)) {
                return $this->sendErrorResponse([__('Ce lien est protégé, merci d\'en utiliser un autre.')]);
            }
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function validatePostUpdate(array $submittedData): array
    {
        $idClient = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        if ($idClient == CLIENT_MASTER) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas')]);
        }
        $client = $this->clientsService->getClientById($idClient);
        if (!$client) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas')]);
        }

        if (SUBDOMAIN_ENABLED) {
            $uniqId = filter_var($submittedData['uniqid'], FILTER_UNSAFE_RAW);
            if (!$uniqId) {
                return $this->sendErrorResponse([__('Veuillez entrer un lien')]);
            }

            $uniqId = permalink($uniqId);
            if ($client->getUniqid() != $uniqId) {
                $getClient = $this->clientsService->getClientByUniqId($uniqId);
                if ($getClient and $getClient->getId() != $idClient) {
                    return $this->sendErrorResponse([__('Un client existe déjà avec ce lien')]);
                }
                if (!$this->clientsService->isValidUniqId($uniqId)) {
                    return $this->sendErrorResponse([__('Ce lien est protégé, merci d\'en utiliser un autre.')]);
                }
            }
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $subscription = '';
        if ($submittedData['subscription']) {
            $productId = filter_var($submittedData['subscription'], FILTER_VALIDATE_INT);
            $product = $this->shopProductsService->getProductById($productId);
            if ($product) {
                $subscription = $product->getName();
            }
        }

        $submittedData['subscription'] = $subscription;

        if (!SUBDOMAIN_ENABLED) {
            $submittedData['uniqid'] = Chars::generateUniqid();
        }

        $insert = $this->clientsService->insertClient($submittedData);
        if (!$insert['valid']) {
            return $this->sendErrorResponse([$insert['message']]);
        }

        $idClient = $insert['client_id'];
        $client = $this->clientsService->getClientById($idClient);

        if (isset($product) and $product) {
            $data = [
                'client_id' => $insert['client_id'],
                'product_id' => $product->getId(),
                'product_name' => $product->getName(),
                'subscription_id' => 0,
                'type_subscription' => '',
                'transaction_reference' => '',
                'customer_id' => null,
                'amount_tax_excl' => $product->getPriceTaxExcl(),
                'amount_tax_incl' => $product->getPriceTaxExcl(),
            ];
            $this->clientsSubscriptionsService->insertSubscription($data);

            $client->setFreemium(false);
            if ($product->getType() == ProductsEnum::TYPE_FREEMIUM) {
                $client->setFreemium(true);
            }
            try {
                $this->clientsService->persistAndFlush($client);
            } catch (\Exception $e) {
                return $this->sendErrorResponse([__('Une erreur est survenue lors de la mise à jour du client')]);
            }
        }

        return $this->sendSuccessResponse(null, false, ['id_client' => $insert['client_id']]);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function update(array $submittedData): array
    {
        $idClient = filter_var($submittedData['id'], FILTER_VALIDATE_INT);
        $client = $this->clientsService->getClientById($idClient);
        if (!$client) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas.')]);
        }

        if ($submittedData['subscription']) {
            $productId = filter_var($submittedData['subscription'], FILTER_VALIDATE_INT);
            $product = $this->shopProductsService->getProductById($productId);
            if ($product) {
                $submittedData['subscription'] = $product->getName();
            }
        }

        $update = $this->clientsService->updateClient($submittedData);
        if (!$update['valid']) {
            return $this->sendErrorResponse([$update['message']]);
        }

        if (isset($product) and $product) {
            $hasSubscriptionActive = false;
            $clientSubscriptions = $this->clientsSubscriptionsService->getRepository()->findBy(['client' => $idClient], ['id' => 'DESC']);
            if ($clientSubscriptions) {
                foreach ($clientSubscriptions as $clientSubscription) {
                    if ($clientSubscription->getStatus() == ClientSubscription::STATUS_ACTIVE and $clientSubscription->getProduct() and $clientSubscription->getProduct() === $product) {
                        $hasSubscriptionActive = true;
                        break;
                    }
                }
            }

            if (!$hasSubscriptionActive) {
                $data = [
                    'client_id' => $idClient,
                    'product_id' => $product->getId(),
                    'product_name' => $product->getName(),
                    'subscription_id' => 0,
                    'type_subscription' => '',
                    'transaction_reference' => '',
                    'customer_id' => null,
                    'amount_tax_excl' => $product->getPriceTaxExcl(),
                    'amount_tax_incl' => $product->getPriceTaxExcl(),
                ];
                $this->clientsSubscriptionsService->insertSubscription($data);

                $client->setFreemium(false);
                if ($product->getType() == ProductsEnum::TYPE_FREEMIUM) {
                    $client->setFreemium(true);
                }
                try {
                    $this->clientsService->persistAndFlush($client);
                } catch (\Exception $e) {
                    return $this->sendErrorResponse([__('Une erreur est survenue lors de la mise à jour du client')]);
                }
            }
        }

        return $this->sendSuccessResponse();
    }
}
