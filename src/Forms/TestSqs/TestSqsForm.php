<?php

namespace MatG<PERSON>ver\Forms\TestSqs;

use Mat<PERSON>yver\Forms\AbstractForm;
use MatGyver\FormsFactory\TestSqs\TestSqsFormFactory;
use MatGyver\Services\Aws\Sqs\MessageTestService;

/**
 * Class TestSqsForm
 * @package MatGyver\Forms
 */
class TestSqsForm extends AbstractForm
{
    /**
     * @var MessageTestService
     */
    private $messageTestService;

    /**
     * TestSqsForm constructor.
     * @param MessageTestService $messageTestService
     * @param TestSqsFormFactory $formFactory
     */
    public function __construct(
        MessageTestService $messageTestService,
        TestSqsFormFactory $formFactory
    ) {
        $this->messageTestService = $messageTestService;
        $this->setFactory($formFactory);
    }

    /**
     * @param array $submittedData
     * @return array
     */
    public function insert(array $submittedData): array
    {
        $process = $this->messageTestService->process($submittedData);
        if (!$process['valid']) {
            return $this->sendErrorResponse([$process['message']]);
        }

        return $this->sendSuccessResponse(__('Action exécutée avec succès'), false, ['id' => $process['id_log']]);
    }
}
