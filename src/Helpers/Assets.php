<?php

namespace MatGyver\Helpers;

/**
 * Class Assets.
 */
class Assets
{
    /**
     * @var array
     */
    private static $css = [];

    /**
     * @var array
     */
    private static $inlineCss = [];

    /**
     * @var array
     */
    private static $js = [];

    /**
     * @var array
     */
    private static $jsHead = [];

    /**
     * @var array
     */
    private static $inlineJs = [];

    /**
     * @var array
     */
    private static $inlineJsHead = [];

    /**
     * @param string      $filepath
     * @param null|string $folder
     */
    public static function addCss(string $filepath, ?string $folder = 'assets/css/'): void
    {
        if (!str_contains($filepath, '//')) {
            $filepath = CDN_URL . $folder . $filepath . self::getVersion();
        }
        self::$css[md5($filepath)] = $filepath;
    }

    /**
     * @param string $code
     */
    public static function addInlineCss(string $code): void
    {
        self::$inlineCss[] = $code;
    }

    /**
     * @return array
     */
    public static function getCss(): array
    {
        return self::$css;
    }

    public static function renderCss(): void
    {
        foreach (self::getCss() as $css) {
            echo '<link rel="stylesheet" type="text/css" href="' . $css . '" />' . "\n";
        }
        if (!empty(self::$inlineCss)) {
            echo '<style>';
            foreach (self::$inlineCss as $inlineCss) {
                echo strip_tags($inlineCss);
            }
            echo '</style>';
        }
    }

    /**
     * @param string $filepath
     * @param string|null $folder
     * @param bool $head
     */
    public static function addJs(string $filepath, ?string $folder = 'assets/js/', bool $head = false): void
    {
        if (!str_contains($filepath, '//')) {
            $filepath = CDN_URL . $folder . $filepath . self::getVersion();
        }

        if ($head) {
            self::$jsHead[md5($filepath)] = $filepath;
        } else {
            self::$js[md5($filepath)] = $filepath;
        }
    }

    /**
     * @param bool $head
     * @return array
     */
    public static function getJs(bool $head = false): array
    {
        return $head ? self::$jsHead : self::$js;
    }

    /**
     * @param bool $head
     */
    public static function renderJs(bool $head = false): void
    {
        foreach (self::getJs($head) as $js) {
            echo '<script src="' . $js . '"></script>' . "\n";
        }
    }

    /**
     * @param string $code
     * @param bool   $head
     */
    public static function addInlineJs(string $code, bool $head = false): void
    {
        if ($head) {
            self::$inlineJsHead[] = $code;
        } else {
            self::$inlineJs[] = $code;
        }
    }

    /**
     * @param bool $head
     *
     * @return array
     */
    public static function getInlineJs(bool $head = false): array
    {
        return $head ? self::$inlineJsHead : self::$inlineJs;
    }

    /**
     * @param bool $head
     */
    public static function renderInlineJs(bool $head = false): void
    {
        foreach (self::getInlineJs($head) as $js) {
            echo $js;
        }
    }

    /**
     * @param string $filepath
     * @param string|null $folder
     * @return string
     */
    public static function getUrl(string $filepath, ?string $folder = CDN_URL): string
    {
        return $folder . $filepath;
    }

    /**
     * @param string $filepath
     * @param string|null $folder
     * @return string
     */
    public static function getImageUrl(string $filepath, ?string $folder = 'assets/images/'): string
    {
        return self::getUrl($filepath . self::getVersion(), CDN_URL . $folder);
    }

    /**
     * @param string $filepath
     * @param string|null $clientUniqid
     * @return string
     */
    public static function getClientImageUrl(string $filepath, string $clientUniqid = null): string
    {
        if ($clientUniqid === null) {
            $clientUniqid = $_SESSION['client_uniqid'];
        }
        return self::getUrl($filepath, MEDIAS_URL . $clientUniqid . '/');
    }

    /**
     * @param string $filepath
     * @param string|null $folder
     * @return string
     */
    public static function getMediaUrl(string $filepath, ?string $folder = MEDIAS_URL): string
    {
        if (SUBDOMAIN_ENABLED and str_contains($folder, MEDIAS_URL)) {
            $folder = str_replace(MEDIAS_URL, '', $folder);
            $clientUniqid = $_SESSION['client_uniqid'];
            return 'https://' . $clientUniqid . '.' . APP_DOMAIN . '/medias/' . $folder . $filepath;
        }

        return self::getUrl($filepath, $folder);
    }

    /**
     * @param string $filepath
     * @param string|null $clientUniqid
     * @return string
     */
    public static function getClientMediaUrl(string $filepath, string $clientUniqid = null): string
    {
        if ($clientUniqid === null) {
            $clientUniqid = $_SESSION['client_uniqid'];
        }
        if (SUBDOMAIN_ENABLED) {
            return 'https://' . $clientUniqid . '.' . APP_DOMAIN . '/medias/' . $filepath;
        }
        return self::getUrl($filepath, MEDIAS_URL . $clientUniqid . '/');
    }

    /**
     * Reset CSS / JS
     */
    public static function reset()
    {
        self::$css = [];
        self::$inlineCss = [];
        self::$js = [];
        self::$jsHead = [];
        self::$inlineJs = [];
        self::$inlineJsHead = [];
    }

    /**
     * @return string
     */
    public static function getVersion(): string
    {
        return '?v=' . (defined('ASSETS_VERSION') ? ASSETS_VERSION : 0);
    }
}
