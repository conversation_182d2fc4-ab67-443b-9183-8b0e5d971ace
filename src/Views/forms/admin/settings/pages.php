<?php
$pages = [
    [
        'id' => 'site_index',
        'nom' => __('Page d\'accueil'),
        'description' => __('Sélectionnez votre page d\'accueil, celle qui apparaîtra à l\'adresse') . ' <a href="' . $indexUrl . '" target="_blank">' . $indexUrl . '</a>',
        'editor' => false,
    ],
    [
        'id' => 'site_contact',
        'nom' => __('Page de contact'),
        'description' => __('Sélectionnez votre page de contact.'),
        'editor' => false,
    ],
    [
        'id' => 'site_tos',
        'nom' => __('Page de conditions générales de vente'),
        'description' => __('Sélectionnez votre page de conditions générales de vente'),
        'editor' => true,
    ],
    [
        'id' => 'site_legal_notice',
        'nom' => __('Page de mentions légales'),
        'description' => __('Editez la page de mentions légales'),
        'editor' => true,
    ],
    [
        'id' => 'site_privacy',
        'nom' => __('Page de politique de confidentialité'),
        'description' => __('Editez la page de politique de confidentialité'),
        'editor' => true,
    ],
    [
        'id' => 'site_dpa',
        'nom' => __('Page de traitement des données'),
        'description' => __('Editez la page de traitement des données'),
        'editor' => true,
    ],
    [
        'id' => 'unsubscribe',
        'nom' => __('Page de désinscription des emails'),
        'description' => __('Personnalisez le design de la page de désinscription'),
        'editor' => true,
    ],
    [
        'id' => 'affiliation_inscription',
        'nom' => __('Page d\'inscription pour les partenaires'),
        'description' => __('Personnalisez le design de la page d\'inscription pour les partenaires'),
        'editor' => true,
    ],
];

foreach ($pages as $id => $page) {
    if ($page['editor']) {
        continue;
    }
    $pages[$id]['url'] = '';
    if (isset($config[$page['id']])) {
        $pages[$id]['url'] = stripslashes($config[$page['id']]);
    }
}
?>
<form class="form" method="post" action="">
    <div class="card card-custom card-sticky gutter-b" id="kt_page_sticky_card">
        <div class="card-header">
            <div class="card-title">
                <h3 class="card-label"><?php echo __('Pages de votre site'); ?></h3>
            </div>
            <div class="card-toolbar">
                <button type="submit" name="submit" class="btn btn-primary font-weight-bolder"><?php echo __('Valider'); ?></button>
            </div>
        </div>
        <div class="card-body">
            <table class="table table-separate table-head-custom" style="margin-bottom: 0">
                <tbody>
                <?php foreach ($pages as $page) : ?>
                    <tr>
                        <td>
                            <label class="control-label" for="<?php echo $page['id']; ?>" style="margin-bottom: 0"><?php echo $page['nom']; ?></label>
                        </td>
                        <td>
                            <?php if ($page['editor']) : ?>
                                <div class="form-group mb-0">
                                    <a class="btn btn-default" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'page_editor', $page['id']); ?>"><?php echo __('Personnaliser'); ?></a>
                                </div>
                            <?php else : ?>
                                <div class="form-group mb-0">
                                    <input class="form-control" type="url" name="pages[<?php echo $page['id']; ?>]" value="<?php echo $page['url']; ?>" placeholder="https://">
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Pied de page'); ?></h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="control-label" for="ckeditorsmall"><?php echo __('Colonne n°1'); ?></label>
                <div class="controls">
                    <textarea class="ckeditor" name="footer_column1"
                              id="ckeditorsmall"><?php if (isset($footer['column1'])) {
                            echo $footer['column1'];
                        } ?></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="ckeditorsmall2"><?php echo __('Colonne n°2'); ?></label>
                <div class="controls">
                    <textarea class="ckeditor" name="footer_column2"
                              id="ckeditorsmall2"><?php if (isset($footer['column2'])) {
                            echo $footer['column2'];
                        } ?></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="ckeditorsmall3"><?php echo __('Colonne n°3'); ?></label>
                <div class="controls">
                    <textarea class="ckeditor" name="footer_column3"
                              id="ckeditorsmall3"><?php if (isset($footer['column3'])) {
                            echo $footer['column3'];
                        } ?></textarea>
                </div>
            </div>
        </div>
    </div>

    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Tracking'); ?></h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="control-label" for="facebook_pixel_id"><?php echo __('Pixel Facebook'); ?></label>
                <div class="controls">
                    <?php if (!$selectFacebookPixel) : ?>
                        <div class="alert alert-custom alert-light-info"><?php echo __('Aucun pixel Facebook enregistré'); ?></div>
                    <?php else : ?>
                        <select class="form-control" name="facebook_pixel_id" id="facebook_pixel_id">
                            <?php echo $selectFacebookPixel; ?>
                        </select>
                    <?php endif; ?>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="google_analytics_id"><?php echo __('Identifiant Google Analytics'); ?></label>
                <div class="controls">
                    <?php if (!$selectGoogleAnalytics) : ?>
                        <div class="alert alert-custom alert-light-info"><?php echo __('Aucun identifiant Google Analytics enregistré'); ?></div>
                    <?php else : ?>
                        <select class="form-control" name="google_analytics_id" id="google_analytics_id">
                            <?php echo $selectGoogleAnalytics; ?>
                        </select>
                    <?php endif; ?>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="google_tag_manager_id"><?php echo __('Identifiant Google Tag Manager'); ?></label>
                <div class="controls">
                    <?php if (!$selectGoogleTagManager) : ?>
                        <div class="alert alert-custom alert-light-info"><?php echo __('Aucun identifiant Google Tag Manager enregistré'); ?></div>
                    <?php else : ?>
                        <select class="form-control" name="google_tag_manager_id" id="google_tag_manager_id">
                            <?php echo $selectGoogleTagManager; ?>
                        </select>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="redirect" value="<?php echo $redirect; ?>"/>
    </div>
</form>
