<?php
$container = \MatGyver\Services\DI\ContainerBuilderService::getInstance();
?>
<div class="form-group">
    <label class="control-label" for="account_id"><?php echo __('Compte'); ?> *</label>
    <div class="controls">
        <select name="account_id" id="account_id" data-rel="select2">
            <?php echo $selectAccount; ?>
        </select>
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="sg_idliste"><?php echo __('Liste'); ?> *</label>
    <div class="controls lists-container">
        <?php echo $container->get(\MatGyver\Services\Autoresponders\Types\AutoresponderSgService::class)->generateSelectLists($idAccount, $fields['sg_idliste']['value']); ?>
    </div>
</div>

<?php if ('product' == $type and isset($shopProducts)) : ?>
    <hr>
    <h4><?php echo __('Produits'); ?></h4>
    <?php foreach ($shopProducts as $shopProduct) : ?>
        <?php $value = ($fields['sg_shop_products'][$shopProduct['id']] ?? ''); ?>
        <div class="form-group">
             <label class="control-label" for="sg_shop_products<?php echo $shopProduct['id']; ?>"><?php echo $shopProduct['name']; ?></label>
             <div class="controls lists-container">
                 <?php $selectListeGroupe = $container->get(\MatGyver\Services\Autoresponders\Types\AutoresponderSgService::class)->generateSelectLists($idAccount, $value, false); ?>
                 <?php echo str_replace('<select name="sg_idliste"', '<select name="sg_shop_products[' . $shopProduct['id'] . ']"', $selectListeGroupe); ?>
             </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
