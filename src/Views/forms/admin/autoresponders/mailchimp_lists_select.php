<?php
if (!isset($lists)) {
    $lists = [];
}
if (!isset($activeList)) {
    $activeList = '';
}
if (!isset($activeGrouping)) {
    $activeGrouping = '';
}
$i = 0;
?>
<?php if (!$lists) : ?>
    <div class="alert alert-primary mb-5 p-5">
        <p><?php echo __('Aucune liste trouvée.'); ?></p>
        <div class="border-bottom border-white opacity-20 mb-5"></div>
        <a class="btn btn-sm btn-white btn-get-lists" onclick="updateSelect('lists', 'mailchimp', true);">
            <?php echo __('Récupérer les listes'); ?>
        </a>
    </div>
    <?php return; ?>
<?php endif; ?>

<select name="mc_liste" onchange="DisplayGroups(this.value)" data-rel="select2">
    <option value=""><?php echo __('<PERSON>sis<PERSON>z une liste'); ?></option>
    <?php foreach ($lists as $list) : ?>
        <option value="<?php echo $list['id']; ?>" <?php echo (($activeList and $activeList == $list['id']) ? 'selected="selected"' : ''); ?>><?php echo $list['name']; ?></option>
    <?php endforeach; ?>
</select>
<a class="btn btn-icon btn-clean btn-sm btn-get-lists" onclick="updateSelect('lists', 'mailchimp');"><i class="fas fa-sync-alt"></i></a>

<?php foreach ($lists as $list) : ?>
    <?php if (isset($list['groupings']) and $list['groupings']) : ?>
        <div class="mc_group mt-5" id="groupings<?php echo $list['id']; ?>" style="display:<?php echo (($activeList and $activeList == $list['id']) ? 'block' : 'none'); ?>">
            <?php foreach ($list['groupings'] as $grouping) : ?>
                <div class="font-size-sm text-primary font-weight-bolder text-uppercase mb-2"><?php echo $grouping['name']; ?></div>
                <?php if (isset($grouping['groups']) and $grouping['groups']) : ?>
                    <?php foreach ($grouping['groups'] as $group) : ?>
                        <div class="checkbox">
                            <label for="groupings_<?php echo $grouping['id']; ?>_<?php echo $i; ?>">
                                <input type="checkbox" name="groupings[<?php echo $grouping['id']; ?>][]" id="groupings_<?php echo $grouping['id']; ?>_<?php echo $i; ?>" value="<?php echo $group['name']; ?>" <?php echo (($activeGrouping and isset($activeGrouping[$grouping['id']]) and in_array($group['name'], $activeGrouping[$grouping['id']])) ? 'checked' : ''); ?>> <?php echo $group['name']; ?>
                            </label>
                        </div>
                        <?php $i++; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
<?php endforeach; ?>
