<?php
$gdpr = $product->getGdpr();
if ($gdpr) {
    $gdpr = json_decode($gdpr, true);
}
?>
<form class="form" method="post" action="">

    <div class="card card-custom card-sticky gutter-b" id="kt_page_sticky_card">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Choix des champs du produit'); ?></h3>
            <div class="card-toolbar">
                <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'product/' . $product->getId()); ?>" class="btn btn-light-primary font-weight-bolder mr-2"><i class="ki ki-long-arrow-back icon-xs"></i><?php echo __('Annuler'); ?></a>
                <button type="submit" name="submit" class="btn btn-primary font-weight-bolder"><?php echo __('Valider'); ?></button>
            </div>
        </div>

        <div class="card-body">
            <table class="table table-separate table-head-custom table-hovered">
                <thead>
                    <tr>
                        <th><?php echo __('Activer'); ?></th>
                        <th><?php echo __('Obligatoire'); ?></th>
                        <th><?php echo __('Nom'); ?></th>
                        <th><?php echo __('Valeur par défaut'); ?></th>
                        <th><?php echo __('Position'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $i = 0;
                    foreach ($fields as $name => $field) {
                        ++$i;

                        $disabled = false;
                        if ('last_name' == $name or 'first_name' == $name or 'email' == $name) {
                            $disabled = true;
                        }

                        echo '<tr>';

                        if ($disabled) {
                            echo '
                            <td>
                                <input type="hidden" name="fields[' . $name . '][active]" value="on" checked>
                                <div class="checkbox-list">
                                    <label class="checkbox checkbox-disabled m-auto">
                                        <input type="checkbox" checked disabled>
                                        <span></span>
                                    </label>
                                </div>
                            </td>
                            <td>
                                <input type="hidden" name="fields[' . $name . '][required]" value="on" checked>
                                <div class="checkbox-list">
                                    <label class="checkbox checkbox-disabled m-auto">
                                        <input type="checkbox" checked disabled>
                                        <span></span>
                                    </label>
                                </div>
                            </td>';
                        } else {
                            echo '
                            <td style="text-align:center">
                                <div class="checkbox-list">
                                    <label class="checkbox m-auto">
                                        <input type="checkbox" name="fields[' . $name . '][active]" id="fields-' . $name . '-active" ' . ((isset($field['active']) and $field['active']) ? 'checked' : '') . '>
                                        <span></span>
                                    </label>
                                </div>
                            </td>
                            <td>
                                <div class="checkbox-list">
                                    <label class="checkbox m-auto">
                                        <input type="checkbox" name="fields[' . $name . '][required]" id="fields-' . $name . '-required" ' . ((isset($field['required']) and $field['required']) ? 'checked' : '') . '>
                                        <span></span>
                                    </label>
                                </div>
                            </td>';
                        }

                        echo '
                            <td><label for="fields-' . $name . '-active">' . $field['name'] . '</label></td>    
                            <input type="hidden" name="fields[' . $name . '][name]" value="' . $field['name'] . '">
                            <input type="hidden" name="fields[' . $name . '][type]" value="' . $field['type'] . '">';

                        echo '
                            <td>
                                <input class="form-control input-xlarge" type="text" name="fields[' . $name . '][placeholder]" id="fields-' . $name . '-placeholder" value="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : '') . '">';

                        if ('country' == $name) {
                            $defaultValue = '';
                            if (isset($field['value'])) {
                                $defaultValue = $field['value'];
                            }

                            $selectCountry = \MatGyver\Helpers\Country::generateSelectCountry($defaultValue);
                            echo '<select name="fields[' . $name . '][value]" style="width:100%" data-rel="select2">' . $selectCountry . '</select>';
                        }

                        echo '
                            </td>
                            <td>                
                                <input class="form-control input-small" type="number" min="0" name="fields[' . $name . '][position]" id="fields-' . $name . '-position" value="' . ($field['position'] ?? $i) . '">
                            </td>
                        </tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Certification RGPD'); ?></h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <div class="switch-left">
                    <span class="switch">
                        <label for="gdpr">
                            <input type="checkbox" name="gdpr" id="gdpr" class="ios-toggle" data-no-uniform="true" <?php if (isset($gdpr['gdpr']['active']) and $gdpr['gdpr']['active']) echo 'checked'; ?>>
                            <span></span>
                        </label>
                    </span>
                </div>
                <div class="switch-right">
                    <div class="switch-infos">
                        <label class="control-label" for="gdpr"><?php echo __('Certification RGPD'); ?></label>
                        <br>
                        <span class="form-text text-muted"><?php echo __('Cochez cette case pour demander à vos prospects leur autorisation pour leur envoyer des emails commerciaux.'); ?></span>
                        <?php
                        $gdpr_txt = __("En indiquant votre adresse mail, vous acceptez de recevoir des offres commerciales de notre part. Vous pouvez vous désinscrire à tout moment en nous adressant un mail et à travers les liens de désinscription");
                        if (isset($gdpr['gdpr']['txt']) and $gdpr['gdpr']['txt']) {
                            $gdpr_txt = $gdpr['gdpr']['txt'];
                        }
                        ?>
                        <br>
                        <div class="form-group">
                            <textarea class="form-control" name="gdpr_txt"><?php echo $gdpr_txt; ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="switch-left">
                    <span class="switch">
                        <label for="gdpr_aff">
                            <input type="checkbox" name="gdpr_aff" id="gdpr_aff" class="ios-toggle" data-no-uniform="true" <?php if (isset($gdpr['gdpr']['aff']['active']) and $gdpr['gdpr']['aff']['active']) echo 'checked'; ?>>
                            <span></span>
                        </label>
                    </span>
                </div>
                <div class="switch-right">
                    <div class="switch-infos">
                        <label class="control-label" for="gdpr_aff"><?php echo __('Certification RGPD (Partenaires)'); ?></label>
                        <br>
                        <span class="form-text text-muted"><?php echo __('Cochez cette case pour demander à vos prospects leur autorisation pour leur envoyer des emails commerciaux de vos partenaires.'); ?></span>
                        <br>
                        <?php
                        $gdpr_aff_txt = __("En indiquant votre adresse mail, vous acceptez de recevoir des offres commerciales de nos partenaires.");
                        if (isset($gdpr['aff']['txt']) and $gdpr['aff']['txt']) {
                            $gdpr_aff_txt = $rgpd_aff['txt'];
                        }
                        ?>
                        <div class="form-group">
                            <textarea class="form-control" name="gdpr_aff_txt"><?php echo $gdpr_aff_txt; ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Questions supplémentaires'); ?></h3>
        </div>
        <div class="card-body">
            <div class="alert alert-outline-info"><?php echo __('Lorsque vos clients rempliront le formulaire d\'inscription, vous pouvez leur poser une question en cliquant sur le bouton "Ajouter une question" ci-dessous.'); ?></div>

            <div id="customField" style="margin-bottom: 10px">
                <table class="table table-separate table-head-custom" id="table_custom_fields">
                    <tbody>
                        <?php if (isset($customFields) and $customFields) : ?>
                            <?php echo $customFields; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="controls">
                <a class="btn btn-secondary" onclick="$('#ModalCreateField').modal('show');"><i class="fa fa-plus"></i> <?php echo __('Ajouter un renseignement'); ?></a>
            </div>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="redirect" value="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'product/' . $product->getId()); ?>" />
        <input type="hidden" name="id" value="<?php echo $product->getId(); ?>">
    </div>
</form>
