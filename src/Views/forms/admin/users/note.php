<div class="btn-add-note-form mb-10" style="text-align: right">
    <a class="btn btn-sm btn-light-primary font-weight-bolder" onclick="ShowNoteForm();"><?php echo __('Ajouter une note'); ?></a>
</div>
<div class="tab-notes-form">
    <form class="form form-note" method="post" action="" id="create_note">
        <div class="form-group">
            <textarea class="form-control form-control-solid" name="content" id="note_content" rows="4" placeholder="<?php echo __('Ecrivez votre note ici et mentionnez un administrateur avec @'); ?>"></textarea>
        </div>
        <div class="footer d-flex justify-content-between flex-wrap">
            <div class="footer-left">
                <div class="labels">
                    <?php foreach ($labels as $label) : ?>
                    <div class="label label-<?php echo $label; ?> <?php echo (!$label ? 'active' : ''); ?>" data-label="<?php echo $label; ?>">
                        <div class="label-inside"></div>
                    </div>
                    <?php endforeach; ?>
                    <input type="hidden" id="label" value="">
                </div>
            </div><div class="footer-right">
                <input type="hidden" name="user_id" id="user_id" value="<?php echo $userId; ?>" />
                <a class="btn btn-clean font-weight-bold" id="btn-cancel" onclick="HideNoteForm();"><?php echo __('Annuler'); ?></a>
                <a class="btn btn-light-primary font-weight-bold" id="btn-validate" onclick="AddNote();"><?php echo __('Ajouter la note'); ?></a>
            </div>
        </div>
    </form>
    <div class="separator separator-dashed my-10"></div>
</div>
