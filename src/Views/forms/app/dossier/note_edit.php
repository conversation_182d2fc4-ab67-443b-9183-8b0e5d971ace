<?php /** @var \MatGyver\Entity\Dossier\DossierNote $note */ ?>
<div class="edit-note" data-note="<?php echo $note->getId(); ?>">
    <form class="form form-note" method="post" action="" id="update_note">
        <div class="form-group">
            <textarea class="form-control form-control-solid" name="content" id="note_content" rows="4" placeholder="<?php echo __('Ecrivez votre note ici et mentionnez un administrateur avec @'); ?>"><?php echo $note->getContent(); ?></textarea>
        </div>
        <div class="footer d-flex justify-content-between flex-wrap">
            <div class="footer-left">
                <div class="labels">
                    <?php foreach ($labels as $label) : ?>
                    <div class="label label-<?php echo $label; ?> <?php echo ($note->getLabel() == $label ? 'active' : ''); ?>" data-label="<?php echo $label; ?>">
                        <div class="label-inside"></div>
                    </div>
                    <?php endforeach; ?>
                    <input type="hidden" id="label" value="<?php echo $note->getLabel(); ?>">
                </div>
            </div>
            <div class="footer-right">
                <input type="hidden" name="dossier_id" id="dossier_id" value="<?php echo $note->getDossier()->getId(); ?>" />
                <a class="btn btn-clean font-weight-bold" id="btn-cancel" onclick="HideEditNoteForm();"><?php echo __('Annuler'); ?></a>
                <a class="btn btn-light-primary font-weight-bold" id="btn-validate" onclick="UpdateNote(<?php echo $note->getId(); ?>);"><?php echo __('Modifier la note'); ?></a>
            </div>
        </div>
    </form>
</div>
