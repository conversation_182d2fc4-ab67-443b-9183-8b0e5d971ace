<?php $target = $data['target'] ?? ''; ?>
<div class="form-group">
    <label class="control-label <?php echo ($help ? 'mb-0' : ''); ?>" for="<?php echo $id; ?>"><?php echo $label . ($required ? ' *' : ''); ?></label>
    <?php if ($help) : ?>
        <span class="form-text text-muted mb-4"><?php echo $help ?></span>
    <?php endif; ?>

    <div class="recorder" id="<?php echo $id; ?>" data-target="<?php echo $target; ?>">
        <div class="recorder-container d-flex flex-column">
            <div class="d-flex flex-row justify-content-between align-items-center">
                <div id="btns">
                    <a class="btn btn-clean btn-icon recordButton" id="record"><i class="fas fa-circle text-danger"></i></a>
                    <a class="btn btn-clean btn-icon stopButton d-none" id="stop"><i class="fas fa-stop text-warning"></i></a>
                </div>
                <div id="canvas-container">
                    <canvas id="level" height="40" width="300"></canvas>
                </div>
                <div id="audio-source">
                    <select id="audio-in-select" class="form-control"></select>
                </div>
            </div>
            <div id="result"></div>
        </div>
    </div>
</div>
