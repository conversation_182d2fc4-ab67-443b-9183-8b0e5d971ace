<?php /** @var \MatGyver\FormsFactory\Builder\BuilderFormGroup $group */ ?>
<?php if ($group->isGrid()) : ?>
    <div class="form-group-grid form-group-grid-<?php echo $group->getNbFields() ?: count($group->getFields()); ?> <?php echo $group->getClass(); ?>">
        <?php echo $content; ?>
    </div>
    <?php return; ?>
<?php endif; ?>

    </div>
</div>

<div class="card card-custom <?php echo (!$group->isOpened() ? 'card-collapsed' : ''); ?> gutter-b" data-card="true" id="card<?php echo $group->getId(); ?>">
    <div class="card-header">
        <div class="card-title">
            <?php if ($group->getIcon()) : ?>
                <div class="card-icon-bg">
                    <?php if (str_starts_with($group->getIcon(), 'fa')) : ?>
                        <i class="<?php echo $group->getIcon(); ?>"></i>
                    <?php else : ?>
                        <?php echo $group->getIcon(); ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            <h3 class="card-label"><?php echo $group->getLabel(); ?></h3>
        </div>
        <div class="card-toolbar">
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="<?php echo __('Afficher/Cacher'); ?>">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php echo $content; ?>
