<form class="form <?php echo $formClass; ?>" method="post" action="" <?php echo (isset($enctype) ? 'enctype="' . $enctype . '"' : ''); ?> id="form">
    <?php if ($inPanel) : ?>
        <div class="form-panel">
            <div class="form-panel-content">
    <?php endif; ?>
    <div class="card card-custom <?php echo (!$inPanel ? 'card-sticky' : ''); ?> gutter-b" <?php echo (!$inPanel ? 'id="kt_page_sticky_card"' : ''); ?>>
        <div class="card-header <?php echo ($navigation ? 'card-header-tabs-line' : ''); ?>">
            <?php if ($navigation) : ?>
                <div class="card-toolbar">
                    <?php echo $navigation; ?>
                </div>
            <?php elseif ($title) : ?>
                <div class="card-title">
                    <?php if ($icon) : ?>
                    <div class="card-icon-bg">
                        <?php if (str_starts_with($icon, 'fa')) : ?>
                            <i class="<?php echo $icon; ?>"></i>
                        <?php else : ?>
                            <?php echo $icon; ?>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <h3 class="card-label"><?php echo $title; ?></h3>
                </div>
            <?php endif; ?>

            <div class="card-toolbar">
                <?php if ($cancelUrl) : ?>
                    <a href="<?php echo $cancelUrl; ?>" class="btn btn-light-primary btn-sm font-weight-bolder mr-2"><i class="ki ki-long-arrow-back icon-xs"></i><?php echo $btnCancelText ?: __('Annuler'); ?></a>
                <?php endif; ?>
                <button type="submit" name="submit" class="btn btn-sm <?php echo ($btnValidateClass ?: 'btn-primary'); ?> font-weight-bolder"><?php echo $btnValidateText ?: __('Valider'); ?></button>
            </div>
        </div>

        <div class="card-body">
            <?php if ($headerText) : ?>
                <div class="alert alert-custom alert-white alert-shadow fade show gutter-b" role="alert">
                    <div class="alert-icon">
                        <span class="svg-icon svg-icon-primary svg-icon-2x">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <rect x="0" y="0" width="24" height="24"/>
                                    <circle fill="#000000" opacity="0.3" cx="12" cy="12" r="10"/>
                                    <rect fill="#000000" x="11" y="10" width="2" height="7" rx="1"/>
                                    <rect fill="#000000" x="11" y="7" width="2" height="2" rx="1"/>
                                </g>
                            </svg>
                        </span>
                    </div>
                    <div class="alert-text"><?php echo $headerText; ?></div>
                </div>
            <?php endif; ?>
