<form action="" method="" id="kt_inbox_reply_form">
    <div class="d-block">
        <?php if ($inAdmin and $selectSavedReplies) : ?>
        <div class="border-bottom px-8 py-4 d-flex flex-row align-items-center">
            <div class="text-dark-50 mr-4"><?php echo __('Réponses automatiques'); ?></div>
            <div>
                <select class="form-control" name="saved_reply" id="saved_reply">
                    <?php echo $selectSavedReplies; ?>
                </select>
            </div>
        </div>
        <?php endif; ?>

        <div id="kt_inbox_reply_editor" name="message" class="border-0" style="height: 250px"></div>

        <div class="dropzone dropzone-multi px-8 py-4" id="kt_inbox_reply_attachments">
            <div class="dropzone-items">
                <div class="dropzone-item" style="display:none">
                    <div class="dropzone-file">
                        <div class="dropzone-filename" title="some_image_file_name.jpg">
                            <span data-dz-name="">some_image_file_name.jpg</span>
                            <strong>(<span data-dz-size="">340kb</span>)</strong>
                        </div>
                        <div class="dropzone-error" data-dz-errormessage=""></div>
                    </div>
                    <div class="dropzone-progress">
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" data-dz-uploadprogress=""></div>
                        </div>
                    </div>
                    <div class="dropzone-toolbar">
                        <span class="dropzone-delete" data-dz-remove="">
                            <i class="flaticon2-cross"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="border-bottom px-8 min-h-50px">
            <div class="checkbox-list">
                <label class="checkbox">
                    <input type="checkbox" name="close">
                    <span></span>
                    <?php echo __('Fermer le ticket'); ?>
                </label>
            </div>
        </div>

        <?php if ($inAdmin) : ?>
        <div class="border-bottom">
            <input class="form-control border-0 px-8 min-h-45px" name="add_saved_reply" placeholder="<?php echo __('Enregistrer comme réponse automatique'); ?>">
        </div>
        <?php endif; ?>

        <?php if ($inAdmin and $categories) : ?>
            <div class="align-items-center border-bottom inbox-to-bcc pl-8 pr-5 min-h-50px d-flex">
                <div class="text-dark-50 w-75px"><?php echo __('Catégories'); ?></div>
                <div class="flex-grow-1">
                    <div class="checkbox-inline">
                        <?php foreach ($categories as $id => $category) : ?>
                        <label class="checkbox mb-0" for="categories_<?php echo $category->getId(); ?>">
                            <input type="checkbox" id="categories_<?php echo $category->getId(); ?>" name="categories[]" value="<?php echo $category->getId(); ?>" <?php echo (in_array($category->getId(), $selectedCategories)? 'checked' : ''); ?>>
                            <span></span>
                            <?php echo $category->getName(); ?>
                        </label>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <div class="d-flex align-items-center justify-content-between py-5 pl-8 pr-5 border-top">
        <div class="d-flex align-items-center mr-3">
            <div class="form-actions mr-2">
                <input type="hidden" name="ticketid" id="ticketid" value="<?php echo $ticket->getTicketid(); ?>">
                <a class="btn btn-primary font-weight-bold px-6" onclick="saveReply('<?php echo $ticket->getTicketid(); ?>');"><?php echo __('Valider'); ?></a>
                [[CSRF]]
            </div>
            <span class="btn btn-icon btn-sm btn-clean mr-2 dropzone-select" id="kt_inbox_reply_attachments_select">
                <i class="flaticon2-clip-symbol"></i>
            </span>
        </div>
        <?php if ($inAdmin) : ?>
        <div class="d-flex align-items-center">
            <a class="btn btn-sm btn-light-primary font-weight-bold" onclick="saveNote('<?php echo $ticket->getTicketid(); ?>');"><?php echo __('Enregistrer comme note'); ?></a>
        </div>
        <?php endif; ?>
    </div>
</form>
