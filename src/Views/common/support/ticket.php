<div class="d-flex flex-column justify-content-between flex-wrap card-spacer-x py-5 border-bottom border-bottom-light">
    <div class="d-flex align-items-center py-2">
        <div class="font-weight-bold font-size-h3 mr-3"><?php echo $ticket->getSubject(); ?></div>
        <?php echo $status; ?>
        <?php echo $priority; ?>
        <?php if ($inAdmin and $ticket->getFlag() == \MatGyver\Entity\Support\Ticket\SupportTicket::FLAG_SPAM) : ?>
            <span class="label label-light-danger font-weight-bold label-inline mr-2"><?php echo __('Spam'); ?></span>
        <?php endif; ?>
        <?php if ($inAdmin and $ticket->getFlag() == \MatGyver\Entity\Support\Ticket\SupportTicket::FLAG_IMPORTANT) : ?>
            <span class="label label-light-warning font-weight-bold label-inline mr-2"><?php echo __('Important'); ?></span>
        <?php endif; ?>
    </div>
    <?php if ($inAdmin) : ?>
    <div class="d-flex py-2 pb-0">
        <?php if ($user) : ?>
            <a onclick="displayUserTickets(<?php echo $user->getId(); ?>);" class="text-dark-50 text-hover-primary font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2 cursor-pointer" data-rel="tooltip" data-title="<?php echo __('Voir les tickets'); ?>"><i class="flaticon2-user mr-2 font-size-lg"></i><?php echo $user->getFirstName() . ' ' . $user->getLastName(); ?></a>
            <a href="mailto:<?php echo $user->getEmail(); ?>" class="text-dark-50 text-hover-primary font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2"><i class="flaticon2-new-email mr-2 font-size-lg"></i><?php echo $user->getEmail(); ?></a>
        <?php else : ?>
            <span class="text-dark-50 font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2"><i class="flaticon2-user mr-2 font-size-lg"></i><?php echo $ticket->getFirstName() . ' ' . $ticket->getLastName(); ?></span>
            <a href="mailto:<?php echo $ticket->getEmail(); ?>" class="text-dark-50 text-hover-primary font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2"><i class="flaticon2-new-email mr-2 font-size-lg"></i><?php echo $ticket->getEmail(); ?></a>
        <?php endif; ?>
        <?php if ($client and $client->getId() !== CLIENT_MASTER) : ?>
            <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'client', $client->getId()); ?>" target="_blank" class="text-dark-50 text-hover-primary font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2"><i class="flaticon2-user mr-2 font-size-lg"></i><?php echo __('Client'); ?> : <?php echo $client->getName(); ?></a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>
<div class="mb-3">
    <?php echo $ticketContent; ?>
    <?php echo $repliesContent; ?>
</div>

<div class="card-spacer mb-3" id="kt_inbox_reply">
    <div class="card card-custom shadow-sm">
        <?php echo $replyForm; ?>
    </div>
</div>
