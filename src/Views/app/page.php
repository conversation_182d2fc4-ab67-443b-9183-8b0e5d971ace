<style>
.cards-fields {
    box-shadow: none;
    padding: 0;
    position: relative;
    min-height: 400px; /* Hauteur minimum */
    overflow: visible; /* Permettre au contenu de dépasser si nécessaire */
}

.card-field {
    position: absolute;
    width: 100%;
    transition: all 0.5s ease-in-out;
    transform-origin: center top;
}

/* État actif - carte en haut */
.card-field.active {
    top: 0;
    opacity: 1;
    z-index: 3;
    transform: translateY(0);
}

/* État inactif - carte positionnée dynamiquement avec opacité réduite */
.card-field.inactive {
    top: 100px; /* Valeur par défaut, sera mise à jour par JavaScript */
    opacity: 0.5;
    z-index: 2;
    transform: translateY(0);
}

/* État caché - cartes invisibles */
.card-field.hidden {
    top: 200px;
    opacity: 0;
    z-index: 1;
    transform: translateY(50px);
}

/* État done - carte qui disparaît vers le haut */
.card-field.done {
    top: -100px;
    opacity: 0;
    z-index: 0;
    transform: translateY(-50px);
}

/* Styles pour les boutons */
.btn-prev-field, .btn-next-field {
    margin: 10px;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    cursor: pointer;
    display: inline-block;
}

.btn-prev-field:hover, .btn-next-field:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}
</style>
<a class="btn-prev-field">Prev</a>
<a class="btn-next-field">Next</a>
<div class="cards-fields">
    <?php for ($i = 1; $i <= 10; $i++) : ?>
    <div class="card card-custom card-field gutter-b <?php echo ($i == 1 ? 'active' : ($i == 2 ? 'inactive' : 'hidden')); ?>" id="card_<?php echo $i; ?>">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Carte n°' . $i); ?></h3>
        </div>
        <div class="card-body">
            The standard Lorem Ipsum passage, used since the 1500s
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."

            Section 1.10.32 of "de Finibus Bonorum et Malorum", written by Cicero in 45 BC
            "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?"
        </div>
    </div>
    <?php endfor; ?>
</div>