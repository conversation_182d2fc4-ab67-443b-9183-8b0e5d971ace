<div class="article-container">
    <div class="d-flex align-items-center">
        <div class="symbol symbol-40 symbol-light-success mr-5">
            <span class="symbol-label">
                <i class="flaticon2-file text-primary"></i>
            </span>
        </div>
        <div class="d-flex flex-column flex-grow-1">
            <a href="#" class="text-dark-75 text-hover-primary mb-1 font-size-lg font-weight-bolder"><?php echo $title; ?></a>
            <span class="text-muted font-weight-bold"><?php echo $date; ?></span>
        </div>
    </div>
    <div class="pt-5">
        <?php echo $content; ?>
    </div>
</div>
<div class="card-footer text-center pb-0">
    <?php foreach ($smileys as $type => $smiley) : ?>
        <a class="btn p-0 card-note mr-5 card-note-<?php echo $type; ?> <?php echo ((isset($_SESSION['help_article_note_type_' . $article->getId()]) and $_SESSION['help_article_note_type_' . $article->getId()] != $type) ? 'inactive' : ''); ?>" onclick="setArticleNote(<?php echo $article->getId(); ?>, '<?php echo $type; ?>');">
            <span style="font-size: 28px"><?php echo $smiley; ?></span>
        </a>
    <?php endforeach; ?>
</div>
