<?php /** @var \MatGyver\Entity\Calendar\CalendarAppointment $appointment */ ?>
<?php /** @var \MatGyver\Entity\Dossier\Dossier $dossier */ ?>
<?php /** @var \MatGyver\Entity\User\User|null $user */ ?>
<?php /** @var \MatGyver\Entity\Summary\SummaryTemplate[] $summaryTemplates */ ?>
<div class="card card-custom gutter-b">
    <div class="card-header">
        <h3 class="card-title"><?php echo __('Rendez-vous'); ?></h3>
        <div class="card-toolbar">
            <a class="btn btn-light-primary mr-2" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'dossier', 'appointments/' . $dossier->getId()); ?>"><?php echo __('Retour'); ?></a>
        </div>
    </div>
    <div class="card-body">
        <?php if ($user) : ?>
            <strong><?php echo __('Utilisateur'); ?></strong> : <a class="text-dark-75 text-hover-primary font-weight-bold" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'user', $user->getId()); ?>"><?php echo $user->getFirstName() . ' ' . $user->getLastName(); ?></a>
        <?php elseif ($appointment->getDossier()) : ?>
            <strong><?php echo __('Utilisateur'); ?></strong> : <?php echo $appointment->getDossier()->getContact()->getFirstName() . ' ' . $appointment->getDossier()->getContact()->getLastName(); ?>
        <?php else : ?>
            <span class="text-danger"><?php echo __('Utilisateur inconnu'); ?></span>
        <?php endif; ?>
        <br>
        <strong><?php echo __('État du rendez-vous'); ?></strong> : <span class="label label-inline label-<?php echo $statusLabel; ?>"><?php echo $status; ?></span><br>
        <?php if ($appointment->getNewUser()) : ?>
            <span class="label label-inline label-outline-warning ml-2"><?php echo __('Premier rendez-vous'); ?></span><br>
        <?php endif; ?>
        <strong><?php echo __('Date'); ?></strong> : <?php echo $appointment->getFriendlyDate(); ?><br>
        <strong><?php echo __('Motif de consultation'); ?></strong> : <?php echo ($appointment->getConsultingReason() ? $appointment->getConsultingReason()->getName() : '<em>' . __('Non défini') . '</em>'); ?><br>
        <strong><?php echo __('Calendrier'); ?></strong> : <?php echo $appointment->getCalendar()->getTitle(); ?>
        <?php if ($appointment->getLink()) : ?>
            <br>
            <strong><?php echo __('Lien de connexion'); ?></strong> : <a href="<?php echo $appointment->getLink(); ?>" target="_blank"><?php echo __('Lien de connexion'); ?></a>
        <?php endif; ?>
    </div>
</div>
<?php if ($recordings) : ?>
    <?php /** @var \MatGyver\Entity\Conference\ConferenceRoomData[] $recordings */ ?>
    <?php /** @var \MatGyver\Entity\Conference\ConferenceRoomData[] $transcripts */ ?>
    <?php /** @var \MatGyver\Entity\Conference\ConferenceRoomData[] $summaries */ ?>
    <?php foreach ($recordings as $recording) : ?>
        <?php $publicLink = $recording->getRecordingPublicLink(); ?>
    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title"><?php echo __('Enregistrement'); ?></h3>
            <?php if ($publicLink) : ?>
            <div class="card-toolbar">
                <a class="btn btn-light-primary" href="<?php echo $publicLink; ?>" target="_blank"><?php echo __('Télécharger'); ?></a>
            </div>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if ($publicLink) : ?>
                <div class="embed-responsive embed-responsive-16by9">
                    <video controls>
                        <source src="<?php echo $publicLink; ?>" type="video/mp4">
                        <?php echo __('Votre navigateur ne supporte pas la lecture de vidéos.'); ?>
                    </video>
                </div>
            <?php else : ?>
                <span class="text-danger"><?php echo __('L\'enregistrement n\'est pas disponible.'); ?></span>
            <?php endif; ?>

            <ul class="nav nav-tabs nav-tabs-line mt-6">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#kt_tab_pane_1"><?php echo __('Transcription'); ?></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#kt_tab_pane_2"><?php echo __('Résumé'); ?></a>
                </li>
            </ul>
            <div class="tab-content mt-5" id="myTabContent">
                <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel" aria-labelledby="kt_tab_pane_2">
                    <?php $transcriptsContents = $recording->getTranscriptsContents(); ?>
                    <?php if (!$transcriptsContents) : ?>
                        <span class="text-danger"><?php echo __('L\'enregistrement n\'est pas disponible.'); ?></span>
                    <?php else : ?>
                        <?php if (count($transcriptsContents) === 1) : ?>
                            <?php echo $transcriptsContents[0]['content']; ?>
                        <?php else : ?>
                            <div class="accordion accordion-toggle-arrow" id="accordionTranscripts">
                                <?php foreach ($transcriptsContents as $id => $transcriptContent) : ?>
                                    <?php $i = $id; $i++; ?>
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="card-title <?php echo ($id ? 'collapsed' : ''); ?>" data-toggle="collapse" data-target="#collapseTranscript<?php echo $i; ?>">
                                                <?php echo __('Transaction %s', $i); ?>
                                            </div>
                                        </div>
                                        <div id="collapseTranscript<?php echo $i; ?>" class="collapse <?php echo ($id ? '' : 'show'); ?>" data-parent="#accordionTranscripts">
                                            <div class="card-body">
                                                <?php echo $transcriptContent['content']; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel" aria-labelledby="kt_tab_pane_2">
                    <?php if ($summaryTemplates) : ?>
                        <div class="form-group d-flex flex-row" id="formGenerateSummary">
                            <select name="summary_template_id" id="summary_template_id" data-rel="select2">
                                <?php foreach ($summaryTemplates as $summaryTemplate) : ?>
                                <option value="<?php echo $summaryTemplate->getId(); ?>"><?php echo $summaryTemplate->getName(); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <input type="hidden" name="recording_id" id="recording_id" value="<?php echo $recording->getId(); ?>">
                            <button type="button" class="btn btn-primary" id="btnGenerateSummary"><?php echo __('Générer'); ?></button>
                        </div>
                    <?php endif; ?>

                    <?php $summariesContents = $recording->getSummariesContents(); ?>
                    <?php if (!$summariesContents) : ?>
                        <span class="text-danger"><?php echo __('Aucun résumé disponible.'); ?></span>
                    <?php else : ?>
                        <?php if (count($summariesContents) === 1) : ?>
                            <?php echo $summariesContents[0]['content']; ?>
                        <?php else : ?>
                            <div class="accordion accordion-toggle-arrow" id="accordionSummaries">
                                <?php foreach ($summariesContents as $id => $summaryContent) : ?>
                                    <?php $i = $id; $i++; ?>
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="card-title <?php echo ($id ? 'collapsed' : ''); ?>" data-toggle="collapse" data-target="#collapseSummary<?php echo $i; ?>">
                                                <?php echo __('Résumé %s', $i); ?>
                                                <?php if (isset($summaryContent['template']) and $summaryContent['template']) : ?>
                                                    <span class="label label-inline label-outline-info ml-2"><?php echo __('Modèle : %s', $summaryContent['template']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div id="collapseSummary<?php echo $i; ?>" class="collapse <?php echo ($id ? '' : 'show'); ?>" data-parent="#accordionSummaries">
                                            <div class="card-body">
                                                <?php echo $summaryContent['content']; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
<?php endif; ?>
