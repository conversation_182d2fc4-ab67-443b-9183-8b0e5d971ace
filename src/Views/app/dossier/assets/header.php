<?php /** @var \MatGyver\Entity\Dossier\DossierAsset $asset */ ?>
<div class="card card-custom gutter-b">
    <div class="card-header">
        <div class="card-toolbar">
            <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'dossier', 'assets/' . $asset->getType() . '/' . $asset->getDossier()->getId()); ?>" class="btn btn-light-primary btn-sm">
                <?php echo __('Retour'); ?>
            </a>
        </div>
        <div class="card-title">
            <h3 class="card-label"><?php echo $asset->getDesignation() ?: \MatGyver\Enums\AssetsEnum::get($asset->getType()); ?></h3>
        </div>
        <div class="card-toolbar">
            <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'dossier', 'asset/' . $asset->getType() . '/edit/' . $asset->getId() . '/' . $asset->getDossier()->getId()); ?>" class="btn btn-primary btn-sm font-weight-bolder">
                <?php echo __('Modifier'); ?>
            </a>
        </div>
    </div>
</div>