<?php $container = \MatGyver\Services\DI\ContainerBuilderService::getInstance(); ?>
<h3 class="font-weight-bolder mb-5"><?php echo __('Quêtes'); ?> <span class="text-muted"><?php echo count($quests); ?></span></h3>
<div class="row">
    <?php /** @var \MatGyver\Entity\Game\GameQuest[] $quests */ ?>
    <?php foreach ($quests as $quest) : ?>
        <?php $clientQuest = $quest->getQuestClient(); ?>
        <?php $status = null; ?>
        <?php if (!$clientQuest and $quest->getConditions()) : ?>
            <?php $status = $container->get(\MatGyver\Services\Game\GameQuestConditionsService::class)->getStatus($quest->getConditions()); ?>
        <?php endif; ?>
        <div class="col-md-3 col-sm-3">
            <div class="card gutter-b card-quest card-custom card-stretch" id="quest<?php echo $quest->getId(); ?>">
                <?php echo \MatGyver\Helpers\Game\GameReward::renderAsStickers($quest->getRewards(), $quest->getNbPoints()); ?>
                <div class="card-body">
                    <div class="d-flex justify-content-between flex-column pt-4 h-100">
                        <div class="d-flex flex-column flex-center">
                            <div class="symbol symbol-80 symbol-circle symbol-white overflow-hidden">
                                <span class="symbol-label">
                                    <img src="<?php echo $quest->getImage(); ?>" class="h-50px" alt="<?php echo $quest->getName(); ?>">
                                </span>
                            </div>
                            <h4 class="card-title font-weight-bolder text-dark-75 text-hover-primary m-0 pt-7 pb-5"><?php echo $quest->getName(); ?></h4>
                            <?php if ($quest->getDescription()) : ?>
                                <div class="description text-muted text-center">
                                    <?php echo $quest->getDescription(); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($quest->getConditions()) : ?>
                                <div class="conditions">
                                    <?php foreach ($quest->getConditions() as $condition) : ?>
                                        <?php echo $condition->render(); ?><br>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <?php if ($clientQuest and $clientQuest->getStatus() == \MatGyver\Entity\Game\GameQuestClient::STATUS_IN_PROGRESS) : ?>
                        <a class="btn btn-primary" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'game', 'quest/continue/' . $quest->getId() . '/' . $clientQuest->getStep()->getId()); ?>"><?php echo __('Continuer'); ?></a><br>
                    <?php elseif ($clientQuest and $clientQuest->getStatus() == \MatGyver\Entity\Game\GameQuestClient::STATUS_DONE) : ?>
                        <?php if ($clientQuest->getRewardsClaimed()) : ?>
                            <span class="text-success"><?php echo __('Terminée'); ?></span><br>
                        <?php else : ?>
                            <a class="btn btn-warning" onclick="getModalQuestReward(<?php echo $quest->getId(); ?>)"><i class="fas fa-gem"></i> <?php echo __('Récupérer les récompenses'); ?></a><br>
                        <?php endif; ?>
                    <?php elseif (!$clientQuest and !$currentQuest) : ?>
                        <?php if ($status and !$status['completed']) : ?>
                            <span class="text-danger"><?php echo __('Conditions non remplies'); ?></span><br>
                        <?php else : ?>
                            <a class="btn btn-primary" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'game', 'quest/start/' . $quest->getId()); ?>"><?php echo __('Démarrer'); ?></a><br>
                        <?php endif; ?>
                    <?php endif; ?>
                    <a class="btn btn-link" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'game', 'quest/' . $quest->getId()); ?>"><?php echo __('Voir le détail'); ?></a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>
