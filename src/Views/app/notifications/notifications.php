<div class="d-flex flex-column flex-lg-row">
    <div class="flex-row-auto w-lg-200px w-xl-275px gutter-b" id="kt_todo_aside">
        <div class="card card-custom card-stretch">
            <div class="card-body px-5">
                <div class="navi navi-hover navi-active navi-link-rounded navi-bold navi-icon-center navi-light-icon" id="main-navigation">
                    <div class="navi-item my-2">
                        <a class="navi-link navi-link-notifications active" onclick="displayNotifications()">
                            <span class="navi-icon mr-4">
                                <span class="svg-icon svg-icon-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <rect x="0" y="0" width="24" height="24"></rect>
                                            <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,13 C19,13.5522847 18.5522847,14 18,14 L6,14 C5.44771525,14 5,13.5522847 5,13 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M13.8,4 C13.1562,4 12.4033,4.72985286 12,5.2 C11.5967,4.72985286 10.8438,4 10.2,4 C9.0604,4 8.4,4.88887193 8.4,6.02016349 C8.4,7.27338783 9.6,8.6 12,10 C14.4,8.6 15.6,7.3 15.6,6.1 C15.6,4.96870845 14.9396,4 13.8,4 Z" fill="#000000" opacity="0.3"></path>
                                            <path d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z" fill="#000000"></path>
                                        </g>
                                    </svg>
                                </span>
                            </span>
                            <span class="navi-text font-weight-bolder font-size-lg"><?php echo __('Notifications'); ?></span>
                            <?php if ($nbNotifications) : ?>
                                <span class="navi-label">
                                    <span class="label label-rounded label-light-primary font-weight-bolder nb-notifications" data-nb-notifications="<?php echo $nbNotifications; ?>"><?php echo $nbNotifications; ?></span>
                                </span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="navi-item my-2">
                        <a class="navi-link navi-link-news" onclick="displayNews();">
                            <span class="navi-icon mr-4">
                                <span class="svg-icon svg-icon-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                            <path d="M12,4.25932872 C12.1488635,4.25921584 12.3000368,4.29247316 12.4425657,4.36281539 C12.6397783,4.46014562 12.7994058,4.61977315 12.8967361,4.81698575 L14.9389263,8.95491503 L19.5054023,9.61846284 C20.0519472,9.69788046 20.4306287,10.2053233 20.351211,10.7518682 C20.3195865,10.9695052 20.2170993,11.1706476 20.0596157,11.3241562 L16.7552826,14.545085 L17.5353298,19.0931094 C17.6286908,19.6374458 17.263103,20.1544017 16.7187666,20.2477627 C16.5020089,20.2849396 16.2790408,20.2496249 16.0843804,20.1472858 L12,18 L12,4.25932872 Z" fill="#000000" opacity="0.3"></path>
                                            <path d="M12,4.25932872 L12,18 L7.91561963,20.1472858 C7.42677504,20.4042866 6.82214789,20.2163401 6.56514708,19.7274955 C6.46280801,19.5328351 6.42749334,19.309867 6.46467018,19.0931094 L7.24471742,14.545085 L3.94038429,11.3241562 C3.54490071,10.938655 3.5368084,10.3055417 3.92230962,9.91005817 C4.07581822,9.75257453 4.27696063,9.65008735 4.49459766,9.61846284 L9.06107374,8.95491503 L11.1032639,4.81698575 C11.277344,4.464261 11.6315987,4.25960807 12,4.25932872 Z" fill="#000000"></path>
                                        </g>
                                    </svg>
                                </span>
                            </span>
                            <span class="navi-text font-weight-bolder font-size-lg"><?php echo __('News'); ?></span>
                            <?php if ($nbNews) : ?>
                                <span class="navi-label navi-nb-news">
                                    <span class="label label-rounded label-light-primary font-weight-bolder"><?php echo $nbNews; ?></span>
                                </span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="navi-item my-2">
                        <a class="navi-link navi-link-versions" onclick="displayVersions();">
                            <span class="navi-icon mr-4">
                                <span class="svg-icon svg-icon-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <rect x="0" y="0" width="24" height="24"></rect>
                                            <path d="M3,16 L5,16 C5.55228475,16 6,15.5522847 6,15 C6,14.4477153 5.55228475,14 5,14 L3,14 L3,12 L5,12 C5.55228475,12 6,11.5522847 6,11 C6,10.4477153 5.55228475,10 5,10 L3,10 L3,8 L5,8 C5.55228475,8 6,7.55228475 6,7 C6,6.44771525 5.55228475,6 5,6 L3,6 L3,4 C3,3.44771525 3.44771525,3 4,3 L10,3 C10.5522847,3 11,3.44771525 11,4 L11,19 C11,19.5522847 10.5522847,20 10,20 L4,20 C3.44771525,20 3,19.5522847 3,19 L3,16 Z" fill="#000000" opacity="0.3"></path>
                                            <path d="M16,3 L19,3 C20.1045695,3 21,3.8954305 21,5 L21,15.2485298 C21,15.7329761 20.8241635,16.200956 20.5051534,16.565539 L17.8762883,19.5699562 C17.6944473,19.7777745 17.378566,19.7988332 17.1707477,19.6169922 C17.1540423,19.602375 17.1383289,19.5866616 17.1237117,19.5699562 L14.4948466,16.565539 C14.1758365,16.200956 14,15.7329761 14,15.2485298 L14,5 C14,3.8954305 14.8954305,3 16,3 Z" fill="#000000"></path>
                                        </g>
                                    </svg>
                                </span>
                            </span>
                            <span class="navi-text font-weight-bolder font-size-lg"><?php echo __('Versions'); ?></span>
                            <?php if ($nbVersions) : ?>
                                <span class="navi-label navi-nb-versions">
                                    <span class="label label-rounded label-light-primary font-weight-bolder"><?php echo $nbVersions; ?></span>
                                </span>
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="flex-row-fluid ml-lg-8">
        <div class="d-flex flex-column flex-grow-1">
            <div id="notifications">
                <div class="notifications-blank-state <?php echo ((!$notifications and !$tempNotifications) ? '' : 'd-none'); ?>">
                    <?php $factory = new \MatGyver\Factories\BlankStates\SuccessBlankState('Notifications', __('Aucune notification')); ?>
                    <?php echo $factory->render(); ?>
                </div>

                <?php if ($tempNotifications or $notifications) : ?>
                <div class="card card-custom card-stretch" id="notifications_container">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <div class="list list-hover" data-inbox="list">
                                <?php if ($tempNotifications) : ?>
                                    <?php foreach ($tempNotifications as $id => $tempNotification) : ?>
                                        <div class="d-flex align-items-start list-item card-spacer-x py-4 notification-inbox nav-notification-temp_<?php echo $id; ?>" data-inbox="message" onclick="displayNotification('temp_<?php echo $id; ?>');">
                                            <div class="notification-container">
                                                <div class="d-flex flex-row align-items-center">
                                                    <div class="flex-grow-1 mt-1 mr-2" data-toggle="view">
                                                        <div class="notification-subject"><?php echo $tempNotification['title']; ?></div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <?php $type = '<span class="label label-inline label-light-info">' . __('Notification') . '</span>'; ?>
                                                        <?php if (isset($tempNotification['class']) and $tempNotification['class'] == 'danger') : ?>
                                                            <?php $type = '<span class="label label-inline label-light-danger">' . __('Important') . '</span>'; ?>
                                                        <?php endif; ?>
                                                        <?php echo $type; ?>
                                                    </div>
                                                    <?php if (isset($tempNotification['class']) and $tempNotification['class'] == 'danger') : ?>
                                                        <div class="d-flex align-items-center justify-content-end flex-wrap mt-5" data-toggle="view">
                                                            <i class="fas fa-exclamation-triangle text-danger"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="notification d-none" id="notification_temp_<?php echo $id; ?>">
                                                    <div class="mb-1">
                                                        <?php echo $tempNotification['content']; ?>
                                                    </div>
                                                    <?php if (isset($tempNotification['link']) and $tempNotification['link']) : ?>
                                                        <a href="<?php echo $tempNotification['link']; ?>" class="btn btn-success font-weight-bolder btn-lg"><?php echo $tempNotification['linkText']; ?></a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <?php if ($notifications) : ?>
                                    <?php foreach ($notifications as $notification) : ?>
                                        <div class="d-flex flex-column align-items-start list-item card-spacer-x py-4 notification-inbox nav-notification-<?php echo $notification->getId(); ?> <?php echo (!$notification->getSeen() ? 'new' : ''); ?>" data-notification="<?php echo $notification->getId(); ?>">
                                            <div class="notification-container">
                                                <div class="d-flex flex-row align-items-center">
                                                    <div class="flex-grow-1 mt-1 mr-2 notification-subject" data-toggle="view" onclick="displayNotification(<?php echo $notification->getId(); ?>);">
                                                        <?php echo $notification->getSubject(); ?>
                                                        <div class="mt-2">
                                                            <?php if ($notification->getType()) : ?>
                                                                <?php $label = $notification->getLabel(); ?>
                                                                <span class="label label-light-<?php echo $label['class']; ?> font-weight-bold label-inline"><?php echo $label['info']; ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-end flex-wrap" data-toggle="view">
                                                        <div class="font-weight-bold mr-2" data-toggle="view"><?php echo humanizeTime(time() - $notification->getDate()->getTimestamp()); ?></div>
                                                        <span class="btn btn-default btn-icon btn-sm mr-2 btn-hover-light-danger" data-toggle="tooltip" title="<?php echo __('Supprimer'); ?>" onclick="deleteNotification(<?php echo $notification->getId(); ?>);">
                                                            <span class="svg-icon svg-icon-md">
                                                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                        <rect x="0" y="0" width="24" height="24"/>
                                                                        <path d="M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z" fill="#000000" fill-rule="nonzero"/>
                                                                        <path d="M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z" fill="#000000" opacity="0.3"/>
                                                                    </g>
                                                                </svg>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="notification d-none" id="notification_<?php echo $notification->getId(); ?>"></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div id="news" class="d-none">
                <?php echo $news; ?>
            </div>

            <div id="versions" class="d-none">
                <?php echo $versions; ?>
            </div>
        </div>
    </div>
</div>
