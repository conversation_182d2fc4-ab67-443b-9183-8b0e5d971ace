<!DOCTYPE html>
<html lang="<?php echo ((isset($_SESSION['language']) and $_SESSION['language'] == 'en_US') ? 'en' : 'fr'); ?>">
<head>
	<meta charset="utf-8">
	<title><?php echo $this->data['title']; ?></title>
	<meta name="description" content="<?php echo $this->data['title']; ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Main CSS -->
	<?php \MatGyver\Helpers\Assets::renderCss(); ?>

    <?php
    if (isset($this->data['meta']) and $this->data['meta']) {
        echo $this->data['meta'];
    }
    ?>

    <!-- Fav and touch icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo \MatGyver\Helpers\Assets::getUrl('assets/favicon/apple-touch-icon.png'); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo \MatGyver\Helpers\Assets::getUrl('assets/favicon/favicon-32x32.png'); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo \MatGyver\Helpers\Assets::getUrl('assets/favicon/favicon-16x16.png'); ?>">
    <link rel="manifest" href="<?php echo \MatGyver\Helpers\Assets::getUrl('assets/favicon/site.webmanifest'); ?>">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">

    <script>
        var baseDir = "<?php echo defined('APP_CLIENT_URL') ? APP_CLIENT_URL : ''; ?>";
        var cdnDir = "<?php echo defined('CDN_URL') ? CDN_URL : ''; ?>";
        var appLanguage = '<?php echo ($_SESSION['language'] ?? 'fr_FR'); ?>';
        var CSRFGuard_token = '<?php echo $this->data['CSRFGuard_token']; ?>';
	</script>

    <!-- Head JS -->
    <?php \MatGyver\Helpers\Assets::renderJs(true); ?>
    <?php \MatGyver\Helpers\Assets::renderInlineJs(true); ?>

    <?php if (!isset($this->data['no_debug_toolbar']) or $this->data['no_debug_toolbar'] !== true) : ?>
        <?php require VIEWS_PATH . '/layouts/common/debug-toolbar-head.php'; ?>
    <?php endif; ?>
</head>
<body class="<?php echo (!empty($this->data['class']) ? implode(' ', $this->data['class']) : ''); ?>">
    <noscript>
        <div class="alert alert-block col-md-10">
            <h4 class="alert-heading"><?php echo __('Attention!'); ?></h4>
            <a href="http://en.wikipedia.org/wiki/JavaScript" target="_blank"><?php echo __('Vous devez activer JavaScript dans votre navigateur pour voir ce site'); ?></a>
        </div>
    </noscript>

    <div class="d-flex flex-column flex-root">
        <div class="login login-3 wizard d-flex flex-column flex-lg-row flex-column-fluid wizard" id="kt_wizard" data-wizard-state="first" data-wizard-step="1">
