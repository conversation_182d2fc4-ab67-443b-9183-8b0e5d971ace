<ul class="menu-nav">
    <?php foreach ($menu as $item) : ?>
        <li class="nav-item">
            <a class="nav-link <?php echo (($matcher->isCurrent($item) || $matcher->isAncestor($item)) ? 'active' : ''); ?> <?php echo ($item->hasChildren() ? 'has-children' : ''); ?>" <?php echo ($item->getExtra('tab') ? 'data-toggle="tab" data-target="#' . $item->getExtra('tab') . '" role="tab"' : 'href="' . $item->getUri() . '"'); ?>>
                <span class="menu-icon">
                    <?php echo $item->getExtra('icon'); ?>
                </span>
                <span class="menu-text"><?php echo $item->getLabel(); ?></span>
            </a>
            <?php if ($item->hasChildren()) : ?>
                <div class="menu-submenu menu-submenu-classic menu-submenu-left <?php echo ($item->getExtra('extended') ? 'menu-extended w-100 w-lg-550px' : ''); ?>">
                    <?php if ($item->getExtra('extended')) : ?>
                        <div class="py-3 px-3 py-lg-6 px-lg-6">
                            <div class="row">
                                <?php foreach ($item->getChildren() as $children) : ?>
                                    <div class="col-lg-6 mb-3">
                                        <div class="menu-item">
                                            <a href="<?php echo ($children->getUri() ?: 'javascript:;'); ?>" class="menu-link <?php echo ($matcher->isCurrent($children) ? 'active' : ''); ?>">
                                                <?php if (str_contains($children->getExtra('icon'), 'fa-') or str_contains($children->getExtra('icon'), 'flaticon')) : ?>
                                                    <i class="<?php echo $children->getExtra('icon'); ?>"></i>
                                                <?php else : ?>
                                                    <?php echo $children->getExtra('icon'); ?>
                                                <?php endif; ?>
                                                <span class="d-flex flex-column">
                                                    <span class="menu-title"><?php echo $children->getLabel(); ?></span>
                                                    <?php if ($children->getExtra('description')) : ?>
                                                        <span class="menu-subtitle text-muted"><?php echo $children->getExtra('description'); ?></span>
                                                    <?php endif; ?>
                                                </span>
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else : ?>
                    <ul class="menu-subnav">
                        <?php foreach ($item->getChildren() as $children) : ?>
                            <li class="menu-item <?php echo (!$children->getUri() ? 'menu-item-submenu' : ''); ?> <?php echo ($matcher->isCurrent($children) ? 'menu-item-here' : ''); ?>" <?php echo (!$children->getUri() ? 'data-menu-toggle="hover" aria-haspopup="true"' : ''); ?>>
                                <a href="<?php echo ($children->getUri() ?: 'javascript:;'); ?>" class="menu-link <?php echo (!$children->getUri() ? 'menu-toggle' : ''); ?>">
                                    <?php if ($children->getExtra('icon')) : ?>
                                        <span class="menu-icon">
                                            <?php echo $children->getExtra('icon'); ?>
                                        </span>
                                    <?php endif; ?>
                                    <span class="menu-text"><?php echo $children->getLabel(); ?></span>
                                    <?php if (!$children->getUri()) : ?>
                                        <i class="menu-arrow"></i>
                                    <?php endif; ?>
                                </a>

                                <?php if ($children->getChildren()) : ?>
                                    <div class="menu-submenu menu-submenu-classic menu-submenu-right">
                                        <ul class="menu-subnav">
                                            <?php foreach ($children->getChildren() as $subChildren) : ?>
                                                <li class="menu-item" aria-haspopup="true">
                                                    <a href="<?php echo $subChildren->getUri(); ?>" class="menu-link">
                                                        <?php if ($subChildren->getExtra('icon')) : ?>
                                                            <span class="menu-icon">
                                                                <?php echo $subChildren->getExtra('icon'); ?>
                                                            </span>
                                                        <?php endif; ?>
                                                        <span class="menu-text"><?php echo $subChildren->getLabel(); ?></span>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </li>
    <?php endforeach; ?>
</ul>
