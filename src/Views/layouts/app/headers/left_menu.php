<?php foreach ($menu as $item) : ?>
    <div class="navi-item">
        <?php if ($item->hasChildren()) : ?>
            <div class="accordion accordion-light accordion-toggle-arrow" id="accordion<?php echo $item->getAttribute('id'); ?>">
                <div class="card-title navi-link cursor-pointer mb-2 <?php echo (($matcher->isCurrent($item) || $matcher->isAncestor($item)) ? 'active' : 'collapsed'); ?>" data-toggle="collapse" data-target="#collapse<?php echo $item->getAttribute('id'); ?>">
                    <span class="navi-icon">
                        <span class="svg-icon <?php echo $item->getExtra('iconColor') ? $item->getExtra('iconColor') : ''; ?>">
                            <i class="fas fa-circle"></i>
                        </span>
                    </span>
                    <span class="navi-text font-size-lg"><?php echo $item->getLabel(); ?></span>
                    <?php if ($item->getAttribute('label')) : ?>
                        <span class="navi-label">
                            <span class="label label-sm label-light-primary"><?php echo $item->getAttribute('label'); ?></span>
                        </span>
                    <?php endif; ?>
                </div>
                <div id="collapse<?php echo $item->getAttribute('id'); ?>" class="collapse <?php echo (($matcher->isCurrent($item) || $matcher->isAncestor($item)) ? 'show' : ''); ?>" data-parent="#accordion<?php echo $item->getAttribute('id'); ?>">
                    <div class="card-body pl-8 pt-0">
                        <?php foreach ($item->getChildren() as $children) : ?>
                            <div class="navi-item mb-2">
                                <a href="<?php echo $children->getUri(); ?>" class="navi-link py-3 <?php echo ($matcher->isCurrent($children) ? 'active' : ''); ?>">
                                    <span class="navi-text font-size-lg"><?php echo $children->getLabel(); ?></span>
                                    <?php if ($children->getAttribute('label')) : ?>
                                        <span class="navi-label navi-label-rounded ">
                                            <span class="label <?php echo $children->getAttribute('label-color') ?: 'label-light-primary'; ?> label-inline"><?php echo $children->getAttribute('label'); ?></span>
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($children->getAttribute('success') === true) : ?>
                                        <span class="text-success"><i class="fas fa-check"></i></span>
                                    <?php elseif ($children->getAttribute('success') === false) : ?>
                                        <span class="text-danger"><i class="fas fa-exclamation-triangle"></i></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php else : ?>
            <?php if ($item->getExtra('separator')) : ?>
                <div class="separator separator-solid my-4"></div>
            <?php else : ?>
                <a href="<?php echo $item->getUri(); ?>" class="navi-link <?php echo ($matcher->isCurrent($item) ? 'active' : ''); ?>">
                    <span class="navi-icon">
                        <span class="svg-icon <?php echo $item->getExtra('iconColor') ? $item->getExtra('iconColor') : ''; ?>">
                            <i class="fas fa-circle"></i>
                        </span>
                    </span>
                    <span class="navi-text font-size-lg"><?php echo $item->getLabel(); ?></span>
                    <?php if ($item->getAttribute('label')) : ?>
                        <span class="navi-label">
                        <span class="label label-light-primary label-sm"><?php echo $item->getAttribute('label'); ?></span>
                    </span>
                    <?php endif; ?>
                </a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endforeach; ?>
