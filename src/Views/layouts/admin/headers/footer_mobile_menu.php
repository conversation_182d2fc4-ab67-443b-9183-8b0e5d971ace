<nav class="mobile-nav">
    <?php foreach ($menu as $item) : ?>
        <?php if ($item->hasChildren()) : ?>
            <?php foreach ($item->getChildren() as $children) : ?>
                <?php if ($children->getChildren()) : ?>
                    <?php foreach ($children->getChildren() as $subChildren) : ?>
                        <?php if (!$subChildren->getExtra('mobile')) : ?>
                            <?php continue; ?>
                        <?php endif; ?>
                        <a class="mobile-nav-item <?php echo (($matcher->isCurrent($subChildren) or $matcher->isAncestor($subChildren)) ? 'mobile-nav-item-active' : ''); ?>" href="<?php echo ($subChildren->getUri() ?: 'javascript:;'); ?>">
                            <?php if ($subChildren->getExtra('icon')) : ?>
                                <span class="mobile-nav-icon">
                                    <?php if (str_contains($subChildren->getExtra('icon'), 'fa-')) : ?>
                                        <?php echo $subChildren->getExtra('icon'); ?>
                                    <?php else : ?>
                                        <span class="svg-icon <?php echo (($matcher->isCurrent($subChildren) or $matcher->isAncestor($subChildren)) ? 'svg-icon-primary' : ''); ?>">
                                            <?php echo $subChildren->getExtra('icon'); ?>
                                        </span>
                                    <?php endif; ?>
                                </span>
                            <?php endif; ?>
                            <span class="mobile-nav-text"><?php echo $subChildren->getLabel(); ?></span>
                        </a>
                    <?php endforeach; ?>
                <?php else : ?>
                    <?php if (!$children->getExtra('mobile')) : ?>
                        <?php continue; ?>
                    <?php endif; ?>
                    <a class="mobile-nav-item <?php echo (($matcher->isCurrent($children) or $matcher->isAncestor($children)) ? 'mobile-nav-item-active' : ''); ?>" href="<?php echo ($children->getUri() ?: 'javascript:;'); ?>">
                        <?php if ($children->getExtra('icon')) : ?>
                            <span class="mobile-nav-icon">
                            <?php if (str_contains($children->getExtra('icon'), 'fa-')) : ?>
                                <?php echo $children->getExtra('icon'); ?>
                            <?php else : ?>
                                <span class="svg-icon <?php echo (($matcher->isCurrent($children) or $matcher->isAncestor($children)) ? 'svg-icon-primary' : ''); ?>">
                                    <?php echo $children->getExtra('icon'); ?>
                                </span>
                            <?php endif; ?>
                            </span>
                        <?php endif; ?>
                        <span class="mobile-nav-text"><?php echo $children->getLabel(); ?></span>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else : ?>
            <?php if (!$item->getExtra('mobile')) : ?>
                <?php continue; ?>
            <?php endif; ?>
            <a class="mobile-nav-item <?php echo (($matcher->isCurrent($item) or $matcher->isAncestor($item)) ? 'mobile-nav-item-active' : ''); ?>" href="<?php echo ($item->getUri() ?: 'javascript:;'); ?>">
                <?php if ($item->getExtra('icon')) : ?>
                    <span class="mobile-nav-icon">
                        <?php if (str_contains($item->getExtra('icon'), 'fa-')) : ?>
                            <?php echo $item->getExtra('icon'); ?>
                        <?php else : ?>
                            <span class="svg-icon <?php echo (($matcher->isCurrent($item) or $matcher->isAncestor($item)) ? 'svg-icon-primary' : ($item->getExtra('iconColor') ? $item->getExtra('iconColor') : '')); ?>">
                                <?php echo $item->getExtra('icon'); ?>
                            </span>
                        <?php endif; ?>
                    </span>
                <?php endif; ?>
                <span class="mobile-nav-text"><?php echo $item->getLabel(); ?></span>
            </a>
        <?php endif; ?>
    <?php endforeach; ?>
</nav>
