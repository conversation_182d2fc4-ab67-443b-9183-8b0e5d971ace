<?php /** @var \MatGyver\Entity\Shop\Customer\ShopCustomer $customer */ ?>
<div class="btn-group">
    <a class="btn btn-info font-weight-bolder" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'customer/edit/' . $customer->getId()); ?>">
        <?php echo __('Modifier'); ?>
    </a>
    <button type="button" class="btn btn-info font-weight-bolder dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <span class="sr-only"><?php echo __('Menu'); ?></span>
    </button>
    <div class="dropdown-menu dropdown-menu-right">
        <ul class="navi navi-hover py-5 no-wrap">
            <li class="navi-item">
                <a class="navi-link text-danger" onclick="CreateConfirmationDeleteAlert('shop_customer', <?php echo $customer->getId(); ?>, '<?php echo __('Etes-vous sûr de vouloir supprimer ce client ?'); ?>', '', '', '<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'customer/delete/' . $customer->getId()); ?>');">
                    <span class="navi-icon"><i class="fa fa-trash"></i></span>
                    <span class="navi-text"><?php echo __('Supprimer'); ?></span>
                </a>
            </li>
        </ul>
    </div>
</div>