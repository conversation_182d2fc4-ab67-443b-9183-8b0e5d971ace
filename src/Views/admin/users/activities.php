<div class="timeline timeline-3">
    <div class="timeline-items">
        <?php foreach ($activities as $activity) : ?>
            <div class="timeline-item <?php echo $activity['type']; ?>">
                <?php if ($activity['type'] == 'transaction') : ?>
                <div class="timeline-media">
                    <i class="fa fa-shopping-cart"></i>
                </div>
                <div class="timeline-content">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <span class="text-dark-75 text-hover-primary font-weight-bold"><strong><?php echo __('Nouvelle commande'); ?></strong> <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'transaction/' . $activity['detail']->getReference()); ?>">#<?php echo $activity['detail']->getReference(); ?></a></span>
                        <span class="text-muted ml-2"><?php echo dateTimeFr($activity['date']); ?></span>
                    </div>
                    <p>
                        <?php echo \MatGyver\Helpers\Transaction::displayTransactionPaymentMethod($activity['detail']->getPaymentMethod()); ?><br>
                        <?php echo \MatGyver\Helpers\Number::formatAmount($activity['detail']['amount'], $activity['detail']->getCurrency()); ?><br>
                        <?php echo \MatGyver\Helpers\Transaction::displayTransactionStatus($activity['detail']->getStatus(), true); ?>
                    </p>
                </div>
                <?php elseif ($activity['type'] == 'appointment') : ?>
                    <?php /** @var \MatGyver\Entity\Calendar\CalendarAppointment $appointment */ ?>
                    <?php $appointment = $activity['detail']; ?>
                    <div class="timeline-media">
                        <i class="flaticon-calendar"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div>
                                <?php if ($appointment->getDossier()) : ?>
                                    <a class="text-dark-75 text-hover-primary font-weight-bolder mr-2" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'dossier', 'appointment/' . $appointment->getId() . '/' . $appointment->getDossier()->getId()); ?>"><?php echo __('Rendez-vous'); ?></a>
                                <?php else : ?>
                                    <a class="text-dark-75 text-hover-primary font-weight-bolder mr-2" href="<?php echo \MatGyver\Helpers\Tools::makeLink('app', 'calendar', 'appointment/' . $appointment->getId()); ?>"><?php echo __('Rendez-vous'); ?></a>
                                <?php endif; ?>
                                <?php $status = \MatGyver\Services\Calendar\CalendarAppointmentService::getStatus($appointment->getStatus()); ?>
                                <?php $statusLabel = \MatGyver\Services\Calendar\CalendarAppointmentService::getStatusLabel($appointment->getStatus()); ?>
                                <span class="label label-inline label-<?php echo $statusLabel; ?>"><?php echo $status; ?></span>
                            </div>
                            <span class="text-muted ml-2"><?php echo dateTimeFr($activity['date']); ?></span>
                        </div>
                        <p>
                            <i class="flaticon2-calendar mr-2 font-size-lg"></i> <?php echo $appointment->getCalendar()->getTitle(); ?><br>
                            <i class="flaticon2-list mr-2 font-size-lg"></i> <?php echo ($appointment->getConsultingReason() ? $appointment->getConsultingReason()->getName() : ''); ?><br>
                            <i class="flaticon-clock mr-2 font-size-lg"></i> <?php echo $appointment->getFriendlyDate(); ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>
