<?php /** @var \MatGyver\Entity\Request\RequestQuestion $requestQuestion */ ?>
<div class="card card-custom gutter-b">
    <div class="card-header">
        <h3 class="card-title"><?php echo $requestQuestion->getTitle(); ?></h3>
    </div>
    <div class="card-body">
        <p>
            <strong><?php echo __('Id'); ?></strong> :
                    <?php echo $requestQuestion->getId(); ?>
        <br>
            <strong><?php echo __('Slug'); ?></strong> :
                    <?php echo $requestQuestion->getSlug(); ?>
        <br>
            <strong><?php echo __('Type'); ?></strong> :
                    <?php echo $requestQuestion->getType(); ?>
        <br>
            <strong><?php echo __('Title'); ?></strong> :
                    <?php echo $requestQuestion->getTitle(); ?>
        <br>
            <strong><?php echo __('Help'); ?></strong> :
                    <?php echo $requestQuestion->getHelp(); ?>
        <br>
            <strong><?php echo __('Options'); ?></strong> :
                    <?php echo $requestQuestion->getOptions(); ?>
        <br>
            <strong><?php echo __('Required'); ?></strong> :
                    <?php if ($requestQuestion->getRequired()) : ?>
                <span class="label label-inline label-success"><?php echo __('Oui'); ?></span>
                <?php else : ?>
                <span class="label label-inline label-danger"><?php echo __('Non'); ?></span>
                <?php endif; ?>
        <br>
            <strong><?php echo __('Position'); ?></strong> :
                    <?php echo $requestQuestion->getPosition(); ?>
        <br>
            <strong><?php echo __('Conditions'); ?></strong> :
                    <?php echo $requestQuestion->getConditions(); ?>
        <br>
        </p>
    </div>
</div>
