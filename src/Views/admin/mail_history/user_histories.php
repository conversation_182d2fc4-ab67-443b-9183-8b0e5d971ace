<?php if (!$user and (!isset($email) or !$email)) : ?>
    <div class="alert alert-danger"><?php echo __('Cet utilisateur n\'existe pas.'); ?></div>
    <?php return; ?>
<?php endif; ?>

<?php if ($user) : ?>
<div class="mb-10" style="text-align: right">
    <a class="btn btn-sm btn-light-primary font-weight-bolder" onclick="ShowSendEmailForm();"><?php echo __('Envoyer un email à') . ' ' . $user->getFirstName(); ?></a>
</div>
<div class="tab-mails-form">
    <form class="form" method="post" action="" id="send_email">
        <div class="content">
            <div class="form-group">
                <div class="controls">
                    <input class="form-control input" type="text" name="subject" id="subject" placeholder="<?php echo __('Sujet'); ?>">
                </div>
            </div>
            <div class="form-group">
                <div class="controls">
                    <textarea id="send-email-message" name="message" rows="3" class="ckeditor"></textarea>
                </div>
            </div>
        </div>
        <div class="footer">
            <div class="footer-left"></div><div class="footer-right">
                [[CSRF]]
                <input type="hidden" name="user_id" id="user_id" value="<?php echo $user->getId(); ?>">
                <a class="btn btn-clean btn-default" href="#" onclick="HideSendEmailForm();"><?php echo __('Annuler'); ?></a>
                <a class="btn btn-light-primary font-weight-bold" id="btn-validate" onclick="SendEmail();"><?php echo __('Envoyer le message'); ?></a>
            </div>
        </div>
    </form>
    <div class="separator separator-dashed my-10"></div>
</div>
<?php endif; ?>

<?php if (!$userHistories) : ?>
    <div class="alert alert-custom alert-light-info"><?php echo __('Aucun email transactionnel trouvé pour cet utilisateur.'); ?></div>
<?php else : ?>
    <table class="table table-separate table-head-custom datatable" style="width: 100%">
        <thead>
            <tr>
                <th class="hidden"></th>
                <th><?php echo __('Type'); ?></th>
                <th><?php echo __('Sujet'); ?></th>
                <th><?php echo __('Date'); ?></th>
                <th><?php echo __('Date d\'ouverture'); ?></th>
                <th width="75"><?php echo __('Actions'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($userHistories as $userHistory) : ?>
                <?php $mail = $userHistory->getMail(); ?>
                <tr>
                    <td class="hidden"><?php $userHistory->getDate()->getTimestamp(); ?></td>
                    <td><span class="label label-default label-inline"><?php echo __('Transactionnel'); ?></span></td>
                    <td><?php echo $mail->getSubject(); ?></td>
                    <td data-order="<?php echo $userHistory->getDate()->getTimestamp(); ?>">
                        <?php echo dateTimeFr($userHistory->getDate()->format('Y-m-d H:i:s')); ?>
                    </td>
                    <td data-order="<?php echo ($userHistory->getOpen() ? $userHistory->getDateOpen()->getTimestamp() : 0); ?>">
                        <?php if ($userHistory->getOpen()) : ?>
                            <?php echo dateTimeFr($userHistory->getDateOpen()->format('Y-m-d H:i:s')); ?>
                        <?php else : ?>
                            <span class="text-warning"><?php echo __('Non ouvert'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a class="btn btn-sm btn-clean btn-icon" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'mail', 'history/' . $mail->getId()); ?>"><i class="fa fa-search"></i></a>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php endif; ?>
