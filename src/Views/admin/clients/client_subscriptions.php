<?php /** @var \MatGyver\Entity\Client\Subscription\ClientSubscription[] $subscriptions */ ?>
<?php if (!$subscriptions) : ?>
    <div class="alert alert-custom alert-light-info"><?php echo __('Aucun abonnement trouvé pour ce client'); ?></div>
    <?php return; ?>
<?php endif; ?>

<div class="table-responsive">
    <table class="table table-separate table-head-custom">
        <thead>
            <tr>
                <th><?php echo __('Date'); ?></th>
                <th><?php echo __('Produit'); ?></th>
                <th><?php echo __('Abonnement'); ?></th>
                <th><?php echo __('Transaction d\'origine'); ?></th>
                <th><?php echo __('Etat'); ?></th>
                <th><?php echo __('Actions'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php krsort($subscriptions); ?>
            <?php foreach ($subscriptions as $subscription) : ?>
                <tr>
                    <td><?php echo dateFr($subscription->getDate()->format('Y-m-d H:i:s')); ?></td>
                    <td>
                        <?php if ($subscription->getProduct() and \MatGyver\Services\RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_PRODUCTS)) : ?>
                            <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'product/' . $subscription->getProduct()->getId()); ?>">
                        <?php endif; ?>
                        <?php echo $subscription->getProductName(); ?>
                        <?php if ($subscription->getProduct() and \MatGyver\Services\RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_PRODUCTS)) : ?>
                            </a>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($subscription->getSubscriptionId()) : ?>
                            <?php if (\MatGyver\Services\RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_SHOP)) : ?>
                                <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'subscription/' . $subscription->getSubscriptionId(), 't=' . $subscription->getTypeSubscription()); ?>" target="_blank"><?php echo $subscription->getTypeSubscription() . ' #' . $subscription->getSubscriptionId(); ?></a>
                            <?php else : ?>
                                <?php echo $subscription->getTypeSubscription() . ' #' . $subscription->getSubscriptionId(); ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if (\MatGyver\Services\RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_SHOP)) : ?>
                            <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'transaction/' . $subscription->getTransactionReference()); ?>" target="_blank"><?php echo $subscription->getTransactionReference(); ?></a>
                        <?php else : ?>
                            <?php echo $subscription->getTransactionReference(); ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($subscription->getStatus() == 'active') : ?>
                            <span class="label label-inline label-light-success"><?php echo __('Actif'); ?></span>
                        <?php else : ?>
                            <span class="label label-inline label-light-danger"><?php echo __('Désactivé'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($subscription->getStatus() == 'active' and \MatGyver\Services\RightsService::isGranted(ATTRIBUTE_EDIT, UNIVERSE_ADMIN_SAV)) : ?>
                        <form method="post" action="/<?php echo ADMIN_URL; ?>/client/subscription/desactive/">
                            [[CSRF]]
                            <input type="hidden" name="client_subscription_id" value="<?php echo $subscription->getId(); ?>">
                            <input type="hidden" name="client_id" value="<?php echo $subscription->getClient()->getId(); ?>">
                            <button type="submit" class="btn btn-sm btn-clean btn-hover-danger btn-icon" title="<?php echo __('Désactiver'); ?>" data-rel="tooltip"><i class="fa fa-power-off"></i></button>
                        </form>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
