<?php
$reasons = [];
foreach ($cancelReasons as $id => $cancelReason) {
    $reasons[$id] = ['title' => $cancelReason, 'nb' => 0];
}

$nb_reasons = 0;
foreach ($clientsCancel as $clientCancel) {
    if (!$clientCancel->getCancelReason()) {
        continue;
    }
    if (!isset($reasons[$clientCancel->getCancelReason()])) {
        continue;
    }

    $reasons[$clientCancel->getCancelReason()]['nb']++;
    $nb_reasons++;
}

array_sort($reasons, 'nb');

echo '
<table class="table table-condensed">
    <thead>
      <tr>
          <th>' . __('Raison') . '</th>
          <th></th>
          <th>' . __('% (Nb)') . '</th>
      </tr>
    </thead>
    <tbody>';

foreach ($reasons as $reason => $data) {
    $nb = $data['nb'];
    $pourcentage = 0;
    if ($nb and $nb_reasons) {
        $pourcentage = round($nb * 100 / $nb_reasons);
    }

    echo '
    <tr>
        <td>' . $data['title'] . '</td>
        <td style="width:500px">
            <div class="progress" style="margin-bottom:0">
              <div class="progress-bar" role="progressbar" aria-valuenow="' . $pourcentage . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $pourcentage . '%;">
                <span class="sr-only">' . $pourcentage . '%</span>
              </div>
            </div>
        </td>
        <td>' . $pourcentage . '% (' . $nb . ')</td>
    </tr>';
}

echo '
    </tbody>
</table>';
