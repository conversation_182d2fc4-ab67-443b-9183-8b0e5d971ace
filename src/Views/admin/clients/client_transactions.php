<?php /** @var \MatGyver\Entity\Client\Transaction\ClientTransaction[] $clientTransactions */ ?>
<table class="table table-separate table-head-custom datatable" id="table_commandes">
    <thead>
        <tr>
            <th><?php echo __('ID'); ?></th>
            <th><?php echo __('Date'); ?></th>
            <th><?php echo __('Produit'); ?></th>
            <th><?php echo __('TTC'); ?></th>
            <th><?php echo __('Etat'); ?></th>
            <th><?php echo __('Actions'); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($clientTransactions as $clientTransaction) : ?>
            <?php
            if (!$clientTransaction->getTransaction()) {
                continue;
            }
            $transaction = $clientTransaction->getTransaction();
            try {
                $transaction->getReference();
            } catch (\Exception $e) {
                continue;
            }
            ?>
            <tr>
                <td>
                    <?php if (\MatGyver\Services\RightsService::isGranted(ATTRIBUTE_VIEW, UNIVERSE_ADMIN_SHOP)) : ?>
                        <span class="font-weight-bolder"><a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'transaction/' . $transaction->getReference()); ?>" target="_blank"><?php echo $transaction->getId(); ?></a></span>
                    <?php else : ?>
                        <span class="font-weight-bolder"><?php echo $transaction->getId(); ?></span>
                    <?php endif; ?>
                </td>
                <td><?php echo dateTimeFr($transaction->getDate()->format('Y-m-d H:i:s')); ?></td>
                <td><?php echo $transaction->getProductName(); ?></td>
                <td><div class="font-weight-bolder text-primary mb-0"><?php echo \MatGyver\Helpers\Number::formatAmount($transaction->getAmountTaxIncl(), DEFAULT_CURRENCY); ?></div></td>
                <td><?php echo \MatGyver\Helpers\Transaction::displayTransactionStatus($transaction->getStatus(), true); ?></td>
                <td>
                    <a class="btn btn-sm btn-clean btn-icon" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'transaction/' . $transaction->getReference()); ?>" target="_blank"><i class="fa fa-search"></i></a>
                    <?php if ($_SESSION['user']['id'] == 1) : ?>
                        <form method="post" action="/<?php echo ADMIN_URL; ?>/client/error/add/" style="margin:0; padding:0;display: inline;">
                            [[CSRF]]
                            <input type="hidden" name="client_id" value="<?php echo $clientTransaction->getClient()->getId(); ?>">
                            <input type="hidden" name="id_order" value="<?php echo $transaction->getId(); ?>" />
                            <input type="hidden" name="id_customer" value="<?php echo ($transaction->getShopCustomer() ? $transaction->getShopCustomer()->getId() : '0'); ?>" />
                            <button type="submit" class="btn btn-sm btn-clean btn-hover-danger btn-icon" title="<?php echo __('Créer une relance avec cette commande'); ?>" data-rel="tooltip"><i class="fa fa-times"></i></button>
                        </form>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
