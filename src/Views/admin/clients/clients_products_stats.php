<div class="card card-custom gutter-b">
    <div class="card-header">
        <div class="card-title">
            <div class="card-label">
                <div class="font-weight-bolder"><?php echo __('Produits'); ?></div>
                <div class="font-size-sm text-muted mt-2"><?php echo __('Nombre de clients actifs actuellement (payants) et qui ont commandé au moins une fois les produits suivants'); ?></div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-separate table-head-custom" id="table_commandes2">
                <thead>
                    <tr>
                        <th><?php echo __('ID'); ?></th>
                        <th><?php echo __('Produit'); ?></th>
                        <th><?php echo __('Prix'); ?></th>
                        <th><?php echo __('Récurrence'); ?></th>
                        <th><?php echo __('Clients'); ?></th>
                        <th><?php echo __('Différence'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $clientsProducts = json_decode($clientsStats->getProducts(), true); ?>
                    <?php $clientsProductsLastMonth = ($clientsStatsLastMonth ? json_decode($clientsStatsLastMonth->getProducts(), true) : []); ?>
                    <?php foreach ($clientsProducts as $clientsProduct) : ?>
                        <tr>
                            <td><?php echo $clientsProduct['id']; ?></td>
                            <td><a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'product/' . $clientsProduct['id']); ?>"><?php echo $clientsProduct['name']; ?></a></td>
                            <td><?php echo \MatGyver\Helpers\Number::formatAmount($clientsProduct['price_tax_excl'], ($clientsProduct['currency'] ?? DEFAULT_CURRENCY)); ?> HT</td>
                            <td>
                                <?php if ($clientsProduct['recurring']) : ?>
                                    <?php if ($clientsProduct['duration'] == '12 month') : ?>
                                        <?php echo __('Annuel'); ?>
                                    <?php elseif ($clientsProduct['duration'] == '30 day' or $clientsProduct['duration'] == '1 month') : ?>
                                        <?php echo __('Mensuel'); ?>
                                    <?php elseif (str_contains($clientsProduct['duration'], 'month')) : ?>
                                        <?php $nbMonths = (int) str_replace(' month', '', $clientsProduct['duration']); ?>
                                        <?php echo n__('tous les mois', 'tous les %d mois', $nbMonths, $nbMonths); ?>
                                    <?php elseif (str_contains($clientsProduct['duration'], 'day')) : ?>
                                        <?php $nbDays = (int) str_replace(' day', '', $clientsProduct['duration']); ?>
                                        <?php echo n__('tous les jours', 'tous les %d jours', $nbDays, $nbDays); ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex justify-content-between">
                                    <span class="text-dark font-weight-bold"><?php echo $clientsProduct['nb_clients']; ?></span>
                                    <?php $pourcentage = ($clientsProduct['nb_clients'] ? round($clientsProduct['nb_clients'] * 100 / $clientsStats->getNbClients()) : 0); ?>
                                    <?php $pourcentage = min($pourcentage, 100); ?>
                                    <div style="width: 100%; padding: 7px 10px;">
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: <?php echo $pourcentage; ?>%;" aria-valuenow="<?php echo $pourcentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                    <span class="text-primary text-right" style="width: 40px;"><?php echo $pourcentage . '%'; ?></span>
                                </div>
                            </td>
                            <td>
                                <?php if ($clientsProductsLastMonth and isset($clientsProductsLastMonth[$clientsProduct['id']])) : ?>
                                    <?php $diff = $clientsProduct['nb_clients'] - $clientsProductsLastMonth[$clientsProduct['id']]['nb_clients']; ?>
                                    <span class="text text-<?php echo ($diff >= 0 ? 'success' : 'danger'); ?>"><?php echo ($diff >= 0 ? '+' : '') . $diff . ' (' . $clientsProductsLastMonth[$clientsProduct['id']]['nb_clients'] . ')'; ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
	</div>
</div>


