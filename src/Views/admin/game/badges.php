<div class="row">
    <?php /** @var \MatGyver\Entity\Game\GameBadge[] $badges */ ?>
    <?php foreach ($badges as $badge) : ?>
        <div class="col-md-3 col-sm-3">
            <div class="card gutter-b card-item card-custom card-stretch">
                <?php echo \MatGyver\Helpers\Game\GameReward::renderAsStickers($badge->getRewards(), $badge->getNbPoints()); ?>
                <div class="card-body">
                    <div class="d-flex justify-content-between flex-column pt-4 h-100">
                        <div class="d-flex flex-column flex-center">
                            <div class="symbol symbol-80 symbol-circle symbol-white overflow-hidden">
                                <span class="symbol-label">
                                    <img src="<?php echo $badge->getImage(); ?>" class="h-50px" alt="<?php echo $badge->getName(); ?>">
                                </span>
                            </div>
                            <h4 class="card-title font-weight-bolder text-dark-75 text-hover-primary m-0 pt-7 pb-5"><?php echo $badge->getName(); ?></h4>
                            <?php if ($badge->getDescription()) : ?>
                                <div class="description text-muted text-center mb-2">
                                    <?php echo $badge->getDescription(); ?>
                                </div>
                            <?php endif; ?>

                            <?php $conditions = $badge->getConditions(); ?>
                            <?php if ($conditions) : ?>
                                <div class="conditions">
                                    <?php foreach ($conditions as $condition) : ?>
                                        <span class="label label-inline label-pill label-outline-info mb-2 mr-2"><?php echo $condition->render(); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($badge->getActive()) : ?>
                                <span class="label label-inline label-light-success mt-4"><?php echo __('Activé'); ?></span>
                            <?php else : ?>
                                <span class="label label-inline label-light-danger mt-4"><?php echo __('Désactivé'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a class="btn btn-sm btn-clean btn-icon" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'game', 'badge/edit/' . $badge->getId()); ?>">
                        <i class="fas fa-pen"></i>
                    </a>
                    <a class="btn btn-sm btn-clean btn-hover-danger btn-icon" onclick="CreateConfirmationDeleteAlert('badge', <?php echo $badge->getId() ?>, '<?php echo __('Etes-vous sûr de vouloir supprimer ce badge ?') ?>', '', '', '<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'game', 'badge/delete/' . $badge->getId()); ?>');">
                        <i class="fas fa-trash"></i>
                    </a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>
