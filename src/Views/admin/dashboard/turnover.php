<div class="card card-custom card-stretch gutter-b">
    <div class="card-header border-0 pt-0">
        <div class="card-title">
            <div class="card-label">
                <div class="font-weight-bolder"><?php echo __('Chiffre d\'affaires'); ?></div>
            </div>
        </div>
    </div>
    <div class="card-body d-flex flex-column px-0 pb-0">
        <?php echo $turnoverGraph; ?>
        <div class="flex-grow-1 card-spacer-x pt-8">
            <div class="d-flex align-items-center justify-content-between mb-10">
                <div class="d-flex align-items-center mr-4">
                    <div class="symbol symbol-50 symbol-light mr-4 flex-shrink-0">
                        <div class="symbol-label">
                            <span class="svg-icon svg-icon-primary svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"/>
                                        <path d="M5,19 L20,19 C20.5522847,19 21,19.4477153 21,20 C21,20.5522847 20.5522847,21 20,21 L4,21 C3.44771525,21 3,20.5522847 3,20 L3,4 C3,3.44771525 3.44771525,3 4,3 C4.55228475,3 5,3.44771525 5,4 L5,19 Z" fill="#000000" fill-rule="nonzero"/>
                                        <path d="M8.7295372,14.6839411 C8.35180695,15.0868534 7.71897114,15.1072675 7.31605887,14.7295372 C6.9131466,14.3518069 6.89273254,13.7189711 7.2704628,13.3160589 L11.0204628,9.31605887 C11.3857725,8.92639521 11.9928179,8.89260288 12.3991193,9.23931335 L15.358855,11.7649545 L19.2151172,6.88035571 C19.5573373,6.44687693 20.1861655,6.37289714 20.6196443,6.71511723 C21.0531231,7.05733733 21.1271029,7.68616551 20.7848828,8.11964429 L16.2848828,13.8196443 C15.9333973,14.2648593 15.2823707,14.3288915 14.8508807,13.9606866 L11.8268294,11.3801628 L8.7295372,14.6839411 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div>
                        <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'clients', 'stats'); ?>" class="font-size-sm text-muted text-hover-primary font-weight-bold"><?php echo __('Chiffre d\'affaires de ce mois'); ?></a>
                        <div class="font-size-h5 text-dark-75 font-weight-bolder"><?php echo \MatGyver\Helpers\Number::formatAmount(($clientsStatThisMonth ? $clientsStatThisMonth->getTurnover() : 0), DEFAULT_CURRENCY); ?></div>
                    </div>
                </div>
                <?php if ($clientsStatThisMonth and $clientsStatLastMonth) : ?>
                    <?php $diff = $clientsStatThisMonth->getTurnover() - $clientsStatLastMonth->getTurnover(); ?>
                    <div class="label <?php echo ($diff >= 0 ? 'label-light-success' : 'label-light-danger'); ?> label-inline label-lg font-weight-bold py-4 px-3" data-rel="tooltip" data-title="<?php echo __('Par rapport au mois dernier') . ' (' . \MatGyver\Helpers\Number::formatAmount($clientsStatLastMonth->getTurnover(), DEFAULT_CURRENCY) . ')'; ?>">
                        <?php echo ($diff >= 0 ? '+' : '') . \MatGyver\Helpers\Number::formatAmount($diff, DEFAULT_CURRENCY); ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-flex align-items-center justify-content-between mb-10">
                <div class="d-flex align-items-center mr-2">
                    <div class="symbol symbol-50 symbol-light mr-4 flex-shrink-0">
                        <div class="symbol-label">
                            <span class="svg-icon svg-icon-primary svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"></rect>
                                        <rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5"></rect>
                                        <rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5"></rect>
                                        <rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5"></rect>
                                        <rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5"></rect>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div>
                        <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'clients', 'stats'); ?>" class="font-size-sm text-muted text-hover-primary font-weight-bold"><?php echo __('Chiffre d\'affaires depuis le %s', dateFr($dateStart)); ?></a>
                        <div class="font-size-h5 text-dark-75 font-weight-bolder"><?php echo \MatGyver\Helpers\Number::formatAmount($amountTaxExcl, DEFAULT_CURRENCY); ?></div>
                    </div>
                </div>
                <div class="label label-light-success label-inline label-lg font-weight-bold py-4 px-3" data-rel="tooltip" data-title="<?php echo __('Prévision à la fin de l\'année'); ?>">
                    <?php echo \MatGyver\Helpers\Number::formatAmount($finalTurnover, DEFAULT_CURRENCY); ?>
                </div>
            </div>

            <div class="d-flex align-items-center justify-content-between mb-10">
                <div class="d-flex align-items-center mr-2">
                    <div class="symbol symbol-50 symbol-light mr-4 flex-shrink-0">
                        <div class="symbol-label">
                            <span class="svg-icon svg-icon-primary svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"></rect>
                                        <rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5"></rect>
                                        <rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5"></rect>
                                        <rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5"></rect>
                                        <rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5"></rect>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div>
                        <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'clients', 'stats'); ?>" class="font-size-sm text-muted text-hover-primary font-weight-bold"><?php echo __('Objectif (%s)', \MatGyver\Helpers\Number::formatAmount($caTarget, DEFAULT_CURRENCY)); ?></a>
                        <div class="d-flex flex-row align-items-center">
                            <div class="font-size-h5 text-dark-75 font-weight-bolder mr-4"><?php echo (int) $pourcentage . '%'; ?></div>
                            <div class="progress progress-xs w-100">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo (int)$pourcentage; ?>%;" aria-valuenow="<?php echo (int)$pourcentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="label label-light-success label-inline label-lg font-weight-bold py-4 px-3" data-rel="tooltip" data-title="<?php echo __('CA attendu à la date actuelle'); ?>">
                    <?php echo \MatGyver\Helpers\Number::formatAmount($expectedTurnover, DEFAULT_CURRENCY); ?>
                </div>
            </div>

            <div class="d-flex align-items-center justify-content-between mb-10">
                <div class="d-flex align-items-center mr-2">
                    <div class="symbol symbol-50 symbol-light mr-4 flex-shrink-0">
                        <div class="symbol-label">
                            <span class="svg-icon svg-icon-primary svg-icon-2x">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                        <path d="M18,14 C16.3431458,14 15,12.6568542 15,11 C15,9.34314575 16.3431458,8 18,8 C19.6568542,8 21,9.34314575 21,11 C21,12.6568542 19.6568542,14 18,14 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"></path>
                                        <path d="M17.6011961,15.0006174 C21.0077043,15.0378534 23.7891749,16.7601418 23.9984937,20.4 C24.0069246,20.5466056 23.9984937,21 23.4559499,21 L19.6,21 C19.6,18.7490654 18.8562935,16.6718327 17.6011961,15.0006174 Z M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero"></path>
                                    </g>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div>
                        <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'clients', 'stats'); ?>" class="font-size-sm text-muted text-hover-primary font-weight-bold"><?php echo __('Clients'); ?></a>
                        <div class="font-size-h5 text-dark-75 font-weight-bolder"><?php echo ($clientsStatThisMonth ? $clientsStatThisMonth->getNbClients() : '-'); ?></div>
                    </div>
                </div>
                <?php if ($clientsStatThisMonth and $clientsStatLastMonth) : ?>
                    <?php $diff = $clientsStatThisMonth->getNbClients() - $clientsStatLastMonth->getNbClients(); ?>
                    <div class="label <?php echo ($diff >= 0 ? 'label-light-success' : 'label-light-danger'); ?> label-inline label-lg font-weight-bold py-4 px-3" data-rel="tooltip" data-title="<?php echo __('Par rapport au mois dernier') . ' (' . $clientsStatLastMonth->getNbClients() . ')'; ?>">
                        <?php echo ($diff >= 0 ? '+' : '') . $diff; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
