<div class="card card-custom gutter-b">
    <div class="card-header">
        <h3 class="card-title"><?php echo __('Facture'); ?></h3>
        <div class="card-toolbar">
            <a class="btn btn-sm btn-light-primary font-weight-bold mr-2" href="<?php echo $invoice->getFilePath(); ?>" target="_blank"><?php echo __('Voir la facture'); ?></a>
            <?php if (!$invoice->isRefused() and !$invoice->isPaid()) : ?>
                <div class="pull-right">
                    <?php if ($invoice->isValidated()) : ?>
                        <a class="btn btn-sm btn-light-danger font-weight-bold" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'affiliation', 'invoice/refuse/' . $invoice->getId()); ?>"><i class="fa fa-trash"></i> <?php echo __('Refuser'); ?></a>
                        <a class="btn btn-sm btn-light-success font-weight-bold" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'affiliation', 'invoice/edit/status/' . $invoice->getId() . '/' . \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_PAID) ?>"><i class="fa fa-check"></i> <?php echo __('Marquer comme payée'); ?></a>
                    <?php elseif ($invoice->isSended()) : ?>
                        <a class="btn btn-sm btn-light-success font-weight-bold" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'affiliation', 'invoice/edit/status/' . $invoice->getId() . '/' . \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_VALIDATED) ?>"><i class="fa fa-check"></i> <?php echo __('Valider'); ?></a>
                        <a class="btn btn-sm btn-light-danger font-weight-bold" href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'affiliation', 'invoice/refuse/' . $invoice->getId()) ?>"><i class="fa fa-trash"></i> <?php echo __('Refuser'); ?></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if(!$hasIban): ?>
            <div class="alert alert-custom alert-light-danger">
                <?php echo __('Client sans IBAN, facture à régler manuellement.') ?>
            </div>
        <?php endif; ?>

        <p>
            <strong><?php echo __('Partenaire') ?> :</strong> <a href="<?php echo \MatGyver\Helpers\Tools::makeLink('admin', 'affiliation', 'partner/' . $invoice->getPartner()->getId()); ?>"><?php echo $invoice->getPartner()->getLastName(); ?> <?php echo $invoice->getPartner()->getFirstName(); ?></a><br>
            <strong><?php echo __('Date d\'envoi') ?> :</strong> <?php echo dateFr($invoice->getUploadedAt()->format('Y-m-d')); ?><br>
            <strong><?php echo __('Montant HT') ?> :</strong> <?php echo \MatGyver\Helpers\Number::formatAmount($invoice->getPreTaxAmount(), DEFAULT_CURRENCY); ?><br>
            <strong><?php echo __('TVA') ?> :</strong> <?php echo $invoice->getVatPercent(); ?>&#37;<br>
            <strong><?php echo __('Montant TTC') ?> :</strong> <?php echo \MatGyver\Helpers\Number::formatAmount($invoice->getPostTaxAmount(), DEFAULT_CURRENCY); ?><br>
            <strong><?php echo __('Statut') ?> :</strong>
            <?php $status = $invoice->getStatus(); ?>
            <?php if ($status == \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_SENT) : ?>
                <label class="label label-info label-inline"><?php echo $invoice->getDisplayStatus(); ?></label>
            <?php elseif ($status == \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_VALIDATED) : ?>
                <label class="label label-warning label-validated label-inline"><?php echo $invoice->getDisplayStatus(); ?></label>
            <?php elseif ($status == \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_REFUSED) : ?>
                <label class="label label-danger label-inline"><?php echo $invoice->getDisplayStatus(); ?></label>
            <?php elseif ($status == \MatGyver\Entity\Affiliation\Invoice\AffiliationInvoice::STATUS_PAID) : ?>
                <label class="label label-success label-inline"><?php echo $invoice->getDisplayStatus(); ?></label>
            <?php endif; ?>
        </p>

        <?php if ($invoice->isRefused()) : ?>
        <p>
            <strong><?php echo __('Motif de refus') ?> :</strong>
            <?php if (isset($refusalConditions[$invoice->getRefusedReason()])) : ?>
                <?php echo $refusalConditions[$invoice->getRefusedReason()]; ?>
            <?php else : ?>
                <?php echo __('Raison inconnue'); ?>
            <?php endif; ?>
        </p>
        <?php endif; ?>
    </div>
</div>

<div class="card card-custom gutter-b">
    <div class="card-header">
        <h3 class="card-title"><?php echo __('Commissions'); ?></h3>
    </div>
    <div class="card-body">
        <table class="table table-separate table-head-custom" id="table_commissions">
            <thead>
            <tr>
                <td><?php echo __('Id') ?></td>
                <td><?php echo __('Produit') ?></td>
                <td><?php echo __('Commission') ?></td>
                <td><?php echo __('Date') ?></td>
                <td><?php echo __('Type') ?></td>
            </tr>
            </thead>
            <tbody>
            <?php foreach($invoice->getCommissions() as $commission): ?>
                <tr>
                    <td><?php echo $commission->getId() ?></td>
                    <td><?php echo $commission->getProduct() ?></td>
                    <td><?php echo \MatGyver\Helpers\Number::formatAmount($commission->getCommission(), DEFAULT_CURRENCY); ?></td>
                    <td><?php echo dateFr($commission->getDate()->format('Y-m-d')); ?></td>
                    <td><?php echo $commission->displayCommissionType(); ?></td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
