 <div class="cursor-pointer shadow-xs reply note bg-light-info toggle-off" id="note<?php echo $idNote; ?>" data-inbox="message">
    <div class="d-flex align-items-center card-spacer-x py-6 reply-header">
        <span class="symbol symbol-50 mr-4">
            <span class="symbol-label" style="background-image: url('<?php echo $avatarUrl; ?>')"></span>
        </span>
        <div class="d-flex flex-column flex-grow-1 flex-wrap mr-2">
            <div class="d-flex">
                <a href="#" class="font-size-lg font-weight-bolder text-dark-75 text-hover-primary mr-2"><?php echo $name; ?></a>
                <div class="font-weight-bold text-muted mr-2">
                    <span class="label label-success label-dot mr-2"></span>
                    <?php echo humanizeTime(time() - strtotime($date), true); ?>
                </div>
                <div class="mr-2">
                    <label class="label label-inline label-info"><?php echo __('Note'); ?></label>
                </div>
            </div>
            <div class="d-flex flex-column">
                <div class="text-muted font-weight-bold toggle-on-item" data-inbox="toggle"><?php echo $lightContent; ?></div>
            </div>
        </div>
        <div class="d-flex align-items-center">
            <div class="font-weight-bold text-muted mr-4"><?php echo dateTimeFr($date); ?></div>
            <div class="d-flex align-items-center">
                <span class="btn btn-clean btn-sm btn-icon btn-hover-primary" onclick="UseNote(<?php echo $idNote; ?>);" data-rel="tooltip" data-placement="top" title="<?php echo __('Utiliser'); ?>">
                    <i class="far fa-arrow-alt-circle-down icon-1x"></i>
                </span>
            </div>
            <?php if (\MatGyver\Services\RightsService::isGranted(ATTRIBUTE_DELETE, UNIVERSE_ADMIN_SAV)) : ?>
            <div class="d-flex align-items-center">
                <span class="btn btn-clean btn-sm btn-icon btn-hover-danger" onclick="CreateConfirmationDeleteNote('<?php echo $ticketId; ?>', '<?php echo $idNote; ?>');" data-rel="tooltip" data-placement="top" title="<?php echo __('Supprimer'); ?>">
                    <i class="fas fa-trash icon-1x"></i>
                </span>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-spacer-x py-3 toggle-off-item note-content"><?php echo $content; ?></div>
</div>
