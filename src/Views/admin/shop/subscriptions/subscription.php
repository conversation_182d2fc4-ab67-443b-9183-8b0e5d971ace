<?php
/** @var \MatGyver\Entity\PaymentMethod\PaymentMethodSubscription $subscription */
$container = \MatGyver\Services\DI\ContainerBuilderService::getInstance();

echo '
<div class="d-flex flex-row">
    <div class="flex-row-auto offcanvas-mobile w-300px w-xl-350px">';

//widget user
if ($user) {
    echo $container->get(\MatGyver\Services\WidgetsService::class)->displayWidgetUser($user, $userConfig);
}

//widget products
if ($transaction) {
    echo $container->get(\MatGyver\Services\WidgetsService::class)->displayWidgetTransaction($transaction);
    echo $container->get(\MatGyver\Services\WidgetsService::class)->displayWidgetProducts($transaction);
}

echo '
    </div>
    <div class="flex-row-fluid ml-lg-8">';

//etat
if (!$subscription->getValid() and $subscription->getResult() and \MatGyver\Services\RightsService::isSuperAdmin()) {
    echo '
        <div class="card card-custom gutter-b">
            <div class="card-header">
                <h3 class="card-title">' . __('Abonnement en erreur') . '</h3>
            </div>
            <div class="card-body">
                <p><i class="fa fa-flag"></i> <strong>' . __('Détail de l\'erreur') . ' : </strong>' . $subscription->getResult() . '</p>
            </div>
        </div>';
}

echo '
    <div class="card card-custom gutter-b">
        <div class="card-header">
            <h3 class="card-title">' . n__('Transaction associée à cet abonnement', 'Transactions associées à cet abonnement', count($charges)) . '</h3>
        </div>
        <div class="card-body">';

if (!$charges) {
    echo '<div class="alert alert-custom alert-light-info">' . __('Aucune transaction associée à cet abonnement') . '</div>';
} else {
    echo '
        <div class="timeline timeline-5">
            <div class="timeline-items">';

    foreach ($charges as $charge) {
        $id_order = $charge->getTransactionReference();

        $transaction = $container->get(\MatGyver\Services\Shop\Transaction\ShopTransactionService::class)->getTransactionByReference($id_order);
        if (!$transaction) {
            continue;
        }

        $class = \MatGyver\Helpers\Transaction::getTransactionStatusClass($transaction->getStatus());
        $icon = \MatGyver\Helpers\Transaction::getTransactionStatusIcon($transaction->getStatus());
        $status = \MatGyver\Helpers\Transaction::displayTransactionStatus($transaction->getStatus());

        echo '
            <div class="timeline-item">
                <div class="timeline-media bg-light-' . $class . '">
                    <i class="fas ' . $icon . ' text-' . $class . '" style="font-size: 1.2rem;"></i>
                </div>
                <div class="timeline-desc timeline-desc-light-' . $class . '">
                    <a class="font-weight-bolder text-hover-primary" href="' . \MatGyver\Helpers\Tools::makeLink('admin', 'shop', 'transaction/' . $id_order) . '">' . __('Transaction #%s', $id_order) . '</a>
                    <p class="font-weight-normal text-dark-50 pb-2">
                        ' . dateTimeFr($transaction->getDate()->format('Y-m-d H:i:s')) . '<br>
                        ' . $status . '<br>
                        <span class="font-weight-bolder mb-0">' . \MatGyver\Helpers\Number::formatAmount($transaction->getAmountTaxIncl(), $transaction->getCurrency()) . '</span>
                    </p>
                </div>
            </div>';
    }

    echo '
        </div>
    </div>';
}

echo '
            </div>
        </div>
    </div>
</div>';
