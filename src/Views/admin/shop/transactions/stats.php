<?php echo $panelStats; ?>

<?php echo $chartOrders; ?>

<?php echo $chartNbOrders; ?>

<?php echo $chart; ?>

<div class="card card-custom gutter-t">
    <div class="card-header">
        <div class="card-title">
            <span class="card-icon">
                <i class="fa fa-tags"></i>
            </span>
            <h3 class="card-label"><?php echo __('Produits'); ?></h3>
        </div>
    </div>
    <div class="card-body">
        <?php if (!$transactions) : ?>
        <div class="alert alert-custom alert-light-info"><?php echo __('Aucune transaction trouvée.'); ?></div>
        <?php  else : ?>
        <table class="table table-separate table-head-custom datatable">
            <thead>
                <tr>
                    <th><?php echo __('Produit'); ?></th>
                    <th><?php echo __('Ventes'); ?></th>
                    <th><?php echo __('Montant'); ?></th>
                    <th class="hidden"></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transactions as $transaction) : ?>
                    <?php $amount = number_format(round($transaction['totalTaxExcl'], 2), 2, '.', ''); ?>
                    <tr>
                        <td><?php echo $transaction['productName']; ?></td>
                        <td><?php echo $transaction['nbSales']; ?></td>
                        <td><?php echo $amount; ?></td>
                        <td class="hidden"></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
</div>

<div class="card card-custom gutter-t">
    <div class="card-header">
        <div class="card-title">
            <span class="card-icon">
                <i class="fa fa-credit-card"></i>
            </span>
            <h3 class="card-label"><?php echo __('Moyens de paiement'); ?></h3>
        </div>
    </div>
    <div class="card-body">
        <?php if (!$transactionsPayments) : ?>
            <div class="alert alert-custom alert-light-info"><?php echo __('Aucune transaction trouvée.'); ?></div>
        <?php else : ?>
        <table class="table table-separate table-head-custom datatable">
            <thead>
                <tr>
                    <th><?php echo __('Moyen de paiement'); ?></th>
                    <th><?php echo __('Ventes'); ?></th>
                    <th><?php echo __('Montant'); ?></th>
                    <th class="hidden"></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transactionsPayments as $transactionPayment) : ?>
                    <?php $amount = number_format(round($transactionPayment['totalTaxExcl'], 2), 2, '.', ''); ?>
                    <tr>
                        <td><?php echo \MatGyver\Helpers\Transaction::displayTransactionPaymentMethod($transactionPayment['paymentMethod']); ?></td>
                        <td><?php echo $transactionPayment['nbSales']; ?></td>
                        <td><?php echo $amount; ?></td>
                        <td class="hidden"></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
</div>
