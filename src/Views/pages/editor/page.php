<style type="text/css" id="page-style"></style>

<div class="page-loading">
    <div class="page-loader">
        <div class="spinner spinner-primary"></div>
    </div>
</div>

<div class="header-container">
    <div class="header-container-overlay"></div>
    <div class="d-flex flex-column flex-root">
        <div class="d-flex flex-row flex-column-fluid page" id="content">
            <div class="d-flex flex-column flex-row-fluid wrapper" id="page-editor">
                <?php echo $sidebar; ?>
                <div class="page-content">
                    <?php if ($similarPages) : ?>
                        <div class="card card-similar-pages bg-dark">
                            <div class="card-header card-header-tabs-line">
                                <div class="card-toolbar">
                                <ul class="nav nav-tabs nav-tabs-line nav-tabs-inverse">
                                    <?php foreach ($similarPages as $similarPage) : ?>
                                        <li class="nav-item">
                                            <a class="nav-link <?php echo ($currentClass == $similarPage['class'] ? 'active' : ''); ?>" href="<?php echo $similarPage['link']; ?>">
                                                <span class="nav-text"><?php echo $similarPage['title']; ?></span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php echo $pageContent; ?>
                </div>
            </div>
        </div>
    </div>
</div>
[[CSRF]]

<div id="devices" class="d-none">
    <div class="close">
        <a onclick="hideDevices();" class="btn btn-clean btn-icon btn-lg">
            <i class="ki ki-close"></i>
        </a>
    </div>

    <div class="devices-row d-flex flex-column justify-content-center align-items-center">
        <div class="d-flex flew-row align-items-end">
            <div id="iphone-x" class="text-center mr-5">
                <div class="device device-iphone-x">
                    <div class="device-frame"></div>
                    <div class="device-stripe"></div>
                    <div class="device-header"></div>
                    <div class="device-sensors"></div>
                    <div class="device-btns"></div>
                    <div class="device-power"></div>
                    <div class="device-home"></div>
                </div>
            </div>

            <div id="ipad-pro" class="text-center ml-5">
                <div class="device device-ipad-pro device-spacegray">
                    <div class="device-frame"></div>
                    <div class="device-stripe"></div>
                    <div class="device-header"></div>
                    <div class="device-sensors"></div>
                    <div class="device-btns"></div>
                    <div class="device-power"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var pageType = '<?php echo $pageType; ?>';
    var objectId = '<?php echo $objectId; ?>';
    <?php if ($pageType != 'email' and isset($pageLink) and $pageLink) : ?>
        var pageLink = '<?php echo $pageLink; ?>';
    <?php endif; ?>
</script>
