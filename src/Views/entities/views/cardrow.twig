namespace MatGyver\Helpers\View\CardRow{% if nameSpace %}\{% endif %}{{ nameSpace|trim('\\', 'right') }};

use Mat<PERSON>yver\Entity\{{ nameSpace }}{{ entityName }};
use MatGyver\Helpers\Number;
use Mat<PERSON>yver\Helpers\Tools;
use MatGyver\Helpers\View\Action;
{% if nameSpace %}
use MatGyver\Helpers\View\CardRow\ViewCardRowHelper;
{% endif %}
use MatGyver\Helpers\View\CardRow\CardRow;
use MatGyver\Helpers\View\Stat;

class {{ entityName }}ViewHelper extends ViewCardRowHelper
{
    /**
     * @param {{ entityName }}[] ${{ entityName[:1]|lower ~ entityName[1:] }}s
     * @return string
     */
    public function getContent(array ${{ entityName[:1]|lower ~ entityName[1:] }}s): string
    {
        $this->setCards(${{ entityName[:1]|lower ~ entityName[1:] }}s);
        return $this->getCardsContent();
    }

    /**
     * @param {{ entityName }}[] ${{ entityName[:1]|lower ~ entityName[1:] }}s
     * @return void
     */
    public function setCards(array ${{ entityName[:1]|lower ~ entityName[1:] }}s): void
    {
        foreach (${{ entityName[:1]|lower ~ entityName[1:] }}s as ${{ entityName[:1]|lower ~ entityName[1:] }}) {
            $cardRow = new CardRow();
            $cardRow->setTitle(${{ entityName[:1]|lower ~ entityName[1:] }}->get{{ nameVar }}());

{% if hasImage %}
            $cardRow->setImage(${{ entityName[:1]|lower ~ entityName[1:] }}->getImage());
{% else %}
            $cardRow->setIcon('
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <polygon points="0 0 24 0 24 24 0 24"></polygon>
                    <path d="M12.9336061,16.072447 L19.36,10.9564761 L19.5181585,10.8312381 C20.1676248,10.3169571 20.2772143,9.3735535 19.7629333,8.72408713 C19.6917232,8.63415859 19.6104327,8.55269514 19.5206557,8.48129411 L12.9336854,3.24257445 C12.3871201,2.80788259 11.6128799,2.80788259 11.0663146,3.24257445 L4.47482784,8.48488609 C3.82645598,9.00054628 3.71887192,9.94418071 4.23453211,10.5925526 C4.30500305,10.6811601 4.38527899,10.7615046 4.47382636,10.8320511 L4.63,10.9564761 L11.0659024,16.0730648 C11.6126744,16.5077525 12.3871218,16.5074963 12.9336061,16.072447 Z" fill="#000000" fill-rule="nonzero"></path>
                    <path d="M11.0563554,18.6706981 L5.33593024,14.122919 C4.94553994,13.8125559 4.37746707,13.8774308 4.06710397,14.2678211 C4.06471678,14.2708238 4.06234874,14.2738418 4.06,14.2768747 L4.06,14.2768747 C3.75257288,14.6738539 3.82516916,15.244888 4.22214834,15.5523151 C4.22358765,15.5534297 4.2250303,15.55454 4.22647627,15.555646 L11.0872776,20.8031356 C11.6250734,21.2144692 12.371757,21.2145375 12.909628,20.8033023 L19.7677785,15.559828 C20.1693192,15.2528257 20.2459576,14.6784381 19.9389553,14.2768974 C19.9376429,14.2751809 19.9363245,14.2734691 19.935,14.2717619 L19.935,14.2717619 C19.6266937,13.8743807 19.0546209,13.8021712 18.6572397,14.1104775 C18.654352,14.112718 18.6514778,14.1149757 18.6486172,14.1172508 L12.9235044,18.6705218 C12.377022,19.1051477 11.6029199,19.1052208 11.0563554,18.6706981 Z" fill="#000000" opacity="0.3"></path>
                </g>
            </svg>');
{% endif %}

            //actions
            $cardRow->setActions($this->getActions(${{ entityName[:1]|lower ~ entityName[1:] }}));

            //stats
            $cardRow->addStat($this->getStat1(${{ entityName[:1]|lower ~ entityName[1:] }}));
            $cardRow->addStat($this->getStat2(${{ entityName[:1]|lower ~ entityName[1:] }}));

            $cardRow->setMainContent('
            <a href="#" target="_blank" class="text-dark-50 text-hover-primary font-weight-bold mr-2 mb-0">
                <i class="flaticon-imac mr-2 font-size-lg"></i> Main content
            </a>');

            $cardRow->setButtonText(__('Voir {% if gender == 'm' %}le{% else %}la{% endif %} {{ name }}'));

            $this->addCard($cardRow);
        }
    }

    /**
     * @param {{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}
     * @return Stat
     */
    public function getStat1({{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}): Stat
    {
        $stat = new Stat();
        $stat->setIcon('<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <rect x="0" y="0" width="24" height="24"></rect>
                <path d="M12,4.56204994 L7.76822128,9.6401844 C7.4146572,10.0644613 6.7840925,10.1217854 6.3598156,9.76822128 C5.9355387,9.4146572 5.87821464,8.7840925 6.23177872,8.3598156 L11.2317787,2.3598156 C11.6315738,1.88006147 12.3684262,1.88006147 12.7682213,2.3598156 L17.7682213,8.3598156 C18.1217854,8.7840925 18.0644613,9.4146572 17.6401844,9.76822128 C17.2159075,10.1217854 16.5853428,10.0644613 16.2317787,9.6401844 L12,4.56204994 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"></path>
                <path d="M3.5,9 L20.5,9 C21.0522847,9 21.5,9.44771525 21.5,10 C21.5,10.132026 21.4738562,10.2627452 21.4230769,10.3846154 L17.7692308,19.1538462 C17.3034221,20.271787 16.2111026,21 15,21 L9,21 C7.78889745,21 6.6965779,20.271787 6.23076923,19.1538462 L2.57692308,10.3846154 C2.36450587,9.87481408 2.60558331,9.28934029 3.11538462,9.07692308 C3.23725479,9.02614384 3.36797398,9 3.5,9 Z M12,17 C13.1045695,17 14,16.1045695 14,15 C14,13.8954305 13.1045695,13 12,13 C10.8954305,13 10,13.8954305 10,15 C10,16.1045695 10.8954305,17 12,17 Z" fill="#000000"></path>
            </g>
        </svg>');
        $stat->setTitle(__('Stat 1'));
        $stat->setIconColor(Stat::ICON_COLOR_SUCCESS);
        $stat->setValue(Number::formatAmount(0, DEFAULT_CURRENCY));
        return $stat;
    }

    /**
     * @param {{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}
     * @return Stat
     */
    public function getStat2({{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}): Stat
    {
        $stat = new Stat();
        $stat->setIcon('<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <rect x="0" y="0" width="24" height="24"></rect>
                <rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5"></rect>
                <rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5"></rect>
                <rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5"></rect>
                <rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5"></rect>
            </g>
        </svg>');
        $stat->setTitle(__('Stat 2'));
        $stat->setIconColor(Stat::ICON_COLOR_INFO);
        $stat->setValue(Number::formatAmount(0, DEFAULT_CURRENCY));
        return $stat;
    }

    /**
     * @param {{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}
     * @return Action[]
     */
    public function getActions({{ entityName }} ${{ entityName[:1]|lower ~ entityName[1:] }}): array
    {
        $actions = [];

        $action = new Action();
        $action->setTitle(__('Modifer'));
        $action->setIcon('fas fa-pen');
        $action->setHref(Tools::makeLink('{{ controller }}', '{{ route }}', 'edit/' . ${{ entityName[:1]|lower ~ entityName[1:] }}->getId()));
        $actions[] = $action;

        $action = new Action();
        $action->setDivider(true);
        $actions[] = $action;

{% if hasActive %}
        $action = new Action();
        $action->setIcon('fas fa-power-off');
        if (${{ entityName[:1]|lower ~ entityName[1:] }}->getActive()) {
            $action->setTitle(__('Désactiver'));
            $action->setOnClickDelete('disable', ${{ entityName[:1]|lower ~ entityName[1:] }}->getId(), __('Êtes-vous sûr de vouloir désactiver ce{% if gender == 'f' %}tte{% endif %} {{ name }} ?'), __('Désactiver'), Tools::makeLink('{{ controller }}', '{{ route }}', 'disable/' . ${{ entityName[:1]|lower ~ entityName[1:] }}->getId()));
        } else {
            $action->setTitle(__('Activer'));
            $action->setOnClickDelete('enable', ${{ entityName[:1]|lower ~ entityName[1:] }}->getId(), __('Êtes-vous sûr de vouloir activer ce{% if gender == 'f' %}tte{% endif %} {{ name }} ?'), __('Activer'), Tools::makeLink('{{ controller }}', '{{ route }}', 'enable/' . ${{ entityName[:1]|lower ~ entityName[1:] }}->getId()));
        }
        $actions[] = $action;
{% endif %}

        $action = new Action();
        $action->setTitle(__('Supprimer'));
        $action->setIcon('fas fa-trash');
        $action->setHref(Tools::makeLink('{{ controller }}', '{{ route }}', 'delete/' . ${{ entityName[:1]|lower ~ entityName[1:] }}->getId()));
        $action->setClass('text-danger');
        $actions[] = $action;

        return $actions;
    }
}
