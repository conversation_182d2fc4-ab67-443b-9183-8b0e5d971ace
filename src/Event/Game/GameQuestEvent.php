<?php

namespace Mat<PERSON>yver\Event\Game;

use Mat<PERSON>yver\Entity\Game\GameQuest;
use Symfony\Contracts\EventDispatcher\Event;

class GameQuestEvent extends Event
{
    const QUEST_COMPLETED = 'quest.completed';

    /**
     * @var GameQuest
     */
    protected GameQuest $quest;

    public function __construct(GameQuest $quest)
    {
        $this->quest = $quest;
    }

    public function getQuest(): GameQuest
    {
        return $this->quest;
    }
}
