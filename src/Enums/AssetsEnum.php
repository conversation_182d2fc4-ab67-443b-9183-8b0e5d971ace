<?php

namespace MatGyver\Enums;

enum AssetsEnum: string
{
    case CATEGORY_FINANCIER = 'category_financier';
    case CATEGORY_OTHERS = 'category_others';
    case CATEGORY_PROFESSIONNELS = 'category_professionnels';
    case CATEGORY_IMMOBILIER = 'category_immobilier';

    case TYPE_BIENS_D_USAGE = 'biens_d_usage';
    case TYPE_SCPI = 'scpi';
    case TYPE_BIENS_PROFESSIONNELS = 'biens_professionnels';
    case TYPE_IMMOBILIER = 'immobilier';
    case TYPE_PLACEMENTS_FONCIERS = 'placements_fonciers';
    case TYPE_DISPONIBILITES = 'disponibilites';
    case TYPE_VALEURS_MOBILIERES = 'valeurs_mobilieres';
    case TYPE_ASSURANCE_VIE = 'assurance_vie';
    case TYPE_EPARGNE_RETRAITE_ET_SALARIALE = 'epargne_retraite_et_salariale';

    case RESIDENCE_PRINCIPALE = 'residence_principale';
    case RESIDENCES_SECONDAIRES = 'residences_secondaires';
    case TERRAINS = 'terrains';
    case AUTRES_BIENS_D_USAGE = 'autres_biens_d_usage';
    case MEUBLES_MEUBLANTS = 'meubles_meublants';

    case IMMOBILIER_LOCATIF = 'immobilier_locatif';
    case IMMOBILIER_LOCATIF_LOI_PINEL = 'immobilier_locatif_loi_pinel';
    case IMMOBILIER_LOCATIF_LOI_PINEL_OUTRE_MER = 'immobilier_locatif_loi_pinel_outre_mer';
    case IMMOBILIER_LOCATIF_LOI_DUFLOT = 'immobilier_locatif_loi_duflot';
    case IMMOBILIER_LOCATIF_LOI_DUFLOT_OUTRE_MER = 'immobilier_locatif_loi_duflot_outre_mer';
    case IMMOBILIER_LOCATIF_LOI_SCELLIER = 'immobilier_locatif_loi_scellier';
    case IMMOBILIER_LOCATIF_LOI_SCELLIER_BBC = 'immobilier_locatif_loi_scellier_bbc';
    case IMMOBILIER_LOCATIF_LOI_SCELLIER_DOM_TOM = 'immobilier_locatif_loi_scellier_dom_tom';
    case IMMOBILIER_LOCATIF_LOI_DEMESSINE = 'immobilier_locatif_loi_demessine';
    case IMMOBILIER_LOCATIF_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_LIBRE = 'immobilier_locatif_loi_girardin_logement_neuf_secteur_libre';
    case IMMOBILIER_LOCATIF_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_INTERMEDIAIRE = 'immobilier_locatif_loi_girardin_logement_neuf_secteur_intermediaire';
    case IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_CLASSIQUE_HORS_ZRR = 'immobilier_locatif_loi_de_robien_classique_hors_zrr';
    case IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_CLASSIQUE_EN_ZRR = 'immobilier_locatif_loi_de_robien_classique_en_zrr';
    case IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_RECENTRE_HORS_ZRR = 'immobilier_locatif_loi_de_robien_recentre_hors_zrr';
    case IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_RECENTRE_EN_ZRR = 'immobilier_locatif_loi_de_robien_recentre_en_zrr';
    case IMMOBILIER_LOCATIF_LOI_BORLOO_NEUF = 'immobilier_locatif_loi_borloo_neuf';
    case IMMOBILIER_LOCATIF_LOI_BORLOO_ANCIEN_SOCIAL = 'immobilier_locatif_loi_borloo_ancien_social';
    case IMMOBILIER_LOCATIF_LOI_BORLOO_ANCIEN_INTERMEDIAIRE = 'immobilier_locatif_loi_borloo_ancien_intermediaire';
    case IMMOBILIER_LOCATIF_LOI_BESSON_NEUF = 'immobilier_locatif_loi_besson_neuf';
    case IMMOBILIER_LOCATIF_LOI_BESSON_ANCIEN = 'immobilier_locatif_loi_besson_ancien';
    case IMMOBILIER_LOCATIF_LOI_PERISSOL = 'immobilier_locatif_loi_perissol';
    case IMMOBILIER_LOCATIF_LOI_MALRAUX = 'immobilier_locatif_loi_malraux';
    case IMMOBILIER_LOCATIF_MONUMENTS_HISTORIQUES = 'immobilier_locatif_monuments_historiques';
    case IMMOBILIER_LMP = 'immobilier_lmp';
    case IMMOBILIER_LMNP = 'immobilier_lmnp';
    case IMMOBILIER_LMNP_LOI_BOUVARD = 'immobilier_lmnp_loi_bouvard';
    case PARTS_DE_SCI = 'parts_de_sci';

    case PARTS_DE_SCPI = 'parts_de_scpi';
    case PARTS_DE_SCPI_LOI_PINEL = 'parts_de_scpi_loi_pinel';
    case PARTS_DE_SCPI_LOI_PINEL_OUTRE_MER = 'parts_de_scpi_loi_pinel_outre_mer';
    case PARTS_DE_SCPI_LOI_DUFLOT = 'parts_de_scpi_loi_duflot';
    case PARTS_DE_SCPI_LOI_DUFLOT_OUTRE_MER = 'parts_de_scpi_loi_duflot_outre_mer';
    case PARTS_DE_SCPI_LOI_SCELLIER = 'parts_de_scpi_loi_scellier';
    case PARTS_DE_SCPI_LOI_SCELLIER_BBC = 'parts_de_scpi_loi_scellier_bbc';
    case PARTS_DE_SCPI_LOI_SCELLIER_DOM_TOM = 'parts_de_scpi_loi_scellier_dom_tom';
    case PARTS_DE_SCPI_LOI_DEMESSINE = 'parts_de_scpi_loi_demessine';
    case PARTS_DE_SCPI_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_LIBRE = 'parts_de_scpi_loi_girardin_logement_neuf_secteur_libre';
    case PARTS_DE_SCPI_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_INTERMEDIAIRE = 'parts_de_scpi_loi_girardin_logement_neuf_secteur_intermediaire';
    case PARTS_DE_SCPI_LOI_DE_ROBIEN_CLASSIQUE_HORS_ZRR = 'parts_de_scpi_loi_de_robien_classique_hors_zrr';
    case PARTS_DE_SCPI_LOI_DE_ROBIEN_CLASSIQUE_EN_ZRR = 'parts_de_scpi_loi_de_robien_classique_en_zrr';
    case PARTS_DE_SCPI_LOI_DE_ROBIEN_RECENTRE_HORS_ZRR = 'parts_de_scpi_loi_de_robien_recentre_hors_zrr';
    case PARTS_DE_SCPI_LOI_DE_ROBIEN_RECENTRE_EN_ZRR = 'parts_de_scpi_loi_de_robien_recentre_en_zrr';
    case PARTS_DE_SCPI_LOI_BORLOO_NEUF = 'parts_de_scpi_loi_borloo_neuf';
    case PARTS_DE_SCPI_LOI_BORLOO_ANCIEN_SOCIAL = 'parts_de_scpi_loi_borloo_ancien_social';
    case PARTS_DE_SCPI_LOI_BORLOO_ANCIEN_INTERMEDIAIRE = 'parts_de_scpi_loi_borloo_ancien_intermediaire';
    case PARTS_DE_SCPI_LOI_BESSON_NEUF = 'parts_de_scpi_loi_besson_neuf';
    case PARTS_DE_SCPI_LOI_BESSON_ANCIEN = 'parts_de_scpi_loi_besson_ancien';
    case PARTS_DE_SCPI_LOI_PERISSOL = 'parts_de_scpi_loi_perissol';
    case PARTS_DE_SCPI_LOI_MALRAUX = 'parts_de_scpi_loi_malraux';
    case PARTS_DE_SCPI_MONUMENTS_HISTORIQUES = 'parts_de_scpi_monuments_historiques';

    case ENTREPRENEUR_INDIVIDUEL = 'entrepreneur_individuel';
    case EIRL = 'eirl';
    case AUTO_ENTREPRENEUR = 'auto_entrepreneur';
    case SARL = 'sarl';
    case EURL = 'eurl';
    case SAS = 'sas';
    case SASU = 'sasu';
    case SA = 'sa';
    case SNC = 'snc';
    case SCS = 'scs';
    case SC = 'sc';
    case FONDS_DE_COMMERCE_CLIENTELES = 'fonds_de_commerce_clienteles';
    case IMMEUBLES_PROFESSIONNELS = 'immeubles_professionnels';
    case AUTRES_BIENS_PROFESSIONNELS = 'autres_biens_professionnels';

    case METAUX_PRECIEUX = 'metaux_precieux';
    case CRYPTOMONNAIES = 'cryptomonnaies';
    case BOIS_ET_FORETS = 'bois_et_forets';
    case OBJETS_D_ART_ET_ANTIQUITES = 'objets_d_art_et_antiquites';
    case OR_PHYSIQUE = 'or_physique';
    case PARTS_DE_GROUPES_FORESTIERS = 'parts_de_groupes_forestiers';
    case PARTS_DE_STES_D_EPARGNE_FORESTIERE = 'parts_de_stes_d_epargne_forestiere';
    case TERRAINS_AGRICOLES = 'terrains_agricoles';
    case PARTS_DE_GFA_GAF_GFV_ET_GFR = 'parts_de_gfa_gaf_gfv_et_gfr';
    case BIENS_RURAUX_LOUES_A_LONG_TERME = 'biens_ruraux_loues_a_long_terme';
    case AUTRES_PLACEMENTS_DIVERS = 'autres_placements_divers';

    case COMPTES_COURANTS = 'comptes_courants';
    case COMPTES_SUR_LIVRET = 'comptes_sur_livret';
    case LIVRET_DE_DEVELOPPEMENT_DURABLE = 'livret_de_developpement_durable';
    case LIVRETS_A = 'livrets_a';
    case LIVRETS_BLEUS = 'livrets_bleus';
    case LIVRETS_D_EPARGNE_POPULAIRE = 'livrets_d_epargne_populaire';
    case LIVRETS_JEUNE = 'livrets_jeune';
    case CEL = 'cel';
    case COMPTES_A_TERME = 'comptes_a_terme';
    case COMPTE_COURANT_D_ASSOCIES = 'compte_courant_d_associes';
    case BONS_DE_CAISSE = 'bons_de_caisse';
    case AUTRES_DISPONIBILITES = 'autres_disponibilites';
    case PEL = 'pel';
    case PEP_BANCAIRE = 'pep_bancaire';
    case AUTRES_DEPOTS = 'autres_depots';

    case COMPTE_TITRES = 'compte_titres';
    case PEA = 'pea';
    case PEA_PME = 'pea_pme';
    case SICAV_FCP_ACTIONS = 'sicav_fcp_actions';
    case SICAV_FCP_OBLIGATIONS = 'sicav_fcp_obligations';
    case SICAV_FCP_MONETAIRE = 'sicav_fcp_monetaire';
    case ACTIONS_FRANCAISES_ET_EUROPEENNES = 'actions_francaises_et_europeennes';
    case ACTIONS_ETRANGERES = 'actions_etrangeres';
    case OBLIGATIONS = 'obligations';
    case PARTS_DE_SOFICA = 'parts_de_sofica';
    case PARTS_DE_FCPI = 'parts_de_fcpi';
    case PARTS_DE_FCPI_IFI = 'parts_de_fcpi_ifi';
    case PARTS_DE_FCPR = 'parts_de_fcpr';
    case PARTS_DE_FIP = 'parts_de_fip';
    case PARTS_DE_FIP_IFI = 'parts_de_fip_ifi';
    case PARTS_DE_HOLDING_IFI = 'parts_de_holding_ifi';
    case CAPITAL_DE_PMETPE_EXONERATION_ISF = 'capital_de_pmetpe_exoneration_isf';
    case DROITS_SOCIAUX_PACTE_DUTREIL = 'droits_sociaux_pacte_dutreil';
    case DROITS_SOCIAUX_SALARIES_MANDATAIRES = 'droits_sociaux_salaries_mandataires';
    case AUTRES_DROITS_SOCIAUX = 'autres_droits_sociaux';
    case AUTRES_VALEURS_MOBILIERES = 'autres_valeurs_mobilières';

    case CONTRAT_D_ASSURANCE_VIE_MULTISUPPORTS = 'contrat_d_assurance_vie_multisupport';
    case CONTRAT_D_ASSURANCE_VIE_EURO_CROISSANCE = 'contrat_d_assurance_vie_euro_croissance';
    case CONTRAT_D_ASSURANCE_VIE_EN_EUROS = 'contrat_d_assurance_vie_en_euros';
    case CONTRAT_VIE_GENERATION = 'contrat_vie_generation';
    case PEP_ASSURANCE_VIE_MULTISUPPORTS = 'pep_assurance_vie_multisupports';
    case BONS_CONTRATS_DE_CAPITALISATION_EN_UC = 'bons_contrats_de_capitalisation_en_uc';
    case BONS_CONTRATS_DE_CAPITALISATION_EN_EUROS = 'bons_contrats_de_capitalisation_en_euros';

    case PEE = 'pee';
    case PEI = 'pei';
    case PERCO = 'perco';
    case PERCOI = 'percoi';
    case PER_ASSURANCE = 'per_assurance';
    case PERO = 'pero';
    case PERECO = 'pereco';
    case PER = 'per';
    case CONTRAT_LOI_MADELIN = 'contrat_loi_madelin';
    case CONTRAT_LOI_MADELIN_AGRICOLE = 'contrat_loi_madelin_agricole';
    case PERE = 'pere';
    case PERI = 'peri';
    case PERP = 'perp';
    case CONTRAT_ARTICLE_82 = 'contrat_article_82';
    case CONTRAT_ARTICLE_83 = 'contrat_article_83';
    case CONTRAT_PREFON = 'contrat_prefon';
    case AUTRE_EPARGNE_RETRAITE = 'autre_epargne_retraite';
    case AUTRE_EPARGNE_SALARIALE = 'autre_epargne_salariale';

    case RENTAL_STRATEGY_COLOCATION = 'colocation';
    case RENTAL_STRATEGY_LOCAL_COMMERCIAL = 'local_commercial';
    case RENTAL_STRATEGY_LOCATION_A_L_ANNEE_MEUBLE = 'location_a_l_annee_meuble';
    case RENTAL_STRATEGY_LOCATION_A_L_ANNEE_NU = 'location_a_l_annee_nu';
    case RENTAL_STRATEGY_LOCATION_SAISONNIERE_INVESTISSEMENT_LOCATIF = 'location_saisonniere_investissement_locatif';
    case RENTAL_STRATEGY_LOCATION_SAISONNIERE_RESIDENCE_PRINCIPALE = 'location_saisonniere_residence_principale';
    case RENTAL_STRATEGY_LOCATION_SAISONNIERE_RESIDENCE_SECONDAIRE = 'location_saisonniere_residence_secondaire';
    case RENTAL_STRATEGY_MIXTE = 'mixte';
    case RENTAL_STRATEGY_RESIDENCE_PRINCIPALE_AUCUN_REVENU = 'residence_principale_aucun_revenu';
    case RENTAL_STRATEGY_AUCUNE_INTENTION_DE_LOUER_NI_D_HABITER = 'aucune_intention_de_louer_ni_d_habiter';
    case RENTAL_STRATEGY_AUTRE = 'autre';

    case PROPERTY_TYPE_MAISON = 'maison';
    case PROPERTY_TYPE_APPARTEMENT = 'appartement';
    case PROPERTY_TYPE_IMMEUBLE = 'immeuble';
    case PROPERTY_TYPE_LOCAL_PROFESSIONNEL = 'local_professionnel';
    case PROPERTY_TYPE_TERRAIN = 'terrain';
    case PROPERTY_TYPE_PARKING_BOX_CAVE = 'parking_box_cave';
    case PROPERTY_TYPE_AUTRE_FONCIER_BATI = 'autre_foncier_bati';
    case PROPERTY_TYPE_AUTRE_FONCIER_NON_BATI = 'autre_foncier_non_bati';

    case REVENUS_SALAIRES = 'salaires';
    case REVENUS_DIVIDENDES = 'dividendes';
    case REVENUS_SALAIRES_DIVIDENDES = 'salaires_dividendes';

    public static function getAll(): array
    {
        return [
            AssetsEnum::TYPE_BIENS_D_USAGE->value => __('Biens d\'usage'),
            AssetsEnum::TYPE_IMMOBILIER->value => __('Immobilier locatif'),
            AssetsEnum::TYPE_SCPI->value => __('Parts de SCPI'),
            AssetsEnum::TYPE_DISPONIBILITES->value => __('Disponibilités, épargne à moyen et long terme'),
            AssetsEnum::TYPE_ASSURANCE_VIE->value => __('Assurance vie'),
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value => __('Valeurs mobilières'),
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value => __('Épargne retraite et salariale'),
            AssetsEnum::TYPE_BIENS_PROFESSIONNELS->value => __('Biens Professionnels'),
            AssetsEnum::TYPE_PLACEMENTS_FONCIERS->value => __('Alternatifs'),
        ];
    }

    public static function getForNavigation(): array
    {
        return [
            AssetsEnum::TYPE_BIENS_D_USAGE->value => __('Biens d\'usage'),
            AssetsEnum::TYPE_IMMOBILIER->value => __('Immobilier locatif'),
            AssetsEnum::TYPE_SCPI->value => __('Parts de SCPI'),
            AssetsEnum::TYPE_DISPONIBILITES->value => __('Disponibilités, épargne à moyen et long terme'),
            AssetsEnum::TYPE_ASSURANCE_VIE->value => __('Assurance vie'),
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value => __('Valeurs mobilières'),
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value => __('Épargne retraite et salariale'),
        ];
    }

    public static function get(string $identifier): ?string
    {
        return self::getAll()[$identifier] ?? null;
    }

    public static function hasDate(string $type): bool
    {
        $withDate = [
            AssetsEnum::TYPE_DISPONIBILITES->value,
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value,
            AssetsEnum::TYPE_ASSURANCE_VIE->value,
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value
        ];
        return in_array($type, $withDate);
    }

    public static function getTexts(string $type): array
    {
        return [
            AssetsEnum::TYPE_BIENS_D_USAGE->value => [
                'title' => __('Biens d\'usage'),
                'empty' => __('Aucun bien d\'usage enregistré.'),
                'add' => __('Ajouter un bien d\'usage'),
                'create' => __('Création d\'un bien d\'usage'),
                'edit' => __('Modification d\'un bien d\'usage'),
            ],
            AssetsEnum::TYPE_IMMOBILIER->value => [
                'title' => __('Immobilier locatif'),
                'empty' => __('Aucun bien immobilier enregistré.'),
                'add' => __('Ajouter un bien immobilier'),
                'create' => __('Création d\'un bien immobilier'),
                'edit' => __('Modification d\'un bien immobilier'),
            ],
            AssetsEnum::TYPE_SCPI->value => [
                'title' => __('Parts de SCPI'),
                'empty' => __('Aucune part de SCPI enregistrée.'),
                'add' => __('Ajouter une part de SCPI'),
                'create' => __('Création d\'une part de SCPI'),
                'edit' => __('Modification d\'une part de SCPI'),
            ],
            AssetsEnum::TYPE_BIENS_PROFESSIONNELS->value => [
                'title' => __('Biens professionnels'),
                'empty' => __('Aucun bien professionnel enregistré.'),
                'add' => __('Ajouter un bien professionnel'),
                'create' => __('Création d\'un bien professionnel'),
                'edit' => __('Modification d\'un bien professionnel'),
            ],
            AssetsEnum::TYPE_PLACEMENTS_FONCIERS->value => [
                'title' => __('Placements fonciers et divers'),
                'empty' => __('Aucun placement foncier enregistré.'),
                'add' => __('Ajouter un placement foncier'),
                'create' => __('Création d\'un placement foncier'),
                'edit' => __('Modification d\'un placement foncier'),
            ],
            AssetsEnum::TYPE_DISPONIBILITES->value => [
                'title' => __('Disponibilités, épargne à moyen et long terme'),
                'empty' => __('Aucune disponibilité ou épargne enregistrée.'),
                'add' => __('Ajouter une disponibilité ou épargne'),
                'create' => __('Création d\'une disponibilité ou épargne'),
                'edit' => __('Modification d\'une disponibilité ou épargne'),
            ],
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value => [
                'title' => __('Valeurs mobilières'),
                'empty' => __('Aucune valeur mobilière enregistrée.'),
                'add' => __('Ajouter une valeur mobilière'),
                'create' => __('Création d\'une valeur mobilière'),
                'edit' => __('Modification d\'une valeur mobilière'),
            ],
            AssetsEnum::TYPE_ASSURANCE_VIE->value => [
                'title' => __('Assurance vie'),
                'empty' => __('Aucun contrat d\'assurance vie enregistré.'),
                'add' => __('Ajouter un contrat d\'assurance vie'),
                'create' => __('Création d\'un contrat d\'assurance vie'),
                'edit' => __('Modification d\'un contrat d\'assurance vie'),
            ],
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value => [
                'title' => __('Épargne retraite et salariale'),
                'empty' => __('Aucun produit d\'épargne retraite ou salariale enregistré.'),
                'add' => __('Ajouter un produit d\'épargne retraite ou salariale'),
                'create' => __('Création d\'un produit d\'épargne retraite ou salariale'),
                'edit' => __('Modification d\'un produit d\'épargne retraite ou salariale'),
            ],
        ][$type];
    }

    public static function getNextAssetType(string $assetType): ?string
    {
        $assetsTypes = self::getForNavigation();
        $currentAssetType = $assetsTypes[$assetType] ?? null;
        if (!$currentAssetType) {
            return null;
        }

        $keys = array_keys($assetsTypes);
        $currentAssetTypeIndex = array_search($assetType, $keys);
        if ($currentAssetTypeIndex === false) {
            return null;
        }
        $nextAssetTypeIndex = $currentAssetTypeIndex + 1;
        return $keys[$nextAssetTypeIndex] ?? null;
    }

    public static function getNatures(string $type): ?array
    {
        $natures = [
            AssetsEnum::TYPE_BIENS_D_USAGE->value => [
                AssetsEnum::RESIDENCE_PRINCIPALE->value => __('Résidence principale'),
                AssetsEnum::RESIDENCES_SECONDAIRES->value => __('Résidences secondaires'),
                AssetsEnum::TERRAINS->value => __('Terrains'),
                AssetsEnum::AUTRES_BIENS_D_USAGE->value => __('Autres biens d\'usage'),
                AssetsEnum::MEUBLES_MEUBLANTS->value => __('Meubles meublants'),
            ],
            AssetsEnum::TYPE_IMMOBILIER->value => [
                AssetsEnum::IMMOBILIER_LOCATIF->value => __('Immobilier locatif'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_PINEL->value => __('Immobilier locatif - Loi Pinel'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_PINEL_OUTRE_MER->value => __('Immobilier locatif - Loi Pinel (outre-mer)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DUFLOT->value => __('Immobilier locatif - Loi Duflot'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DUFLOT_OUTRE_MER->value => __('Immobilier locatif - Loi Duflot (outre-mer)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_SCELLIER->value => __('Immobilier locatif - Loi Scellier'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_SCELLIER_BBC->value => __('Immobilier locatif - Loi Scellier (BBC)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_SCELLIER_DOM_TOM->value => __('Immobilier locatif - Loi Scellier (DOM-TOM)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DEMESSINE->value => __('Immobilier locatif - Loi Demessine'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_LIBRE->value => __('Immobilier locatif - Loi Girardin (logement neuf - secteur libre)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_INTERMEDIAIRE->value => __('Immobilier locatif - Loi Girardin (logement neuf - secteur intermédiaire)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_CLASSIQUE_HORS_ZRR->value => __('Immobilier locatif - Loi de Robien (classique hors ZRR)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_CLASSIQUE_EN_ZRR->value => __('Immobilier locatif - Loi de Robien (classique en ZRR)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_RECENTRE_HORS_ZRR->value => __('Immobilier locatif - Loi de Robien (recentré hors ZRR)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_DE_ROBIEN_RECENTRE_EN_ZRR->value => __('Immobilier locatif - Loi de Robien (recentré en ZRR)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_BORLOO_NEUF->value => __('Immobilier locatif - Loi Borloo (neuf)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_BORLOO_ANCIEN_SOCIAL->value => __('Immobilier locatif - Loi Borloo (ancien social)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_BORLOO_ANCIEN_INTERMEDIAIRE->value => __('Immobilier locatif - Loi Borloo (ancien intermédiaire)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_BESSON_NEUF->value => __('Immobilier locatif - Loi Besson (neuf)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_BESSON_ANCIEN->value => __('Immobilier locatif - Loi Besson (ancien)'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_PERISSOL->value => __('Immobilier locatif - Loi Périssol'),
                AssetsEnum::IMMOBILIER_LOCATIF_LOI_MALRAUX->value => __('Immobilier locatif - Loi Malraux'),
                AssetsEnum::IMMOBILIER_LOCATIF_MONUMENTS_HISTORIQUES->value => __('Immobilier locatif - Monuments historiques'),
                AssetsEnum::IMMOBILIER_LMP->value => __('Immobilier - LMP'),
                AssetsEnum::IMMOBILIER_LMNP->value => __('Immobilier - LMNP'),
                AssetsEnum::IMMOBILIER_LMNP_LOI_BOUVARD->value => __('Immobilier - LMNP - Loi Bouvard'),
                AssetsEnum::PARTS_DE_SCI->value => __('Parts de SCI'),
            ],
            AssetsEnum::TYPE_SCPI->value => [
                AssetsEnum::PARTS_DE_SCPI->value => __('Parts de SCPI'),
                AssetsEnum::PARTS_DE_SCPI_LOI_PINEL->value => __('Parts de SCPI - Loi Pinel'),
                AssetsEnum::PARTS_DE_SCPI_LOI_PINEL_OUTRE_MER->value => __('Parts de SCPI - Loi Pinel (outre-mer)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DUFLOT->value => __('Parts de SCPI - Loi Duflot'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DUFLOT_OUTRE_MER->value => __('Parts de SCPI - Loi Duflot (outre-mer)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_SCELLIER->value => __('Parts de SCPI - Loi Scellier'),
                AssetsEnum::PARTS_DE_SCPI_LOI_SCELLIER_BBC->value => __('Parts de SCPI - Loi Scellier (BBC)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_SCELLIER_DOM_TOM->value => __('Parts de SCPI - Loi Scellier (DOM-TOM)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DEMESSINE->value => __('Parts de SCPI - Loi Demessine'),
                AssetsEnum::PARTS_DE_SCPI_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_LIBRE->value => __('Parts de SCPI - Loi Girardin (logement neuf - secteur libre)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_GIRARDIN_LOGEMENT_NEUF_SECTEUR_INTERMEDIAIRE->value => __('Parts de SCPI - Loi Girardin (logement neuf - secteur intermédiaire)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DE_ROBIEN_CLASSIQUE_HORS_ZRR->value => __('Parts de SCPI - Loi de Robien (classique hors ZRR)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DE_ROBIEN_CLASSIQUE_EN_ZRR->value => __('Parts de SCPI - Loi de Robien (classique en ZRR)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DE_ROBIEN_RECENTRE_HORS_ZRR->value => __('Parts de SCPI - Loi de Robien (recentré hors ZRR)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_DE_ROBIEN_RECENTRE_EN_ZRR->value => __('Parts de SCPI - Loi de Robien (recentré en ZRR)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_BORLOO_NEUF->value => __('Parts de SCPI - Loi Borloo (neuf)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_BORLOO_ANCIEN_SOCIAL->value => __('Parts de SCPI - Loi Borloo (ancien social)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_BORLOO_ANCIEN_INTERMEDIAIRE->value => __('Parts de SCPI - Loi Borloo (ancien intermédiaire)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_BESSON_NEUF->value => __('Parts de SCPI - Loi Besson (neuf)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_BESSON_ANCIEN->value => __('Parts de SCPI - Loi Besson (ancien)'),
                AssetsEnum::PARTS_DE_SCPI_LOI_PERISSOL->value => __('Parts de SCPI - Loi Périssol'),
                AssetsEnum::PARTS_DE_SCPI_LOI_MALRAUX->value => __('Parts de SCPI - Loi Malraux'),
                AssetsEnum::PARTS_DE_SCPI_MONUMENTS_HISTORIQUES->value => __('Parts de SCPI - Monuments historiques'),
            ],
            AssetsEnum::TYPE_BIENS_PROFESSIONNELS->value => [
                AssetsEnum::ENTREPRENEUR_INDIVIDUEL->value => __('Entrepreneur Individuel (EI)'),
                AssetsEnum::EIRL->value => __('Entreprise Individuelle à Responsabilité Limitée (EIRL)'),
                AssetsEnum::AUTO_ENTREPRENEUR->value => __('Auto-entrepreneur (Micro-entreprise)'),
                AssetsEnum::SARL->value => __('Société à Responsabilité Limitée (SARL)'),
                AssetsEnum::EURL->value => __('Entreprise Unipersonnelle à Responsabilité Limitée (EURL)'),
                AssetsEnum::SAS->value => __('Société par Actions Simplifiée (SAS)'),
                AssetsEnum::SASU->value => __('Société par Actions Simplifiée Unipersonnelle (SASU)'),
                AssetsEnum::SA->value => __('Société Anonyme (SA)'),
                AssetsEnum::SNC->value => __('Société en Nom Collectif (SNC)'),
                AssetsEnum::SCS->value => __('Société en Commandite Simple (SCS)'),
                AssetsEnum::SC->value => __('Société Civile (immobilière ou autre) IS / IR'),
                AssetsEnum::FONDS_DE_COMMERCE_CLIENTELES->value => __('Fonds de commerce, clientèles'),
                AssetsEnum::IMMEUBLES_PROFESSIONNELS->value => __('Immeubles professionnels'),
                AssetsEnum::AUTRES_BIENS_PROFESSIONNELS->value => __('Autres biens professionnels'),
            ],
            AssetsEnum::TYPE_PLACEMENTS_FONCIERS->value => [
                AssetsEnum::METAUX_PRECIEUX->value => __('Métaux précieux'),
                AssetsEnum::CRYPTOMONNAIES->value => __('Crypto-monnaies'),
                AssetsEnum::BOIS_ET_FORETS->value => __('Bois et forêts'),
                AssetsEnum::OBJETS_D_ART_ET_ANTIQUITES->value => __('Objets d\'art et antiquités'),
                AssetsEnum::OR_PHYSIQUE->value => __('Or physique (lingots, pièces, etc.)'),
                AssetsEnum::PARTS_DE_GROUPES_FORESTIERS->value => __('Parts de groupements forestiers'),
                AssetsEnum::PARTS_DE_STES_D_EPARGNE_FORESTIERE->value => __('Parts de Stés d\'épargne forestière'),
                AssetsEnum::TERRAINS_AGRICOLES->value => __('Terrains agricoles (loués)'),
                AssetsEnum::PARTS_DE_GFA_GAF_GFV_ET_GFR->value => __('Parts de GFA, GAF, GFV et GFR'),
                AssetsEnum::BIENS_RURAUX_LOUES_A_LONG_TERME->value => __('Biens ruraux loués à long terme'),
                AssetsEnum::AUTRES_PLACEMENTS_DIVERS->value => __('Autres placements divers'),
            ],
            AssetsEnum::TYPE_DISPONIBILITES->value => [
                AssetsEnum::COMPTES_COURANTS->value => __('Comptes courants'),
                AssetsEnum::COMPTES_SUR_LIVRET->value => __('Comptes sur livret (CSL)'),
                AssetsEnum::LIVRET_DE_DEVELOPPEMENT_DURABLE->value => __('Livret de développement durable'),
                AssetsEnum::LIVRETS_A->value => __('Livrets A'),
                AssetsEnum::LIVRETS_BLEUS->value => __('Livrets Bleus'),
                AssetsEnum::LIVRETS_D_EPARGNE_POPULAIRE->value => __('Livrets d\'épargne populaire (LEP)'),
                AssetsEnum::LIVRETS_JEUNE->value => __('Livrets Jeune'),
                AssetsEnum::CEL->value => __('CEL'),
                AssetsEnum::COMPTES_A_TERME->value => __('Comptes à terme'),
                AssetsEnum::COMPTE_COURANT_D_ASSOCIES->value => __('Compte courant d\'associés'),
                AssetsEnum::BONS_DE_CAISSE->value => __('Bons de caisse'),
                AssetsEnum::AUTRES_DISPONIBILITES->value => __('Autres disponibilités'),
                AssetsEnum::PEL->value => __('PEL'),
                AssetsEnum::PEP_BANCAIRE->value => __('PEP bancaire'),
                AssetsEnum::AUTRES_DEPOTS->value => __('Autres dépôts'),
            ],
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value => [
                AssetsEnum::COMPTE_TITRES->value => __('Compte titres'),
                AssetsEnum::PEA->value => __('PEA'),
                AssetsEnum::PEA_PME->value => __('PEA-PME'),
                AssetsEnum::SICAV_FCP_ACTIONS->value => __('SICAV/FCP Actions'),
                AssetsEnum::SICAV_FCP_OBLIGATIONS->value => __('SICAV/FCP Obligations'),
                AssetsEnum::SICAV_FCP_MONETAIRE->value => __('SICAV/FCP Monétaire'),
                AssetsEnum::ACTIONS_FRANCAISES_ET_EUROPEENNES->value => __('Actions françaises et européennes'),
                AssetsEnum::ACTIONS_ETRANGERES->value => __('Actions étrangères'),
                AssetsEnum::OBLIGATIONS->value => __('Obligations'),
                AssetsEnum::PARTS_DE_SOFICA->value => __('Parts de SOFICA'),
                AssetsEnum::PARTS_DE_FCPI->value => __('Parts de FCPI'),
                AssetsEnum::PARTS_DE_FCPI_IFI->value => __('Parts de FCPI (IFI)'),
                AssetsEnum::PARTS_DE_FCPR->value => __('Parts de FCPR'),
                AssetsEnum::PARTS_DE_FIP->value => __('Parts de FIP'),
                AssetsEnum::PARTS_DE_FIP_IFI->value => __('Parts de FIP (IFI)'),
                AssetsEnum::PARTS_DE_HOLDING_IFI->value => __('Parts de holding - IFI'),
                AssetsEnum::CAPITAL_DE_PMETPE_EXONERATION_ISF->value => __('Capital de PME/TPE (exonération ISF)'),
                AssetsEnum::DROITS_SOCIAUX_PACTE_DUTREIL->value => __('Droits sociaux (pacte Dutreil)'),
                AssetsEnum::DROITS_SOCIAUX_SALARIES_MANDATAIRES->value => __('Droits sociaux (salariés, mandataires)'),
                AssetsEnum::AUTRES_DROITS_SOCIAUX->value => __('Autres droits sociaux'),
                AssetsEnum::AUTRES_VALEURS_MOBILIERES->value => __('Autres valeurs mobilières'),
            ],
            AssetsEnum::TYPE_ASSURANCE_VIE->value => [
                AssetsEnum::CONTRAT_D_ASSURANCE_VIE_MULTISUPPORTS->value => __('Contrat d\'assurance vie multisupports'),
                AssetsEnum::CONTRAT_D_ASSURANCE_VIE_EURO_CROISSANCE->value => __('Contrat d\'assurance vie euro-croissance'),
                AssetsEnum::CONTRAT_D_ASSURANCE_VIE_EN_EUROS->value => __('Contrat d\'assurance vie en euros'),
                AssetsEnum::CONTRAT_VIE_GENERATION->value => __('Contrat vie-génération'),
                AssetsEnum::PEP_ASSURANCE_VIE_MULTISUPPORTS->value => __('PEP assurance vie multisupports'),
                AssetsEnum::BONS_CONTRATS_DE_CAPITALISATION_EN_UC->value => __('Bons, contrats de capitalisation en UC'),
                AssetsEnum::BONS_CONTRATS_DE_CAPITALISATION_EN_EUROS->value => __('Bons, contrats de capitalisation en euros'),
            ],
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value => [
                AssetsEnum::PEE->value => __('PEE (Plan d\'Epargne Entreprise)'),
                AssetsEnum::PEI->value => __('PEI (Plan d\'Epargne Interentreprise)'),
                AssetsEnum::PERCO->value => __('PERCO'),
                AssetsEnum::PERCOI->value => __('PERCOI'),
                AssetsEnum::PER_ASSURANCE->value => __('PER Assurance'),
                AssetsEnum::PERO->value => __('PERO'),
                AssetsEnum::PERECO->value => __('PERECO'),
                AssetsEnum::PER->value => __('PER'),
                AssetsEnum::CONTRAT_LOI_MADELIN->value => __('Contrat Loi Madelin'),
                AssetsEnum::CONTRAT_LOI_MADELIN_AGRICOLE->value => __('Contrat Loi Madelin Agricole'),
                AssetsEnum::PERE->value => __('PÈRE'),
                AssetsEnum::PERI->value => __('PERI'),
                AssetsEnum::PERP->value => __('PERP'),
                AssetsEnum::CONTRAT_ARTICLE_82->value => __('Contrat Article 82'),
                AssetsEnum::CONTRAT_ARTICLE_83->value => __('Contrat Article 83'),
                AssetsEnum::CONTRAT_PREFON->value => __('Contrat Préfon'),
                AssetsEnum::AUTRE_EPARGNE_RETRAITE->value => __('Autre épargne retraite'),
                AssetsEnum::AUTRE_EPARGNE_SALARIALE->value => __('Autre épargne salariale'),
            ],
        ];

        return $natures[$type] ?? null;
    }

    public static function getNature(string $type, string $nature): ?string
    {
        return self::getNatures($type)[$nature] ?? null;
    }

    public static function getRentalStrategies(): array
    {
        return [
            AssetsEnum::RENTAL_STRATEGY_COLOCATION->value => __('Colocation'),
            AssetsEnum::RENTAL_STRATEGY_LOCAL_COMMERCIAL->value => __('Local commercial'),
            AssetsEnum::RENTAL_STRATEGY_LOCATION_A_L_ANNEE_MEUBLE->value => __('Location à l\'année - Meublé'),
            AssetsEnum::RENTAL_STRATEGY_LOCATION_A_L_ANNEE_NU->value => __('Location à l\'année - Nu'),
            AssetsEnum::RENTAL_STRATEGY_LOCATION_SAISONNIERE_INVESTISSEMENT_LOCATIF->value => __('Location saisonnière - Investissement locatif'),
            AssetsEnum::RENTAL_STRATEGY_LOCATION_SAISONNIERE_RESIDENCE_PRINCIPALE->value => __('Location saisonnière - Résidence principale'),
            AssetsEnum::RENTAL_STRATEGY_LOCATION_SAISONNIERE_RESIDENCE_SECONDAIRE->value => __('Location saisonnière - Résidence secondaire'),
            AssetsEnum::RENTAL_STRATEGY_MIXTE->value => __('Mixte'),
            AssetsEnum::RENTAL_STRATEGY_RESIDENCE_PRINCIPALE_AUCUN_REVENU->value => __('Résidence principale - Aucun revenu'),
            AssetsEnum::RENTAL_STRATEGY_AUCUNE_INTENTION_DE_LOUER_NI_D_HABITER->value => __('Aucune intention de louer ni d\'habiter'),
            AssetsEnum::RENTAL_STRATEGY_AUTRE->value => __('Autre'),
        ];
    }

    public static function getRentalStrategy(string $rentalStrategy): ?string
    {
        return self::getRentalStrategies()[$rentalStrategy] ?? null;
    }

    public static function getPropertiesTypes(): array
    {
        return [
            AssetsEnum::PROPERTY_TYPE_MAISON->value => __('Maison'),
            AssetsEnum::PROPERTY_TYPE_APPARTEMENT->value => __('Appartement'),
            AssetsEnum::PROPERTY_TYPE_IMMEUBLE->value => __('Immeuble'),
            AssetsEnum::PROPERTY_TYPE_LOCAL_PROFESSIONNEL->value => __('Local professionnel'),
            AssetsEnum::PROPERTY_TYPE_TERRAIN->value => __('Terrain'),
            AssetsEnum::PROPERTY_TYPE_PARKING_BOX_CAVE->value => __('Parking / box / cave'),
            AssetsEnum::PROPERTY_TYPE_AUTRE_FONCIER_BATI->value => __('Autre foncier bâti'),
            AssetsEnum::PROPERTY_TYPE_AUTRE_FONCIER_NON_BATI->value => __('Autre foncier non bâti'),
        ];
    }

    public static function getPropertyType(string $propertyType): ?string
    {
        return self::getPropertiesTypes()[$propertyType] ?? null;
    }

    public static function getRevenusTypes(): array
    {
        return [
            AssetsEnum::REVENUS_SALAIRES->value => __('Salaires'),
            AssetsEnum::REVENUS_DIVIDENDES->value => __('Dividendes'),
            AssetsEnum::REVENUS_SALAIRES_DIVIDENDES->value => __('Salaires et dividendes'),
        ];
    }

    public static function getRevenuType(string $revenuType): ?string
    {
        return self::getRevenusTypes()[$revenuType] ?? null;
    }

    public static function getTypesFinanciers(): array
    {
        return [
            AssetsEnum::TYPE_DISPONIBILITES->value,
            AssetsEnum::TYPE_ASSURANCE_VIE->value,
            AssetsEnum::TYPE_EPARGNE_RETRAITE_ET_SALARIALE->value,
            AssetsEnum::TYPE_VALEURS_MOBILIERES->value
        ];
    }

    public static function getTypesImmobiliers(): array
    {
        return [
            self::TYPE_BIENS_D_USAGE->value,
            self::TYPE_IMMOBILIER->value,
            self::TYPE_SCPI->value,
        ];
    }

    public static function getTypesProfessionnels(): array
    {
        return [
            self::TYPE_BIENS_PROFESSIONNELS->value,
        ];
    }

    public static function getTypesOthers(): array
    {
        return [
            self::TYPE_PLACEMENTS_FONCIERS->value
        ];
    }
}
