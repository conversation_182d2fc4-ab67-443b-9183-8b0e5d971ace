<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Doctrine\Set\DoctrineSetList;

return static function (RectorConfig $rectorConfig): void {
    // Paths for <PERSON> to act upon
    $rectorConfig->paths([
        __DIR__ . '/src/Entity',
        __DIR__ . '/src/Repository',
    ]);

    $rectorConfig->sets([
        DoctrineSetList::ANNOTATIONS_TO_ATTRIBUTES,
    ]);
};
