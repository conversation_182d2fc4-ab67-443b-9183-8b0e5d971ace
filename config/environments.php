<?php
/*
 *  PATHS
 */
require_once 'paths.php';

define("MASTER_UNIQID", 'master');
define('ADMIN_URL', 'adminAS1810');
define('SUBDOMAIN_ENABLED', true);

/**
 * Environment vars
 */
define("ENV_DEV", 'dev');
define("ENV_TEST", 'test');
define("ENV_PROD", 'prod');
define("ENV_PRE_PROD", 'preprod');

/**
 * Application
 */
define("APPLICATION_ADMIN", 1);
define("APPLICATION_API", 2);
define("APPLICATION_APP", 3);
define("APPLICATION_SITE", 4);

/**
 * Universes
 */
define("UNIVERSE_ADMIN_DEFAULT", 1);
define("UNIVERSE_ADMIN_STATS", 2);
define("UNIVERSE_ADMIN_SAV", 3);
define("UNIVERSE_ADMIN_NEWS", 4);
define("UNIVERSE_ADMIN_PRODUCTS", 5);
define("UNIVERSE_ADMIN_AFFILIATION", 6);
define("UNIVERSE_ADMIN_DEV", 7);
define("UNIVERSE_ADMIN_PRIVATE", 8);
define("UNIVERSE_ADMIN_SHOP", 9);
define("UNIVERSE_API_DEFAULT", 10);
define("UNIVERSE_SITE_DEFAULT", 11);

define("UNIVERSE_APP_PRIVATE", 20);
define("UNIVERSE_APP_PUBLIC", 21);
define("UNIVERSE_APP_AFFILIATION", 22);
define("UNIVERSE_APP_USERS", 23);
define("UNIVERSE_APP_NEWS", 24);
define("UNIVERSE_APP_RELATIONCLIENT", 25);
define("UNIVERSE_APP_DOSSIER", 26);
define("UNIVERSE_APP_SHOP", 27);
define("UNIVERSE_APP_CALENDAR", 28);

/**
 * Default roles
 */
define("ROLE_USER", 1);
define("ROLE_EDITOR", 2);
define("ROLE_SUPEREDITOR", 3);
define("ROLE_ADMIN", 4);
define("ROLE_SUPERADMIN", 5);
define("ROLE_AFFILIATE", 6);

/**
 * Default attributes
 */
define('ATTRIBUTE_CREATE', 'create');
define('ATTRIBUTE_VIEW', 'view');
define('ATTRIBUTE_EDIT', 'edit');
define('ATTRIBUTE_DELETE', 'delete');
define('ATTRIBUTE_EXPORT', 'export');

/**
 * FileSystem vars
 */
define('FILE_SYSTEM_FILES', 'files');
define('FILE_SYSTEM_S3', 's3');

/**
 * Queue handlers
 */
define('QUEUE_HANDLERS_DATABASE', 'database');
define('QUEUE_HANDLERS_SQS', 'sqs');

/**
 * API conf
 */
define("API_LIMIT", 20000);

/**
 * Support conf
 */
define("SUPPORT_NB_PER_PAGE", 20);

/**
 * Protected permalinks
 */
define('PROTECTED_PERMALINKS', ['master', 'www', 'paypal', 'spam', 'spamtrap', 'upload', 'uploads', 'thumbnails', 'kibana', 'status', 'api']);

/**
 * Geometry
 */
define('SRID', 4326);