<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class <PERSON><PERSON> extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table(DB_PREFIX . 'mollie_charges');
        $table->addColumn('client_id', 'integer')
            ->addColumn('cart_id', 'integer')
            ->addColumn('transaction_reference', 'string', ['limit' => 100])
            ->addColumn('customer_id', 'string', ['limit' => 100])
            ->addColumn('account_id', 'integer', ['null' => true])
            ->addColumn('payment_method', 'string', ['limit' => 100])
            ->addColumn('secret_key', 'string', ['null' => true])
            ->addColumn('id_mollie_charge', 'string', ['limit' => 100])
            ->addColumn('abonnement_id', 'integer')
            ->addColumn('product', 'string')
            ->addColumn('amount', 'float')
            ->addColumn('currency', 'string', ['limit' => 10])
            ->addColumn('last_name', 'string')
            ->addColumn('first_name', 'string')
            ->addColumn('email', 'string')
            ->addColumn('custom', 'text')
            ->addColumn('checkout', 'text')
            ->addColumn('ip', 'string', ['limit' => 50])
            ->addColumn('status', 'string', ['limit' => 20])
            ->addColumn('result', 'text')
            ->addColumn('last4', 'string', ['limit' => 10, 'null' => true])
            ->addColumn('error', 'text')
            ->addColumn('date', 'datetime')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('account_id', DB_PREFIX . 'integrations_accounts', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->addIndex('transaction_reference')
            ->addIndex('abonnement_id')
            ->addIndex('id_mollie_charge')
            ->addIndex('customer_id')
            ->create();

        $table = $this->table(DB_PREFIX . 'mollie_abonnements');
        $table->addColumn('client_id', 'integer')
            ->addColumn('transaction_reference', 'string', ['limit' => 100])
            ->addColumn('customer_id', 'string', ['limit' => 100])
            ->addColumn('account_id', 'integer', ['null' => true])
            ->addColumn('payment_method', 'string', ['limit' => 100])
            ->addColumn('secret_key', 'string', ['null' => true])
            ->addColumn('product', 'string')
            ->addColumn('amount', 'float')
            ->addColumn('currency', 'string', ['limit' => 10])
            ->addColumn('last_name', 'string')
            ->addColumn('first_name', 'string')
            ->addColumn('email', 'string')
            ->addColumn('custom', 'text')
            ->addColumn('checkout', 'text')
            ->addColumn('ip', 'string', ['limit' => 50])
            ->addColumn('nb_payments', 'string', ['limit' => 3])
            ->addColumn('nb_payments_left', 'string', ['limit' => 3])
            ->addColumn('decalage', 'string', ['limit' => 10])
            ->addColumn('valid', 'boolean')
            ->addColumn('reattempt', 'integer')
            ->addColumn('result', 'text')
            ->addColumn('error', 'text')
            ->addColumn('date', 'date')
            ->addColumn('hour', 'string', ['limit' => 2])
            ->addColumn('deleted_at', 'datetime', ['null' => true])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('account_id', DB_PREFIX . 'integrations_accounts', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->addIndex('transaction_reference')
            ->create();

        $table = $this->table(DB_PREFIX . 'mollie_customers');
        $table->addColumn('client_id', 'integer')
            ->addColumn('account_id', 'integer', ['null' => true])
            ->addColumn('customer_id', 'string', ['limit' => 100])
            ->addColumn('payment_method', 'string', ['limit' => 100])
            ->addColumn('last_name', 'string')
            ->addColumn('first_name', 'string')
            ->addColumn('email', 'string')
            ->addColumn('ip', 'string', ['limit' => 50])
            ->addColumn('last4', 'string', ['limit' => 10, 'null' => true])
            ->addColumn('date', 'datetime')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('account_id', DB_PREFIX . 'integrations_accounts', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addIndex('customer_id')
            ->addIndex('email')
            ->create();
    }
}
