<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierDonation extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_donations');
        $table->addColumn('client_id', 'integer')
            ->addColumn('dossier_id', 'integer')
            ->addColumn('person_id', 'integer', ['null' => true])
            ->addColumn('recipient', 'string')
            ->addColumn('amount', 'float')
            ->addColumn('date', 'date')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'NO_ACTION'])
            ->addForeignKey('dossier_id', 'px_dossiers', 'id', ['delete' => 'CASCADE', 'update' => 'NO_ACTION'])
            ->addForeignKey('person_id', 'px_dossiers_persons', 'id', ['delete' => 'SET NULL', 'update' => 'NO_ACTION'])
            ->create();
    }
}
