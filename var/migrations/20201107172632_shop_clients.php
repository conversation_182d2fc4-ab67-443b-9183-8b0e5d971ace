<?php


use Phinx\Migration\AbstractMigration;

class ShopClients extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('shop_autoresponders');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 1])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('shop_autoresponders_post');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 1])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table = $this->table('shop_vat_rules');
        $table->addColumn('client_id', 'integer', ['after' => 'id', 'default' => 1])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();
    }
}
