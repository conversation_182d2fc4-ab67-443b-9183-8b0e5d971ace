<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class Events extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table(DB_PREFIX . 'events', ['signed' => true]);
        $table->addColumn('name', 'string')
            ->addColumn('image', 'text')
            ->addColumn('bg_image', 'text')
            ->addColumn('small_image', 'text')
            ->addColumn('content', 'text', ['limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG])
            ->addColumn('active', 'boolean')
            ->addColumn('date', 'datetime')
            ->addColumn('date_start', 'datetime', ['null' => true])
            ->addColumn('date_end', 'datetime', ['null' => true])
            ->create();
    }
}
