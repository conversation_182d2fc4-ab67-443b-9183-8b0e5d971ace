<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class AdminUniverseShop extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $row = [
            'universe_id' => UNIVERSE_ADMIN_SHOP,
            'name' => 'Shop',
            'slug' => 'admin_shop',
            'application_id' => APPLICATION_ADMIN,
        ];
        $this->table('mg_universes')->insert($row)->save();

        $rolesSuperAdmin = $this->fetchAll("SELECT * FROM mg_users_roles WHERE role_id = 5");
        $rows = [];
        foreach ($rolesSuperAdmin as $roleSuperAdmin) {
            $rows[] = [
                'user_role_id' => $roleSuperAdmin['user_role_id'],
                'universe_id' => UNIVERSE_ADMIN_SHOP,
                'attributes' => 'view,create,edit,export,delete',
                'objects' => '',
            ];
        }
        $this->table('mg_users_roles_universes')->insert($rows)->save();
    }

    public function down(): void
    {
        $this->execute("DELETE FROM mg_universes WHERE universe_id = " . UNIVERSE_ADMIN_SHOP);
    }
}
