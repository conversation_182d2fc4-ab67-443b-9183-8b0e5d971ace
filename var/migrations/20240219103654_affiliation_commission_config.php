<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class AffiliationCommissionConfig extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('aff_commissions_config');
        $table->addColumn('product_id', 'integer', ['null' => false, 'after' => 'partner_id'])
            ->addColumn('commission_rate', 'float', ['after' => 'commission_amount'])
            ->addForeignKey('product_id', 'shop_products', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $table->removeColumn('commission_amount')
            ->removeColumn('commission_type')
            ->removeColumn('type')
            ->removeColumn('amount_type')
            ->update();
    }

    public function down(): void
    {
        $table = $this->table('aff_commissions_config');
        $table->dropForeignKey('product_id')
            ->update();
        $table->removeColumn('product_id')
            ->removeColumn('commission_rate')
            ->update();

        $table->addColumn('commission_type', 'string', ['after' => 'partner_id', 'limit' => 20])
            ->addColumn('commission_amount', 'float', ['after' => 'commission_type'])
            ->addColumn('type', 'string', ['after' => 'commission_amount', 'limit' => 10])
            ->addColumn('amount_type', 'string', ['after' => 'type', 'limit' => 20])
            ->update();
    }
}
