<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CronLogs extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table(DB_PREFIX . 'cron_logs');
        $table->addColumn('action', 'string', ['limit' => 50])
            ->addColumn('param', 'string', ['limit' => 50, 'null' => true])
            ->addColumn('status', 'string', ['limit' => 50])
            ->addColumn('error', 'boolean')
            ->addColumn('log', 'text', ['limit' => \Phinx\Db\Adapter\MysqlAdapter::TEXT_LONG])
            ->addColumn('date', 'datetime')
            ->addColumn('date_end', 'datetime', ['null' => true])
            ->create();
    }
}
