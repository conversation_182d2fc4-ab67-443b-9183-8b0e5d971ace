<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class RequestUser<PERSON><PERSON>wer extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE rq_users_answers (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, request_user_id INT NOT NULL, step_id INT NOT NULL, question_id INT NOT NULL, content LONGTEXT NOT NULL, status VARCHAR(50) NOT NULL, comment LONGTEXT NOT NULL, date DATETIME NOT NULL, INDEX IDX_D5E1574E19EB6921 (client_id), INDEX IDX_D5E1574E8D4AA1C2 (request_user_id), INDEX IDX_D5E1574E73B21E9C (step_id), INDEX IDX_D5E1574E1E27F6BF (question_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE rq_users_answers ADD CONSTRAINT FK_D5E1574E19EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_answers ADD CONSTRAINT FK_D5E1574E8D4AA1C2 FOREIGN KEY (request_user_id) REFERENCES rq_users (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_answers ADD CONSTRAINT FK_D5E1574E73B21E9C FOREIGN KEY (step_id) REFERENCES rq_steps (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_answers ADD CONSTRAINT FK_D5E1574E1E27F6BF FOREIGN KEY (question_id) REFERENCES rq_questions (id) ON DELETE CASCADE');

    }

    public function down(): void
    {
        $this->execute('ALTER TABLE rq_users_answers DROP FOREIGN KEY FK_D5E1574E19EB6921');
        $this->execute('ALTER TABLE rq_users_answers DROP FOREIGN KEY FK_D5E1574E8D4AA1C2');
        $this->execute('ALTER TABLE rq_users_answers DROP FOREIGN KEY FK_D5E1574EC4663E4');
        $this->execute('ALTER TABLE rq_users_answers DROP FOREIGN KEY FK_D5E1574E73B21E9C');
        $this->execute('ALTER TABLE rq_users_answers DROP FOREIGN KEY FK_D5E1574E1E27F6BF');
        $this->execute('DROP TABLE rq_users_answers');

    }
}
