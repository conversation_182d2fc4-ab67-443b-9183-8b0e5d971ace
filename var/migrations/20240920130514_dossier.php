<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class <PERSON><PERSON><PERSON> extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers', ['signed' => true]);
        $table->addColumn('client_id', 'integer')
            ->addColumn('user_id', 'integer', ['null' => true])
            ->addColumn('reference', 'string', ['limit' => 50])
            ->addColumn('price', 'float')
            ->addColumn('observations', 'text')
            ->addColumn('accepted', 'boolean', ['default' => false])
            ->addColumn('status', 'string', ['limit' => 50])
            ->addColumn('status_message', 'string')
            ->addColumn('alert', 'boolean', ['default' => false])
            ->addColumn('compressed', 'boolean', ['default' => false])
            ->addColumn('date', 'datetime')
            ->addColumn('date_mission', 'date', ['null' => true])
            ->addColumn('date_closed', 'datetime', ['null' => true])
            ->addColumn('date_alert', 'date', ['null' => true])
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('user_id', DB_PREFIX . 'users', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->create();
    }
}
