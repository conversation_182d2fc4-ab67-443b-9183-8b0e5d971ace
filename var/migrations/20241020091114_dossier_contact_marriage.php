<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierContactMarriage extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_contacts');
        $table->addColumn('marriage_date', 'date', ['null' => true, 'after' => 'marital_status'])
            ->addColumn('matrimonial_regime', 'string', ['limit' => 50, 'after' => 'marriage_date'])
            ->addColumn('last_living_to_you', 'boolean', ['default' => false, 'after' => 'matrimonial_regime'])
            ->addColumn('last_living_to_partner', 'boolean', ['default' => false, 'after' => 'last_living_to_you'])
            ->update();
    }
}
