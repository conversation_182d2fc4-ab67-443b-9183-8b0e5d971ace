<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class FixTransactions extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->table('shop_transactions')
            ->changeColumn('customer_id', 'integer', ['null' => true])
            ->changeColumn('cart_id', 'integer', ['null' => true])
            ->addForeignKey('customer_id', 'shop_customers', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->addForeignKey('cart_id', 'shop_cart', 'id', ['delete' => 'SET NULL', 'update' => 'CASCADE'])
            ->update();
    }

    public function down(): void
    {
        $this->table('shop_transactions')
            ->dropForeignKey('customer_id')
            ->dropForeign<PERSON>ey('cart_id')
            ->update();

        $this->table('shop_transactions')
            ->changeColumn('customer_id', 'integer')
            ->changeColumn('cart_id', 'integer')
            ->update();
    }
}
