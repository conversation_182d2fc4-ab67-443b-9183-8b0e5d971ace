<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class GameBadgeClient extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE game_badges_clients (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, badge_id INT NOT NULL, claimed TINYINT(1) NOT NULL, date DATETIME NOT NULL, date_claimed DATETIME DEFAULT NULL, INDEX IDX_85E995C19EB6921 (client_id), INDEX IDX_85E995CF7A2C2FC (badge_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE game_badges_clients ADD CONSTRAINT FK_85E995C19EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE ON UPDATE CASCADE');
        $this->execute('ALTER TABLE game_badges_clients ADD CONSTRAINT FK_85E995CF7A2C2FC FOREIGN KEY (badge_id) REFERENCES game_badges (id) ON DELETE CASCADE ON UPDATE CASCADE');
    }

    public function down(): void
    {
        $this->execute('DROP TABLE game_badges_clients');
    }
}