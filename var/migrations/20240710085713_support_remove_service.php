<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class SupportRemoveService extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $tablesToRemove = ['hdk_departments', 'hdk_filters', 'hdk_tickets', 'hdk_saved_replies', 'hdk_users'];
        foreach ($tablesToRemove as $tableToRemove) {
            $table = $this->table($tableToRemove);
            $table->dropForeignKey('service_id')
                ->update();
            $table->removeColumn('service_id')
                ->update();
        }

        $table = $this->table('hdk_saved_replies');
        $table->addColumn('department_id', 'integer', ['null' => true, 'after' => 'id'])
            ->addForeignKey('department_id', 'hdk_departments', 'id', ['delete' => 'SET_NULL', 'update' => 'NO_ACTION'])
            ->update();

        $stmt = $this->query("SELECT * FROM hdk_departments LIMIT 1");
        $department = $stmt->fetch();
        if ($department) {
            $this->execute("UPDATE hdk_saved_replies SET department_id = " . $department['id'] . " WHERE 1;");
        }

        $table = $this->table('hdk_services');
        $table->drop()->save();
    }

    public function down(): void
    {
        $table = $this->table('hdk_services', ['signed' => true]);
        $table->addColumn('name', 'string')
            ->create();

        //add one service
        $rows = [
            [
                'id' => 1,
                'name' => 'Général'
            ]
        ];
        $this->table('hdk_services')->insert($rows)->save();


        $tablesToAdd = ['hdk_departments', 'hdk_filters', 'hdk_tickets', 'hdk_saved_replies', 'hdk_users'];
        foreach ($tablesToAdd as $tableToAdd) {
            $table = $this->table($tableToAdd);
            $table->addColumn('service_id', 'integer', ['null' => true])
                ->addForeignKey('service_id', 'hdk_services', 'id', ['delete' => 'SET_NULL', 'update' => 'NO_ACTION'])
                ->update();
            $this->execute('UPDATE ' . $tableToAdd . ' SET service_id = 1 WHERE 1;');
        }

        $table = $this->table('hdk_saved_replies');
        $table->dropForeignKey('department_id')
            ->update();
        $table->removeColumn('department_id')
            ->update();
    }
}
