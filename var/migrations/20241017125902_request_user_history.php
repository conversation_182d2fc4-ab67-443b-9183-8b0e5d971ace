<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class RequestUserHistory extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE rq_users_histories (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, request_user_id INT NOT NULL, page_id INT NOT NULL, step_id INT NOT NULL, question_id INT NOT NULL, user_id INT DEFAULT NULL, action VARCHAR(50) NOT NULL, param VARCHAR(255) NOT NULL, date DATETIME NOT NULL, INDEX IDX_70592D4D19EB6921 (client_id), INDEX IDX_70592D4D8D4AA1C2 (request_user_id), INDEX IDX_70592D4DC4663E4 (page_id), INDEX IDX_70592D4D73B21E9C (step_id), INDEX IDX_70592D4D1E27F6BF (question_id), INDEX IDX_70592D4DA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4D19EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4D8D4AA1C2 FOREIGN KEY (request_user_id) REFERENCES rq_users (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4DC4663E4 FOREIGN KEY (page_id) REFERENCES rq_pages (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4D73B21E9C FOREIGN KEY (step_id) REFERENCES rq_steps (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4D1E27F6BF FOREIGN KEY (question_id) REFERENCES rq_questions (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_histories ADD CONSTRAINT FK_70592D4DA76ED395 FOREIGN KEY (user_id) REFERENCES mg_users (id) ON DELETE CASCADE');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4D19EB6921');
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4D8D4AA1C2');
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4DC4663E4');
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4D73B21E9C');
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4D1E27F6BF');
        $this->execute('ALTER TABLE rq_users_histories DROP FOREIGN KEY FK_70592D4DA76ED395');
        $this->execute('DROP TABLE rq_users_histories');
    }
}