<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class <PERSON>ssierN<PERSON> extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_notes', ['signed' => true]);
        $table->addColumn('client_id', 'integer')
            ->addColumn('dossier_id', 'integer')
            ->addColumn('user_id', 'integer')
            ->addColumn('content', 'text')
            ->addColumn('label', 'string', ['limit' => 20, 'null' => true])
            ->addColumn('position', 'integer')
            ->addColumn('date', 'datetime')
            ->addForeignKey('client_id', DB_PREFIX . 'clients', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('dossier_id', 'px_dossiers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('user_id', DB_PREFIX . 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
