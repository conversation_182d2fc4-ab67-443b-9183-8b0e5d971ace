<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class RequestUserPage extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $this->execute('CREATE TABLE rq_users_pages (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, request_user_id INT NOT NULL, page_id INT NOT NULL, percentage INT NOT NULL, status VARCHAR(50) NOT NULL, INDEX IDX_5FB39E8319EB6921 (client_id), INDEX IDX_5FB39E838D4AA1C2 (request_user_id), INDEX IDX_5FB39E83C4663E4 (page_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE rq_users_pages ADD CONSTRAINT FK_5FB39E8319EB6921 FOREIGN KEY (client_id) REFERENCES mg_clients (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_pages ADD CONSTRAINT FK_5FB39E838D4AA1C2 FOREIGN KEY (request_user_id) REFERENCES rq_users (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE rq_users_pages ADD CONSTRAINT FK_5FB39E83C4663E4 FOREIGN KEY (page_id) REFERENCES rq_pages (id) ON DELETE CASCADE');
    }

    public function down(): void
    {
        $this->execute('ALTER TABLE rq_users_pages DROP FOREIGN KEY FK_5FB39E8319EB6921');
        $this->execute('ALTER TABLE rq_users_pages DROP FOREIGN KEY FK_5FB39E838D4AA1C2');
        $this->execute('ALTER TABLE rq_users_pages DROP FOREIGN KEY FK_5FB39E83C4663E4');
        $this->execute('DROP TABLE rq_users_pages');
    }
}