<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class DossierIntervention extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('px_dossiers_interventions', ['id' => false, 'primary_key' => ['dossier_id', 'intervention_id']]);
        $table->addColumn('dossier_id', 'integer', ['null' => false])
            ->addColumn('intervention_id', 'integer', ['null' => false])
            ->addForeignKey('dossier_id', 'px_dossiers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeign<PERSON>ey('intervention_id', 'px_interventions', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
